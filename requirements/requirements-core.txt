# Core dependencies
numpy<2.0  # Downgraded for compatibility
pandas>=2.1.0

# Basic ML
scipy>=1.12.0
scikit-learn>=1.3.2

# API
fastapi==0.104.1
uvicorn==0.24.0
slowapi==0.1.7
redis==5.0.1
bcrypt==4.0.1
python-dotenv==1.0.0
httpx[http2]==0.25.0

# Utils
pyyaml==6.0.1
loguru==0.7.2
tenacity==8.2.3

# Security
itsdangerous==2.1.2
passlib[bcrypt]==1.7.4
minio==7.2.0

# Authentication
python-jose[cryptography]==3.3.0

# Configuration
pydantic-settings==2.0.3

# Monitoring and Metrics
prometheus-client==0.17.1

# NLP
spacy==3.7.2
datasets==2.15.0

# Message Queue
aiokafka==0.8.1
kafka-python==2.0.2

# MongoDB
pymongo==4.6.0
motor==3.3.1

# Email validation
email-validator==2.1.0.post1

# File uploads
python-multipart==0.0.6

# Async file operations
aiofiles==24.1.0

# Task queue
celery==5.3.4

# Authentication
authlib==1.2.1

# Content Moderation
detoxify==0.5.2

# Security and Authentication
pyotp==2.9.0
qrcode==8.2

# Utilities
nest_asyncio==1.6.0
py-cpuinfo==9.0.0
reportlab==4.4.1
chardet==5.2.0
psutil==5.9.5

langdetect==1.0.9
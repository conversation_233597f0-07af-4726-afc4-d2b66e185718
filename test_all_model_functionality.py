#!/usr/bin/env python3
"""
Complete test script that starts the application and tests all model functionality.
"""
import asyncio
import subprocess
import time
import signal
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger

# Configure clean logging
logger.remove()
logger.add(
    lambda msg: print(msg, end=""),
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    colorize=True
)

class ApplicationTester:
    """Test runner for the complete application."""
    
    def __init__(self):
        self.app_process = None
        
    async def start_application(self) -> bool:
        """Start the FastAPI application."""
        logger.info("🚀 Starting FastAPI application...")
        
        try:
            # Start the optimized startup script
            self.app_process = subprocess.Popen(
                [sys.executable, "optimized_startup.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                cwd=project_root
            )
            
            logger.info(f"📡 Application started with PID: {self.app_process.pid}")
            
            # Wait for the application to start
            logger.info("⏳ Waiting for application to be ready...")
            
            # Check if the process is still running and wait for startup
            for i in range(30):  # Wait up to 30 seconds
                if self.app_process.poll() is not None:
                    # Process has terminated
                    stdout, stderr = self.app_process.communicate()
                    logger.error(f"❌ Application terminated early:")
                    logger.error(f"STDOUT: {stdout}")
                    logger.error(f"STDERR: {stderr}")
                    return False
                
                # Check if the application is responding
                try:
                    import aiohttp
                    async with aiohttp.ClientSession() as session:
                        async with session.get("http://localhost:8000/health", timeout=2) as response:
                            if response.status == 200:
                                logger.info("✅ Application is ready and responding!")
                                return True
                except:
                    pass  # Not ready yet
                
                await asyncio.sleep(1)
                if i % 5 == 0:
                    logger.info(f"⏳ Still waiting... ({i+1}/30 seconds)")
            
            logger.error("❌ Application did not become ready within 30 seconds")
            return False
            
        except Exception as e:
            logger.error(f"❌ Error starting application: {str(e)}")
            return False
    
    def stop_application(self):
        """Stop the FastAPI application."""
        if self.app_process and self.app_process.poll() is None:
            logger.info("🛑 Stopping application...")
            self.app_process.terminate()
            try:
                self.app_process.wait(timeout=10)
                logger.info("✅ Application stopped successfully")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ Force killing application...")
                self.app_process.kill()
                self.app_process.wait()
    
    async def run_model_tests(self) -> bool:
        """Run the comprehensive model tests."""
        logger.info("🧪 Running comprehensive model route tests...")
        
        try:
            # Import and run the model routes tester
            from test_model_routes import ModelRoutesTester
            
            async with ModelRoutesTester() as tester:
                # Authenticate
                auth_success = await tester.authenticate()
                if not auth_success:
                    logger.error("❌ Authentication failed")
                    return False
                
                # Run all tests
                health_results = await tester.test_health_endpoints()
                management_results = await tester.test_model_management_routes()
                models_results = await tester.test_models_routes()
                training_results = await tester.test_training_routes()
                operations_results = await tester.test_specific_model_operations()
                
                # Calculate success rate
                all_results = {
                    **health_results,
                    **management_results,
                    **models_results,
                    **training_results,
                    **operations_results
                }
                
                total_tests = len(all_results)
                passed_tests = sum(1 for result in all_results.values() if result)
                success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
                
                logger.info(f"📊 Test Results: {passed_tests}/{total_tests} passed ({success_rate:.1f}%)")
                
                return success_rate >= 70  # Consider 70% success rate as acceptable
                
        except Exception as e:
            logger.error(f"❌ Error running model tests: {str(e)}")
            return False
    
    async def test_integrated_model_flow(self) -> bool:
        """Test the integrated model flow."""
        logger.info("🔄 Testing integrated model flow...")
        
        try:
            # Run the integrated flow test
            process = await asyncio.create_subprocess_exec(
                sys.executable, "test_integrated_flow.py",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=project_root
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("✅ Integrated model flow test passed")
                return True
            else:
                logger.error(f"❌ Integrated model flow test failed: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing integrated model flow: {str(e)}")
            return False

async def main():
    """Main test function."""
    logger.info("🎯 COMPREHENSIVE MODEL FUNCTIONALITY TEST")
    logger.info("="*60)
    
    tester = ApplicationTester()
    
    # Setup signal handler for graceful shutdown
    def signal_handler(signum, frame):
        logger.info("🛑 Received interrupt signal, shutting down...")
        tester.stop_application()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Step 1: Test integrated model flow (without starting app)
        logger.info("📋 Step 1: Testing integrated model flow...")
        flow_success = await tester.test_integrated_model_flow()
        
        # Step 2: Start the application
        logger.info("\n📋 Step 2: Starting application...")
        app_success = await tester.start_application()
        
        if not app_success:
            logger.error("❌ Cannot proceed with API tests - application failed to start")
            return
        
        # Step 3: Test all model routes
        logger.info("\n📋 Step 3: Testing model API routes...")
        routes_success = await tester.run_model_tests()
        
        # Step 4: Summary
        logger.info("\n" + "="*60)
        logger.info("🏆 FINAL RESULTS")
        logger.info("="*60)
        
        results = {
            "Integrated Model Flow": flow_success,
            "Application Startup": app_success,
            "Model API Routes": routes_success
        }
        
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"   {test_name}: {status}")
        
        overall_success = all(results.values())
        
        if overall_success:
            logger.info("\n🎉 ALL TESTS PASSED! Your model functionality is working perfectly!")
        else:
            logger.info("\n⚠️ Some tests failed. Check the logs above for details.")
        
        logger.info("="*60)
        
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
    finally:
        # Always stop the application
        tester.stop_application()

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Fix missing models by uploading them from HuggingFace cache to MinIO.
"""
import os
import sys
import asyncio
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.model_manager import model_manager
from loguru import logger


async def fix_missing_models():
    """Fix models that exist in HF cache but are missing from MinIO."""
    
    print("🔍 Checking for missing models...")
    
    # Get all models from MongoDB metadata
    try:
        import pymongo
        from pymongo import MongoClient
        
        # Connect to MongoDB
        client = MongoClient('**************************************************************')
        db = client['sim_llm']
        
        # Get all models
        models = list(db.model_metadata.find({}, {
            'model_id': 1, 
            'task': 1, 
            'status': 1, 
            'cache_path': 1,
            'minio_required': 1
        }))
        
        print(f"📊 Found {len(models)} models in MongoDB metadata")
        
        missing_models = []
        fixed_models = []
        
        for model in models:
            model_id = model.get('model_id')
            cache_path = model.get('cache_path')
            minio_required = model.get('minio_required', True)
            
            # Skip models that don't require MinIO
            if not minio_required:
                continue
                
            print(f"\n🔍 Checking model: {model_id}")
            
            # Check if model exists in MinIO
            try:
                result = model_manager.check_model_in_minio(model_id)
                if isinstance(result, tuple):
                    in_minio, file_count = result
                else:
                    in_minio = result
                    file_count = 0
            except Exception as e:
                print(f"❌ Error checking MinIO for {model_id}: {e}")
                in_minio = False
                file_count = 0
            
            if not in_minio:
                print(f"❌ Model {model_id} missing from MinIO")
                missing_models.append(model_id)
                
                # Check if model exists in HuggingFace cache
                hf_cache_path = None
                if cache_path and os.path.exists(cache_path):
                    hf_cache_path = cache_path
                else:
                    # Try standard HF cache location
                    hf_cache_path = os.path.expanduser(f"~/.cache/huggingface/hub/models--{model_id.replace('/', '--')}")
                
                if hf_cache_path and os.path.exists(hf_cache_path):
                    print(f"✅ Found model in HF cache: {hf_cache_path}")
                    
                    # Check if snapshots directory exists
                    snapshots_dir = os.path.join(hf_cache_path, 'snapshots')
                    if os.path.exists(snapshots_dir):
                        # Get the latest snapshot
                        snapshots = [d for d in os.listdir(snapshots_dir) if os.path.isdir(os.path.join(snapshots_dir, d))]
                        if snapshots:
                            latest_snapshot = snapshots[0]  # Use first available snapshot
                            snapshot_path = os.path.join(snapshots_dir, latest_snapshot)
                            
                            print(f"📤 Uploading model to MinIO from: {snapshot_path}")
                            
                            try:
                                # Upload to MinIO
                                success = model_manager.upload_model_to_minio(model_id, snapshot_path)
                                if success:
                                    print(f"✅ Successfully uploaded {model_id} to MinIO")
                                    fixed_models.append(model_id)
                                else:
                                    print(f"❌ Failed to upload {model_id} to MinIO")
                            except Exception as e:
                                print(f"❌ Error uploading {model_id} to MinIO: {e}")
                        else:
                            print(f"❌ No snapshots found in {snapshots_dir}")
                    else:
                        print(f"❌ No snapshots directory found in {hf_cache_path}")
                else:
                    print(f"❌ Model not found in HF cache")
            else:
                print(f"✅ Model {model_id} exists in MinIO ({file_count} files)")
        
        print(f"\n📊 Summary:")
        print(f"   Total models checked: {len(models)}")
        print(f"   Missing from MinIO: {len(missing_models)}")
        print(f"   Successfully fixed: {len(fixed_models)}")
        
        if missing_models:
            print(f"\n❌ Still missing models:")
            for model_id in missing_models:
                if model_id not in fixed_models:
                    print(f"   - {model_id}")
        
        if fixed_models:
            print(f"\n✅ Fixed models:")
            for model_id in fixed_models:
                print(f"   - {model_id}")
                
        client.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


async def verify_model_flow():
    """Verify the complete model flow is working correctly."""
    
    print("\n🔍 Verifying model flow...")
    
    # Test model: distil-whisper/distil-small.en
    test_model = "distil-whisper/distil-small.en"
    
    print(f"\n📋 Testing model flow for: {test_model}")
    
    # 1. Check MongoDB metadata
    metadata = model_manager.get_model_metadata(test_model)
    print(f"1️⃣ MongoDB metadata: {'✅ Found' if metadata else '❌ Missing'}")
    
    # 2. Check MinIO
    try:
        result = model_manager.check_model_in_minio(test_model)
        if isinstance(result, tuple):
            in_minio, file_count = result
        else:
            in_minio = result
            file_count = 0
        print(f"2️⃣ MinIO storage: {'✅ Found' if in_minio else '❌ Missing'} ({file_count} files)")
    except Exception as e:
        print(f"2️⃣ MinIO storage: ❌ Error - {e}")
        in_minio = False
    
    # 3. Check local cache directories
    from app.utils.config import settings
    
    # Check MODEL_CACHE_DIR
    cache_dir = settings.MODEL_CACHE_DIR
    local_path = os.path.join(cache_dir, test_model.replace('/', '--'))
    print(f"3️⃣ Local cache ({cache_dir}): {'✅ Found' if os.path.exists(local_path) else '❌ Missing'}")
    
    # Check HF_HOME
    hf_home = settings.HF_HOME
    hf_path = os.path.join(hf_home, test_model.replace('/', '--'))
    print(f"4️⃣ HF Home ({hf_home}): {'✅ Found' if os.path.exists(hf_path) else '❌ Missing'}")
    
    # Check standard HF cache
    hf_cache = os.path.expanduser(f"~/.cache/huggingface/hub/models--{test_model.replace('/', '--')}")
    print(f"5️⃣ HF Cache: {'✅ Found' if os.path.exists(hf_cache) else '❌ Missing'}")
    
    # 6. Test model loading (if in MinIO)
    if in_minio:
        print(f"6️⃣ Testing model download from MinIO...")
        try:
            # Create target directory
            os.makedirs(local_path, exist_ok=True)
            
            # Download from MinIO
            success = model_manager.download_model_from_minio(test_model, local_path)
            print(f"6️⃣ MinIO download: {'✅ Success' if success else '❌ Failed'}")
            
            if success:
                print(f"   📁 Model downloaded to: {local_path}")
                # List contents
                if os.path.exists(local_path):
                    contents = os.listdir(local_path)
                    print(f"   📄 Files: {contents[:5]}{'...' if len(contents) > 5 else ''}")
        except Exception as e:
            print(f"6️⃣ MinIO download: ❌ Error - {e}")
    
    print(f"\n✅ Model flow verification complete!")


async def main():
    """Main function."""
    print("🚀 Starting model fix and verification process...")
    
    # Fix missing models
    await fix_missing_models()
    
    # Verify model flow
    await verify_model_flow()
    
    print("\n🎉 Process complete!")


if __name__ == "__main__":
    asyncio.run(main())

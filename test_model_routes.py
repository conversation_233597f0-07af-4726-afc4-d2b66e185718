#!/usr/bin/env python3
"""
Comprehensive test script for all model-related routes.
Tests all model management, training, and API endpoints.
"""
import asyncio
import aiohttp
import json
import time
from typing import Dict, Any, List
from loguru import logger

# Configure clean logging
logger.remove()
logger.add(
    lambda msg: print(msg, end=""),
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    colorize=True
)

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

# Test credentials (using default admin user)
TEST_CREDENTIALS = {
    "username": "admin",
    "password": "admin"
}

class ModelRoutesTester:
    """Comprehensive tester for all model routes."""
    
    def __init__(self):
        self.session = None
        self.auth_token = None
        self.headers = {}
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def authenticate(self) -> bool:
        """Authenticate and get access token."""
        try:
            # Try to get token using OAuth2 password flow
            auth_data = {
                "username": TEST_CREDENTIALS["username"],
                "password": TEST_CREDENTIALS["password"]
            }
            
            async with self.session.post(
                f"{API_BASE}/auth/token",
                data=auth_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            ) as response:
                if response.status == 200:
                    token_data = await response.json()
                    self.auth_token = token_data.get("access_token")
                    self.headers = {"Authorization": f"Bearer {self.auth_token}"}
                    logger.info("✅ Authentication successful")
                    return True
                else:
                    logger.error(f"❌ Authentication failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Authentication error: {str(e)}")
            return False
    
    async def test_health_endpoints(self) -> Dict[str, bool]:
        """Test basic health endpoints."""
        logger.info("🔍 Testing health endpoints...")
        results = {}
        
        # Test basic health
        try:
            async with self.session.get(f"{BASE_URL}/health") as response:
                results["health"] = response.status == 200
                if results["health"]:
                    logger.info("✅ /health endpoint working")
                else:
                    logger.error(f"❌ /health endpoint failed: {response.status}")
        except Exception as e:
            results["health"] = False
            logger.error(f"❌ /health endpoint error: {str(e)}")
        
        # Test authenticated status
        try:
            async with self.session.get(f"{API_BASE}/status", headers=self.headers) as response:
                results["status"] = response.status == 200
                if results["status"]:
                    logger.info("✅ /api/v1/status endpoint working")
                else:
                    logger.error(f"❌ /api/v1/status endpoint failed: {response.status}")
        except Exception as e:
            results["status"] = False
            logger.error(f"❌ /api/v1/status endpoint error: {str(e)}")
        
        return results
    
    async def test_model_management_routes(self) -> Dict[str, bool]:
        """Test model management routes."""
        logger.info("🔍 Testing model management routes...")
        results = {}
        
        # Test list models
        try:
            async with self.session.get(f"{API_BASE}/model-management/", headers=self.headers) as response:
                results["list_models"] = response.status == 200
                if results["list_models"]:
                    data = await response.json()
                    logger.info(f"✅ Model management list: {len(data.get('models', []))} models found")
                else:
                    logger.error(f"❌ Model management list failed: {response.status}")
        except Exception as e:
            results["list_models"] = False
            logger.error(f"❌ Model management list error: {str(e)}")
        
        # Test model status table
        try:
            async with self.session.get(f"{API_BASE}/model-manager/status-table", headers=self.headers) as response:
                results["status_table"] = response.status == 200
                if results["status_table"]:
                    logger.info("✅ Model status table working")
                else:
                    logger.error(f"❌ Model status table failed: {response.status}")
        except Exception as e:
            results["status_table"] = False
            logger.error(f"❌ Model status table error: {str(e)}")
        
        # Test model manager list
        try:
            async with self.session.get(f"{API_BASE}/model-manager/list", headers=self.headers) as response:
                results["manager_list"] = response.status == 200
                if results["manager_list"]:
                    data = await response.json()
                    logger.info(f"✅ Model manager list: {len(data.get('models', []))} models found")
                else:
                    logger.error(f"❌ Model manager list failed: {response.status}")
        except Exception as e:
            results["manager_list"] = False
            logger.error(f"❌ Model manager list error: {str(e)}")
        
        return results
    
    async def test_models_routes(self) -> Dict[str, bool]:
        """Test models API routes."""
        logger.info("🔍 Testing models API routes...")
        results = {}
        
        # Test list models by task
        tasks = ["text-generation", "speech-to-text", "image-classification"]
        
        for task in tasks:
            try:
                async with self.session.get(f"{API_BASE}/models/{task}", headers=self.headers) as response:
                    results[f"models_{task}"] = response.status == 200
                    if results[f"models_{task}"]:
                        data = await response.json()
                        logger.info(f"✅ Models for {task}: {len(data)} models found")
                    else:
                        logger.error(f"❌ Models for {task} failed: {response.status}")
            except Exception as e:
                results[f"models_{task}"] = False
                logger.error(f"❌ Models for {task} error: {str(e)}")
        
        # Test general models list
        try:
            async with self.session.get(f"{API_BASE}/models", headers=self.headers) as response:
                results["models_general"] = response.status == 200
                if results["models_general"]:
                    data = await response.json()
                    logger.info(f"✅ General models list: {len(data)} tasks found")
                else:
                    logger.error(f"❌ General models list failed: {response.status}")
        except Exception as e:
            results["models_general"] = False
            logger.error(f"❌ General models list error: {str(e)}")
        
        return results
    
    async def test_training_routes(self) -> Dict[str, bool]:
        """Test model training routes."""
        logger.info("🔍 Testing model training routes...")
        results = {}
        
        # Test list training jobs
        try:
            async with self.session.get(f"{API_BASE}/training/jobs", headers=self.headers) as response:
                results["training_jobs"] = response.status == 200
                if results["training_jobs"]:
                    data = await response.json()
                    logger.info(f"✅ Training jobs list: {len(data)} jobs found")
                else:
                    logger.error(f"❌ Training jobs list failed: {response.status}")
        except Exception as e:
            results["training_jobs"] = False
            logger.error(f"❌ Training jobs list error: {str(e)}")
        
        # Test model training list (from model_training.py)
        try:
            async with self.session.get(f"{API_BASE}/models/", headers=self.headers) as response:
                # This might be the model_training.router endpoint
                results["model_training_list"] = response.status in [200, 404]  # 404 is acceptable if no models
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Model training list working")
                else:
                    logger.info(f"ℹ️ Model training list: {response.status} (may be empty)")
        except Exception as e:
            results["model_training_list"] = False
            logger.error(f"❌ Model training list error: {str(e)}")
        
        return results
    
    async def test_specific_model_operations(self) -> Dict[str, bool]:
        """Test specific model operations."""
        logger.info("🔍 Testing specific model operations...")
        results = {}
        
        # Test model status for a known model
        test_model = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
        
        try:
            async with self.session.get(
                f"{API_BASE}/models/text-generation/{test_model}/status", 
                headers=self.headers
            ) as response:
                results["model_status"] = response.status in [200, 404]
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Model status for {test_model}: {data.get('status', 'unknown')}")
                else:
                    logger.info(f"ℹ️ Model status check: {response.status}")
        except Exception as e:
            results["model_status"] = False
            logger.error(f"❌ Model status error: {str(e)}")
        
        return results

async def main():
    """Main test function."""
    logger.info("🚀 Starting comprehensive model routes testing...")
    
    async with ModelRoutesTester() as tester:
        # Step 1: Authenticate
        auth_success = await tester.authenticate()
        if not auth_success:
            logger.error("❌ Authentication failed, cannot proceed with tests")
            return
        
        # Step 2: Test health endpoints
        health_results = await tester.test_health_endpoints()
        
        # Step 3: Test model management routes
        management_results = await tester.test_model_management_routes()
        
        # Step 4: Test models API routes
        models_results = await tester.test_models_routes()
        
        # Step 5: Test training routes
        training_results = await tester.test_training_routes()
        
        # Step 6: Test specific model operations
        operations_results = await tester.test_specific_model_operations()
        
        # Compile results
        all_results = {
            "Health Endpoints": health_results,
            "Model Management": management_results,
            "Models API": models_results,
            "Training Routes": training_results,
            "Model Operations": operations_results
        }
        
        # Print summary
        logger.info("\n" + "="*80)
        logger.info("📊 MODEL ROUTES TEST SUMMARY")
        logger.info("="*80)
        
        total_tests = 0
        passed_tests = 0
        
        for category, results in all_results.items():
            logger.info(f"\n📋 {category}:")
            for test_name, passed in results.items():
                status = "✅ PASS" if passed else "❌ FAIL"
                logger.info(f"   {test_name}: {status}")
                total_tests += 1
                if passed:
                    passed_tests += 1
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        logger.info(f"\n🏆 Overall Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.info("🎉 Model routes are working well!")
        elif success_rate >= 60:
            logger.info("⚠️ Most model routes are working, some issues detected")
        else:
            logger.info("❌ Significant issues with model routes detected")
        
        logger.info("="*80)

if __name__ == "__main__":
    asyncio.run(main())

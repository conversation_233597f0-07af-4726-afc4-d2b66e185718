"""
Model Flow Integration for Startup Process
Integrates the complete model flow into the application startup.
"""
import os
import asyncio
from typing import List, Dict, Any
from loguru import logger

from app.services.integrated_model_flow import IntegratedModelFlow
from app.utils.config import get_settings


# Essential models that should be processed during startup
ESSENTIAL_MODELS = [
    {
        "model_id": "distil-whisper/distil-small.en",
        "task": "automatic-speech-recognition",
        "priority": 9,
        "description": "Small Whisper model for speech recognition"
    },
    {
        "model_id": "sentence-transformers/all-MiniLM-L6-v2",
        "task": "sentence-similarity", 
        "priority": 8,
        "description": "Sentence embeddings for semantic search"
    },
    {
        "model_id": "distilbert-base-uncased",
        "task": "text-classification",
        "priority": 7,
        "description": "Text classification model"
    },
    {
        "model_id": "openai/whisper-tiny",
        "task": "automatic-speech-recognition",
        "priority": 6,
        "description": "Tiny Whisper model for speech recognition"
    },
    {
        "model_id": "TinyLlama/TinyLlama-1.1B-Chat-v1.0",
        "task": "text-generation",
        "priority": 8,
        "description": "Small chat model for text generation"
    }
]


class ModelFlowStartupIntegration:
    """Integrates the model flow into application startup."""
    
    def __init__(self, model_manager):
        """Initialize with model manager."""
        self.model_manager = model_manager
        self.integrated_flow = IntegratedModelFlow(model_manager)
        self.settings = get_settings()
        
    async def setup_environment(self):
        """Setup environment variables for local model storage."""
        logger.info("🔧 Setting up model environment...")
        
        # Ensure environment variables are set
        env_vars = {
            'HF_HOME': self.settings.HF_HOME,
            'TRANSFORMERS_CACHE': self.settings.TRANSFORMERS_CACHE,
            'HF_DATASETS_CACHE': self.settings.HF_DATASETS_CACHE,
            'HUGGINGFACE_HUB_CACHE': self.settings.HUGGINGFACE_HUB_CACHE,
            'HF_HUB_CACHE': self.settings.HF_HUB_CACHE,
            'HF_ASSETS_CACHE': self.settings.HF_ASSETS_CACHE,
        }
        
        for var, value in env_vars.items():
            if var not in os.environ:
                os.environ[var] = value
                logger.debug(f"Set {var}={value}")
        
        # Create directory structure
        directories = [
            self.settings.HF_HOME,
            os.path.join(self.settings.HF_HOME, 'hub'),
            os.path.join(self.settings.HF_HOME, 'datasets'),
            os.path.join(self.settings.HF_HOME, 'assets'),
            self.settings.MODEL_CACHE_DIR,
            self.settings.MODELS_DIR,
            self.settings.UPLOAD_DIR,
            self.settings.TRAINING_DATA_DIR
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            
        logger.info("✅ Model environment setup complete")
    
    async def process_essential_models(self) -> Dict[str, Any]:
        """Process essential models through the integrated flow."""
        logger.info("🚀 Processing essential models through integrated flow...")
        
        # Sort by priority (higher first)
        sorted_models = sorted(ESSENTIAL_MODELS, key=lambda x: x.get('priority', 0), reverse=True)
        
        results = await self.integrated_flow.process_model_list(sorted_models)
        
        # Log summary
        logger.info(f"📊 Essential models processing summary:")
        logger.info(f"   Total: {results['total_models']}")
        logger.info(f"   Successful: {results['successful']}")
        logger.info(f"   Partial: {results['partial']}")
        logger.info(f"   Failed: {results['failed']}")
        
        # Log details for failed models
        for model_id, result in results['details'].items():
            if result['status'] == 'failed':
                error = result.get('error', 'Unknown error')
                logger.warning(f"❌ {model_id}: {error}")
            elif result['status'] == 'completed':
                logger.info(f"✅ {model_id}: Ready at {result.get('local_path', 'N/A')}")
        
        return results
    
    async def verify_model_endpoints(self) -> bool:
        """Verify that API endpoints can access the processed models."""
        logger.info("🔍 Verifying model endpoints...")
        
        verification_results = []
        
        for model_info in ESSENTIAL_MODELS:
            model_id = model_info['model_id']
            
            try:
                # Check if model is accessible
                safe_model_id = model_id.replace('/', '--')
                local_path = os.path.join(self.settings.MODEL_CACHE_DIR, safe_model_id)
                
                if os.path.exists(local_path) and os.listdir(local_path):
                    logger.info(f"✅ {model_id}: Accessible at {local_path}")
                    verification_results.append(True)
                else:
                    logger.warning(f"❌ {model_id}: Not accessible at {local_path}")
                    verification_results.append(False)
                    
            except Exception as e:
                logger.error(f"❌ {model_id}: Verification error - {str(e)}")
                verification_results.append(False)
        
        success_rate = sum(verification_results) / len(verification_results) * 100
        logger.info(f"📊 Model endpoint verification: {success_rate:.1f}% success rate")
        
        return success_rate >= 80  # Consider successful if 80% or more models are accessible
    
    async def migrate_existing_models(self) -> Dict[str, Any]:
        """Migrate any existing models from old cache locations."""
        logger.info("🔄 Checking for models to migrate...")
        
        migration_results = {
            "migrated": 0,
            "skipped": 0,
            "failed": 0,
            "details": {}
        }
        
        # Check for models in old HuggingFace cache
        old_cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
        
        if os.path.exists(old_cache_dir):
            try:
                # Find model directories
                model_dirs = [d for d in os.listdir(old_cache_dir) if d.startswith('models--')]
                
                logger.info(f"Found {len(model_dirs)} models in old cache directory")
                
                for model_dir in model_dirs[:5]:  # Limit to first 5 to avoid overwhelming
                    # Extract model ID
                    model_id = model_dir.replace('models--', '').replace('--', '/')
                    
                    # Check if this is one of our essential models
                    if any(m['model_id'] == model_id for m in ESSENTIAL_MODELS):
                        source_path = os.path.join(old_cache_dir, model_dir)
                        
                        # Process through integrated flow
                        result = await self.integrated_flow.process_model(model_id)
                        migration_results['details'][model_id] = result
                        
                        if result['status'] == 'completed':
                            migration_results['migrated'] += 1
                            logger.info(f"✅ Migrated {model_id}")
                        else:
                            migration_results['failed'] += 1
                            logger.warning(f"❌ Failed to migrate {model_id}")
                    else:
                        migration_results['skipped'] += 1
                        
            except Exception as e:
                logger.error(f"Error during migration: {str(e)}")
        
        logger.info(f"📊 Migration summary: "
                   f"{migration_results['migrated']} migrated, "
                   f"{migration_results['skipped']} skipped, "
                   f"{migration_results['failed']} failed")
        
        return migration_results
    
    async def run_complete_integration(self) -> Dict[str, Any]:
        """Run the complete model flow integration."""
        logger.info("🚀 Starting complete model flow integration...")
        
        integration_results = {
            "environment_setup": False,
            "essential_models": {},
            "migration": {},
            "verification": False,
            "overall_success": False
        }
        
        try:
            # Step 1: Setup environment
            await self.setup_environment()
            integration_results["environment_setup"] = True
            
            # Step 2: Migrate existing models
            migration_results = await self.migrate_existing_models()
            integration_results["migration"] = migration_results
            
            # Step 3: Process essential models
            essential_results = await self.process_essential_models()
            integration_results["essential_models"] = essential_results
            
            # Step 4: Verify endpoints
            verification_success = await self.verify_model_endpoints()
            integration_results["verification"] = verification_success
            
            # Determine overall success
            essential_success_rate = essential_results['successful'] / essential_results['total_models']
            integration_results["overall_success"] = (
                integration_results["environment_setup"] and
                essential_success_rate >= 0.8 and  # 80% of essential models successful
                integration_results["verification"]
            )
            
            if integration_results["overall_success"]:
                logger.info("🎉 Model flow integration completed successfully!")
            else:
                logger.warning("⚠️ Model flow integration completed with some issues")
            
            return integration_results
            
        except Exception as e:
            logger.error(f"❌ Error in model flow integration: {str(e)}")
            integration_results["error"] = str(e)
            return integration_results


async def integrate_model_flow_on_startup(model_manager) -> Dict[str, Any]:
    """
    Main function to integrate model flow on application startup.
    
    Args:
        model_manager: The model manager instance
        
    Returns:
        dict: Integration results
    """
    integration = ModelFlowStartupIntegration(model_manager)
    return await integration.run_complete_integration()


# For backwards compatibility
async def setup_integrated_model_flow(model_manager):
    """Setup the integrated model flow (backwards compatibility)."""
    return await integrate_model_flow_on_startup(model_manager)

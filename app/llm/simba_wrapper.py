"""
SimbaAI LLM Wrapper.

This module provides a wrapper for SimbaAI's local language models.
"""
from typing import Dict, List, Optional, Any, Generator
import tiktoken
from loguru import logger
import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline

class SimbaWrapper:
    """
    Wrapper for SimbaAI's local language models.
    """
    
    def __init__(self):
        """
        Initialize the SimbaAI wrapper.
        """
        self.available = True
        self.models = {}
        self.tokenizers = {}
        
        # Initialize with basic tokenizers for token counting
        try:
            self.tokenizers["default"] = tiktoken.get_encoding("cl100k_base")
        except Exception as e:
            logger.warning(f"Could not load tokenizers: {str(e)}")
            
        # Try to load model manager
        try:
            from app.services.model_manager import ModelManager
            self.model_manager = ModelManager()
            logger.info("ModelManager initialized in SimbaWrapper")
        except Exception as e:
            logger.warning(f"Could not initialize ModelManager: {str(e)}")
            self.model_manager = None
    
    def count_tokens(self, text: str, model: str = "default") -> int:
        """
        Count the number of tokens in a text.
        
        Args:
            text: The text to count tokens for
            model: The model to use for counting
            
        Returns:
            The number of tokens
        """
        tokenizer = self.tokenizers.get(model, self.tokenizers.get("default"))
        if not tokenizer:
            # Fallback to cl100k_base for unknown models
            try:
                tokenizer = tiktoken.get_encoding("cl100k_base")
            except:
                # If tiktoken fails, estimate tokens (rough approximation)
                return len(text.split()) * 1.3
        
        return len(tokenizer.encode(text))
    
    def _get_model(self, model_id: str):
        """
        Get or load a model.
        
        Args:
            model_id: The model ID or alias to load
            
        Returns:
            The loaded model or None if loading fails
        """
        # Check if model is already loaded
        if model_id in self.models:
            return self.models[model_id]
            
        # Try to load from model manager
        if self.model_manager:
            try:
                # Map common aliases to tasks
                task_mapping = {
                    "tinyllama": "chat",
                    "gpt2": "text-generation",
                    "distilgpt2": "text-generation",
                    "all-minilm": "sentence-embedding",
                    "t5": "text2text",
                    "bert": "text-classification",
                    "distilbert": "text-classification",
                    "vit": "image-classification",
                    "yolos": "object-detection"
                }
                
                # Determine task type based on model name or alias
                task = "text-generation"  # Default task
                
                # Check if we have a direct task mapping for this model alias
                for alias, mapped_task in task_mapping.items():
                    if alias in model_id.lower():
                        task = mapped_task
                        break
                
                # Special handling for specific model types
                if "embedding" in model_id.lower():
                    task = "sentence-embedding"
                elif "classification" in model_id.lower():
                    task = "text-classification"
                elif "summarization" in model_id.lower():
                    task = "summarization"
                elif "qa" in model_id.lower() or "question" in model_id.lower():
                    task = "question-answering"
                
                logger.info(f"Getting model {model_id} for task {task} from model manager")
                
                # Try to get model from model manager
                model = self.model_manager.get_model(model_id, task=task)
                if model:
                    logger.info(f"Successfully retrieved model {model_id} from model manager")
                    self.models[model_id] = model
                    return model
                
                # If not found by ID, try to get by alias if it looks like an alias
                if "/" not in model_id:  # Likely an alias, not a HF model ID
                    # Try to find the model ID from the config
                    from app.config.models import ESSENTIAL_MODELS, DEFAULT_MODELS
                    
                    # Check if it's a default model for a task
                    for task_name, default_model in DEFAULT_MODELS.items():
                        if model_id.lower() in default_model.lower():
                            logger.info(f"Found default model {default_model} for task {task_name}")
                            model = self.model_manager.get_model(default_model, task=task_name)
                            if model:
                                self.models[model_id] = model
                                return model
                    
                    # Check in essential models
                    for task_name, models_list in ESSENTIAL_MODELS.items():
                        for model_info in models_list:
                            if model_id.lower() == model_info.get("alias", "").lower():
                                real_model_id = model_info.get("model_id")
                                logger.info(f"Found model ID {real_model_id} for alias {model_id}")
                                model = self.model_manager.get_model(real_model_id, task=task_name)
                                if model:
                                    self.models[model_id] = model
                                    return model
                
                # If still not loaded, try to load it with the appropriate method
                logger.info(f"Model {model_id} not found in cache, attempting to load")
                
                # Use the appropriate loading method based on the task
                if task == "chat" or task == "text-generation":
                    model = self.model_manager.load_text_generation_model(model_id)
                elif task == "sentence-embedding":
                    model = self.model_manager.load_sentence_transformer(model_id)
                elif task == "text-classification":
                    model = self.model_manager.load_text_classification_model(model_id)
                elif task == "question-answering":
                    model = self.model_manager.load_text_classification_model(model_id)
                elif task == "summarization":
                    model = self.model_manager.load_text_generation_model(model_id)
                else:
                    # Default to text generation for unknown tasks
                    model = self.model_manager.load_text_generation_model(model_id)
                
                if model:
                    logger.info(f"Successfully loaded model {model_id}")
                    self.models[model_id] = model
                    return model
                    
            except Exception as e:
                logger.warning(f"Failed to load model {model_id} from model manager: {str(e)}")
        
        # Fallback: Try to use a default model from the local cache
        try:
            logger.warning(f"Model {model_id} not available, falling back to default model")
            
            # Try to get the default chat model (TinyLlama)
            from app.config.models import DEFAULT_MODELS
            default_model_id = DEFAULT_MODELS.get("chat", "TinyLlama/TinyLlama-1.1B-Chat-v1.0")
            
            if self.model_manager:
                default_model = self.model_manager.get_model(default_model_id, task="chat")
                if default_model:
                    logger.info(f"Using default model {default_model_id} instead of {model_id}")
                    self.models[model_id] = default_model  # Cache under requested name for future use
                    return default_model
            
            # If all else fails, try loading directly with transformers as a last resort
            logger.warning(f"Attempting to load default model directly with transformers")
            import os
            from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
            
            # Use the local model path from settings
            from app.utils.config import get_settings
            settings = get_settings()
            local_model_path = settings.LOCAL_MODEL_PATH
            
            # Try to find a suitable model in the local model path
            if os.path.exists(local_model_path):
                for model_dir in os.listdir(local_model_path):
                    if "tinyllama" in model_dir.lower() or "llama" in model_dir.lower():
                        full_path = os.path.join(local_model_path, model_dir)
                        logger.info(f"Found local model at {full_path}")
                        
                        tokenizer = AutoTokenizer.from_pretrained(full_path)
                        model = AutoModelForCausalLM.from_pretrained(full_path)
                        pipe = pipeline("text-generation", model=model, tokenizer=tokenizer)
                        self.models[model_id] = pipe
                        return pipe
            
            # If we still can't find a model, return None
            logger.error(f"Could not find any suitable model for {model_id}")
            return None
            
        except Exception as e:
            logger.error(f"All attempts to load a model failed: {str(e)}")
            return None
    
    def generate(
        self,
        prompt: str,
        model: str = "tinyllama",  # Use the alias instead of direct HF model ID
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Generate text using SimbaAI's local models.
        
        Args:
            prompt: The prompt to generate from
            model: The model to use
            system_prompt: Optional system prompt
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            stop: Stop sequences
            user_id: User ID for tracking
            
        Returns:
            Dict containing the generated text and metadata
        """
        # Prepare full prompt with system prompt if provided
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\n{prompt}"
        
        # Count input tokens
        input_tokens = self.count_tokens(full_prompt)
        
        # Get the model
        model_obj = self._get_model(model)
        
        if not model_obj:
            logger.warning(f"Model {model} not available. Using fallback response.")
            fallback_text = "I'm sorry, but the requested model is not available. Please try a different model."
            output_tokens = self.count_tokens(fallback_text)
            return {
                "text": fallback_text,
                "tokens_input": input_tokens,
                "tokens_output": output_tokens,
                "tokens_total": input_tokens + output_tokens,
                "finish_reason": "stop"
            }
        
        # Generate text
        try:
            # Handle different model types
            if hasattr(model_obj, "generate") and callable(model_obj.generate):
                # Model has a generate method (like HuggingFace models)
                generation_kwargs = {
                    "max_length": max_tokens + input_tokens,
                    "temperature": temperature,
                    "top_p": top_p,
                    "do_sample": temperature > 0,
                }
                
                # Add stop sequences if provided
                if stop:
                    generation_kwargs["stop_strings"] = stop
                
                # Generate
                result = model_obj.generate(full_prompt, **generation_kwargs)
                
                # Extract text from result
                if isinstance(result, str):
                    text = result
                elif isinstance(result, dict) and "generated_text" in result:
                    text = result["generated_text"]
                elif isinstance(result, list) and len(result) > 0:
                    if isinstance(result[0], str):
                        text = result[0]
                    elif isinstance(result[0], dict) and "generated_text" in result[0]:
                        text = result[0]["generated_text"]
                    else:
                        text = str(result[0])
                else:
                    text = str(result)
                    
                # Remove the prompt from the generated text if it's included
                if text.startswith(full_prompt):
                    text = text[len(full_prompt):].lstrip()
            
            elif hasattr(model_obj, "__call__") and callable(model_obj.__call__):
                # Model is a pipeline or callable
                result = model_obj(
                    full_prompt,
                    max_length=max_tokens + input_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    do_sample=temperature > 0,
                    num_return_sequences=1
                )
                
                # Extract text from result
                if isinstance(result, list) and len(result) > 0:
                    if isinstance(result[0], dict) and "generated_text" in result[0]:
                        text = result[0]["generated_text"]
                    else:
                        text = str(result[0])
                else:
                    text = str(result)
                
                # Remove the prompt from the generated text if it's included
                if text.startswith(full_prompt):
                    text = text[len(full_prompt):].lstrip()
            
            else:
                # Unknown model type
                logger.warning(f"Unknown model type: {type(model_obj)}")
                text = "Model type not supported for generation."
            
            # Count output tokens
            output_tokens = self.count_tokens(text)
            
            return {
                "text": text,
                "tokens_input": input_tokens,
                "tokens_output": output_tokens,
                "tokens_total": input_tokens + output_tokens,
                "finish_reason": "stop"
            }
            
        except Exception as e:
            logger.error(f"Error generating text with model {model}: {str(e)}")
            fallback_text = f"I'm sorry, but there was an error generating text: {str(e)}"
            output_tokens = self.count_tokens(fallback_text)
            return {
                "text": fallback_text,
                "tokens_input": input_tokens,
                "tokens_output": output_tokens,
                "tokens_total": input_tokens + output_tokens,
                "finish_reason": "error"
            }
    
    def generate_streaming(
        self,
        prompt: str,
        model: str = "tinyllama",  # Use the alias instead of direct HF model ID
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        user_id: Optional[str] = None,
    ) -> Generator[Dict[str, Any], None, None]:
        """
        Generate text with streaming using SimbaAI's local models.
        
        Args:
            prompt: The prompt to generate from
            model: The model to use
            system_prompt: Optional system prompt
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            stop: Stop sequences
            user_id: User ID for tracking
            
        Returns:
            Generator yielding text chunks
        """
        # Prepare full prompt with system prompt if provided
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\n{prompt}"
        
        # Get the model
        model_obj = self._get_model(model)
        
        if not model_obj:
            logger.warning(f"Model {model} not available for streaming. Using fallback response.")
            fallback_chunks = [
                "I'm ", "sorry, ", "but ", "the ", "requested ", "model ", "is ", "not ", 
                "available. ", "Please ", "try ", "a ", "different ", "model."
            ]
            
            # Yield each chunk
            for chunk in fallback_chunks:
                yield {
                    "text": chunk,
                    "tokens": 1,  # Approximate
                    "finish_reason": None
                }
                
            # Final chunk with finish reason
            yield {
                "text": "",
                "tokens": 0,
                "finish_reason": "stop"
            }
            return
        
        # Generate text with streaming
        try:
            # Check if model supports streaming
            if hasattr(model_obj, "generate_stream") and callable(model_obj.generate_stream):
                # Model has a streaming method
                generation_kwargs = {
                    "max_length": max_tokens + self.count_tokens(full_prompt),
                    "temperature": temperature,
                    "top_p": top_p,
                    "do_sample": temperature > 0,
                }
                
                # Add stop sequences if provided
                if stop:
                    generation_kwargs["stop_strings"] = stop
                
                # Generate with streaming
                for chunk in model_obj.generate_stream(full_prompt, **generation_kwargs):
                    if isinstance(chunk, str):
                        text = chunk
                    elif isinstance(chunk, dict) and "generated_text" in chunk:
                        text = chunk["generated_text"]
                    else:
                        text = str(chunk)
                    
                    yield {
                        "text": text,
                        "tokens": 1,  # Approximate
                        "finish_reason": None
                    }
            else:
                # Model doesn't support streaming, generate full response and chunk it
                result = self.generate(
                    prompt=prompt,
                    model=model,
                    system_prompt=system_prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    top_p=top_p,
                    frequency_penalty=frequency_penalty,
                    presence_penalty=presence_penalty,
                    stop=stop,
                    user_id=user_id
                )
                
                # Get the generated text
                text = result["text"]
                
                # Split into words for streaming simulation
                words = text.split()
                
                # Yield each word
                for word in words:
                    yield {
                        "text": word + " ",
                        "tokens": 1,  # Approximate
                        "finish_reason": None
                    }
            
            # Final chunk with finish reason
            yield {
                "text": "",
                "tokens": 0,
                "finish_reason": "stop"
            }
            
        except Exception as e:
            logger.error(f"Error generating streaming text with model {model}: {str(e)}")
            fallback_chunks = [
                "I'm ", "sorry, ", "but ", "there ", "was ", "an ", "error ", 
                "generating ", "text: ", str(e)
            ]
            
            # Yield each chunk
            for chunk in fallback_chunks:
                yield {
                    "text": chunk,
                    "tokens": 1,  # Approximate
                    "finish_reason": None
                }
                
            # Final chunk with finish reason
            yield {
                "text": "",
                "tokens": 0,
                "finish_reason": "error"
            }
    
    async def generate_async(
        self,
        prompt: str,
        model: str = "tinyllama",  # Use the alias instead of direct HF model ID
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Generate text asynchronously using SimbaAI's local models.
        
        Args:
            prompt: The prompt to generate from
            model: The model to use
            system_prompt: Optional system prompt
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            stop: Stop sequences
            user_id: User ID for tracking
            
        Returns:
            Dict containing the generated text and metadata
        """
        # Run in a separate thread to avoid blocking the event loop
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: self.generate(
                prompt=prompt,
                model=model,
                system_prompt=system_prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p,
                frequency_penalty=frequency_penalty,
                presence_penalty=presence_penalty,
                stop=stop,
                user_id=user_id
            )
        )
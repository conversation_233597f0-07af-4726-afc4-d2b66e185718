"""
Local model wrapper for Hugging Face transformers models.
"""
from typing import Dict, List, Optional, Any, Generator
import os
import time
import warnings
import torch
from loguru import logger

from app.utils.config import settings
from app.utils.progress_bar import ProgressBar

# Suppress specific PyTorch warnings
warnings.filterwarnings("ignore", message=".*copying from a non-meta parameter in the checkpoint to a meta parameter.*")
warnings.filterwarnings("ignore", message=".*Some weights of the model checkpoint.*")
warnings.filterwarnings("ignore", message=".*The tokenizer class you load from this checkpoint is not the same.*")

# Try to import the required libraries, but provide fallbacks if they're not available
try:
    # Disable transformers warnings
    os.environ["TRANSFORMERS_VERBOSITY"] = "error"

    import transformers
    from transformers import AutoModelForCausalLM, AutoTokenizer

    # Disable quantization to avoid bitsandbytes issues
    logger.info("Quantization disabled to avoid bitsandbytes issues")
    QUANTIZATION_AVAILABLE = False

    # Disable transformers warnings
    transformers.logging.set_verbosity_error()

    LOCAL_MODELS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Transformers library not available: {str(e)}. Local models will be disabled.")
    LOCAL_MODELS_AVAILABLE = False
    QUANTIZATION_AVAILABLE = False

class LocalModelWrapper:
    """
    Wrapper for local Hugging Face models.
    """

    def __init__(self, model=None):
        """
        Initialize the local model wrapper.

        Args:
            model: Optional pre-loaded model from model_manager
        """
        self.model = model
        self.tokenizer = None
        self.pipeline = None
        self.is_available = False
        self.using_pipeline = False

        # If a model was provided, set up the wrapper with it
        if model is not None:
            try:
                # Check if the model has a tokenizer attribute
                if hasattr(model, 'tokenizer'):
                    self.tokenizer = model.tokenizer

                # Set up the pipeline
                if hasattr(model, 'generate'):
                    self.pipeline = model
                    self.using_pipeline = True

                # If it's our SimpleLLM, use it directly
                if model.__class__.__name__ == 'SimpleLLM':
                    self.model = model
                    self.tokenizer = model.tokenizer
                    self.pipeline = model
                    self.using_pipeline = True
                    logger.info("Using SimpleLLM as the local model")

                # If it's a dictionary with model and tokenizer (from model_manager)
                if isinstance(model, dict) and 'model' in model and 'tokenizer' in model:
                    self.model = model['model']
                    self.tokenizer = model['tokenizer']
                    self.pipeline = model.get('pipeline', self.model)
                    self.using_pipeline = True
                    logger.info("Using preloaded model from model_manager")

                # Mark as available
                self.is_available = True
                logger.info("Using pre-loaded model from model manager")
                return
            except Exception as e:
                logger.warning(f"Error setting up pre-loaded model: {str(e)}")
                # Continue with normal initialization

        # Check if local models are enabled
        if not getattr(settings, "USE_LOCAL_MODELS", False):
            logger.info("Local models are disabled in settings")
            return

        # Check if transformers is available
        if not LOCAL_MODELS_AVAILABLE:
            logger.warning("Transformers library not available, local models disabled")
            return

        try:
            # Get model configuration from settings
            hf_home = getattr(settings, "HF_HOME", "./data/model_cache")
            model_path = getattr(settings, "LOCAL_MODEL_PATH", f"{hf_home}/local_models/tinyllama")
            model_name = getattr(settings, "LOCAL_MODEL_NAME", "TinyLlama/TinyLlama-1.1B-Chat-v1.0")
            device = getattr(settings, "LOCAL_MODEL_DEVICE", "cpu")
            force_cpu = getattr(settings, "LOCAL_MODEL_FORCE_CPU", False)

            # Ensure model path exists
            os.makedirs(os.path.dirname(model_path), exist_ok=True)

            # Force CPU if specified
            if force_cpu:
                logger.info("Forcing CPU usage for local model")
                device = "cpu"
                # Disable GPU in PyTorch
                os.environ["CUDA_VISIBLE_DEVICES"] = ""
                os.environ["PYTORCH_MPS_HIGH_WATERMARK_RATIO"] = "0.0"  # Disable MPS memory limit

            # Force no quantization due to bitsandbytes issues
            quantization = "none"
            logger.info("Using full precision for local model (quantization disabled)")

            # Configure quantization if enabled and available
            quantization_config = None

            # Get Hugging Face token if available
            hf_token = getattr(settings, "HUGGINGFACE_TOKEN", None)
            if hf_token:
                logger.info("Using Hugging Face token for model access")

            # Check if model exists locally, otherwise download from Hugging Face
            if os.path.exists(model_path) and os.listdir(model_path):
                # Create a progress bar for tokenizer loading with cyan color scheme
                with ProgressBar(total=100, desc="Loading tokenizer", spinner=True, color_scheme="cyan") as pbar:
                    logger.info(f"Loading local model from {model_path}")
                    pbar.update(10, desc="Initializing tokenizer")

                    self.tokenizer = AutoTokenizer.from_pretrained(
                        model_path,
                        token=hf_token
                    )
                    pbar.update(100, desc="Tokenizer loaded successfully")

                # Create a progress bar for model loading with blue color scheme
                with ProgressBar(total=100, desc="Loading language model", spinner=True, color_scheme="blue") as pbar:
                    # Load model with quantization if specified
                    # Try different loading strategies
                    try:
                        # First attempt: Use accelerate with auto device mapping
                        pbar.update(10, desc="Preparing model configuration")
                        model_kwargs = {
                            "device_map": "auto",
                            "torch_dtype": torch.float16 if device == "cuda" else torch.float32,
                            "low_cpu_mem_usage": True,
                            "token": hf_token
                        }

                        if quantization_config:
                            model_kwargs["quantization_config"] = quantization_config
                            pbar.update(20, desc="Applying quantization configuration")
                        else:
                            pbar.update(20, desc="Using full precision")

                        # Temporarily disable warnings during model loading
                        with warnings.catch_warnings():
                            warnings.filterwarnings("ignore", category=UserWarning)
                            pbar.update(30, desc="Loading model with accelerate (this may take a while)")
                            logger.info("Attempting to load model with accelerate and auto device mapping")
                            self.model = AutoModelForCausalLM.from_pretrained(
                                model_path,
                                **model_kwargs
                            )
                            pbar.update(100, desc="Model loaded successfully")
                    except Exception as e1:
                        pbar.update(40, desc="First loading attempt failed, trying alternative method")
                        logger.warning(f"First loading attempt failed: {str(e1)}")
                        try:
                            # Second attempt: Use CPU only with offload
                            pbar.update(50, desc="Preparing CPU-only configuration")
                            model_kwargs = {
                                "device_map": {"": "cpu"},
                                "torch_dtype": torch.float32,
                                "low_cpu_mem_usage": True,
                                "offload_folder": "offload",
                                "token": hf_token
                            }

                            # Temporarily disable warnings during model loading
                            with warnings.catch_warnings():
                                warnings.filterwarnings("ignore", category=UserWarning)
                                pbar.update(60, desc="Loading model with CPU offload (this may take a while)")
                                logger.info("Attempting to load model with CPU only")
                                self.model = AutoModelForCausalLM.from_pretrained(
                                    model_path,
                                    **model_kwargs
                                )
                                pbar.update(100, desc="Model loaded successfully with CPU offload")
                        except Exception as e2:
                            pbar.update(70, desc="Second loading attempt failed, trying pipeline API")
                            logger.warning(f"Second loading attempt failed: {str(e2)}")
                            # Third attempt: Use pipeline instead of direct model loading
                            try:
                                from transformers import pipeline
                                pbar.update(80, desc="Loading model with pipeline API")
                                logger.info("Attempting to load model using pipeline API")
                                self.model = pipeline(
                                    "text-generation",
                                    model=model_path,
                                    tokenizer=self.tokenizer,
                                    token=hf_token
                                )
                                self.using_pipeline = True
                                pbar.update(100, desc="Model loaded successfully with pipeline API")
                            except Exception as e3:
                                pbar.update(90, desc="All loading attempts failed")
                                logger.error(f"All model loading attempts failed. Last error: {str(e3)}")
                                raise
            else:
                # Create a progress bar for model download with magenta color scheme
                with ProgressBar(total=100, desc=f"Downloading model {model_name}", spinner=True, color_scheme="magenta") as pbar:
                    logger.info(f"Downloading model {model_name} from Hugging Face")
                    # Create directory if it doesn't exist
                    os.makedirs(model_path, exist_ok=True)
                    pbar.update(10, desc="Created model directory")

                    # Get Hugging Face token if available
                    hf_token = getattr(settings, "HUGGINGFACE_TOKEN", None)
                    if hf_token:
                        logger.info("Using Hugging Face token for model download")
                        pbar.update(15, desc="Using Hugging Face token for authentication")
                    else:
                        pbar.update(15, desc="No authentication token provided")

                    # Download tokenizer
                    pbar.update(20, desc="Downloading tokenizer")
                    self.tokenizer = AutoTokenizer.from_pretrained(
                        model_name,
                        token=hf_token
                    )
                    pbar.update(30, desc="Tokenizer downloaded successfully")

                    # Download model with quantization if specified
                    pbar.update(35, desc="Preparing model configuration")
                    model_kwargs = {
                        "device_map": "auto" if device == "cuda" else "cpu",
                        "torch_dtype": torch.float16 if device == "cuda" else torch.float32,
                        "low_cpu_mem_usage": True,
                        "token": hf_token
                    }

                    if quantization_config:
                        model_kwargs["quantization_config"] = quantization_config
                        pbar.update(40, desc="Applying quantization configuration")
                    else:
                        pbar.update(40, desc="Using full precision")

                    # Temporarily disable warnings during model loading
                    pbar.update(45, desc="Downloading model (this may take a while)")
                    with warnings.catch_warnings():
                        warnings.filterwarnings("ignore", category=UserWarning)
                        self.model = AutoModelForCausalLM.from_pretrained(
                            model_name,
                            **model_kwargs
                        )
                    pbar.update(80, desc="Model downloaded successfully")

                    # Save tokenizer locally (model is too large to save with quantization)
                    pbar.update(85, desc="Saving tokenizer to local storage")
                    self.tokenizer.save_pretrained(model_path)
                    pbar.update(90, desc="Tokenizer saved successfully")

                    # Only save model if not using quantization
                    if not quantization_config:
                        pbar.update(95, desc="Saving model to local storage (this may take a while)")
                        logger.info("Saving model to local storage")
                        self.model.save_pretrained(model_path)
                        pbar.update(100, desc="Model saved successfully")
                    else:
                        logger.info("Skipping model saving due to quantization")
                        pbar.update(100, desc="Download complete (model not saved due to quantization)")

            # Create pipeline with progress bar using green color scheme
            with ProgressBar(total=100, desc="Creating text generation pipeline", spinner=True, color_scheme="green") as pbar:
                pbar.update(20, desc="Initializing pipeline")
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", category=UserWarning)
                    pbar.update(50, desc="Configuring pipeline components")
                    self.pipeline = transformers.pipeline(
                        "text-generation",
                        model=self.model,
                        tokenizer=self.tokenizer,
                        device="cpu"  # Always use CPU for pipeline
                    )
                    pbar.update(100, desc="Pipeline created successfully")

            self.is_available = True
            logger.info(f"Local model initialized successfully: {model_name}")
        except Exception as e:
            logger.error(f"Error initializing local model: {str(e)}")
            self.is_available = False

    def count_tokens(self, text: str) -> int:
        """
        Count the number of tokens in a text.

        Args:
            text: The text to count tokens for

        Returns:
            Number of tokens
        """
        if not self.is_available:
            return len(text.split())  # Fallback to word count

        try:
            # Check if it's our SimpleLLM's tokenizer
            if hasattr(self.tokenizer, '__class__') and self.tokenizer.__class__.__name__ == 'SimpleTokenizer':
                return len(self.tokenizer.encode(text))

            # Standard tokenizer
            tokens = self.tokenizer.encode(text)
            return len(tokens)
        except Exception as e:
            logger.warning(f"Error counting tokens: {str(e)}")
            return len(text.split())  # Fallback to word count

    def generate(
        self,
        prompt: str,
        model: str = "mistral-7b-instruct",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Generate text using the local model.

        Args:
            prompt: The prompt to generate from
            model: The model to use (ignored, using loaded model)
            system_prompt: Optional system prompt
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            stop: Stop sequences
            user_id: User ID for tracking

        Returns:
            Dict containing the generated text and metadata
        """
        if not self.is_available:
            raise ValueError("Local model not available")

        # Format prompt for Mistral chat model
        if "mistral" in getattr(settings, "LOCAL_MODEL_NAME", "").lower():
            # Mistral chat format
            messages = []

            # Add system prompt if provided
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})

            # Add user message
            messages.append({"role": "user", "content": prompt})

            # Convert to Mistral chat format
            full_prompt = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
        else:
            # Standard format for non-chat models
            full_prompt = prompt
            if system_prompt:
                full_prompt = f"{system_prompt}\n\n{prompt}"

        # Count input tokens
        input_tokens = self.count_tokens(full_prompt)

        # Start timing
        start_time = time.time()

        try:
            # Generate text based on whether we're using a pipeline or direct model
            if self.using_pipeline:
                # Check if it's our SimpleLLM
                if hasattr(self.model, '__class__') and self.model.__class__.__name__ == 'SimpleLLM':
                    # Use the SimpleLLM's generate method directly
                    result = self.model.generate(
                        prompt=full_prompt,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        top_p=top_p,
                        frequency_penalty=frequency_penalty,
                        presence_penalty=presence_penalty,
                        stop=stop
                    )
                    generated_text = result["text"]
                else:
                    # Using the pipeline API (this is the standard case for transformers pipelines)
                    outputs = self.model(
                        full_prompt,
                        max_new_tokens=max_tokens,
                        temperature=temperature,
                        top_p=top_p,
                        repetition_penalty=1.0 + frequency_penalty,
                        do_sample=temperature > 0,
                        num_return_sequences=1,
                        pad_token_id=self.tokenizer.eos_token_id if hasattr(self.tokenizer, 'eos_token_id') else None,
                        eos_token_id=self.tokenizer.eos_token_id if hasattr(self.tokenizer, 'eos_token_id') else None,
                    )
                    # Extract generated text from pipeline output
                    generated_text = outputs[0]["generated_text"]
            else:
                # Using the model directly (not a pipeline)
                # Check if we have a raw model that supports generate
                if hasattr(self.model, "generate") and self.tokenizer:
                    inputs = self.tokenizer(full_prompt, return_tensors="pt")

                    # Move inputs to the same device as the model
                    if hasattr(self.model, "device"):
                        device = self.model.device
                        inputs = {k: v.to(device) for k, v in inputs.items()}

                    # Generate
                    outputs = self.model.generate(
                        **inputs,
                        max_new_tokens=max_tokens,
                        temperature=temperature,
                        top_p=top_p,
                        repetition_penalty=1.0 + frequency_penalty,
                        do_sample=temperature > 0,
                        num_return_sequences=1,
                        pad_token_id=self.tokenizer.eos_token_id,
                        eos_token_id=self.tokenizer.eos_token_id,
                    )

                    # Decode the generated tokens
                    generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
                else:
                    # Fallback: treat as pipeline even if using_pipeline is False
                    logger.warning("Model doesn't have generate method, treating as pipeline")
                    outputs = self.model(
                        full_prompt,
                        max_new_tokens=max_tokens,
                        temperature=temperature,
                        top_p=top_p,
                        repetition_penalty=1.0 + frequency_penalty,
                        do_sample=temperature > 0,
                        num_return_sequences=1,
                        pad_token_id=self.tokenizer.eos_token_id if hasattr(self.tokenizer, 'eos_token_id') else None,
                        eos_token_id=self.tokenizer.eos_token_id if hasattr(self.tokenizer, 'eos_token_id') else None,
                    )
                    # Extract generated text from pipeline output
                    generated_text = outputs[0]["generated_text"]

            # For Mistral chat models, extract the assistant's response
            if "mistral" in getattr(settings, "LOCAL_MODEL_NAME", "").lower():
                # Find the assistant's response in the generated text
                assistant_prefix = "<assistant>"
                if assistant_prefix in generated_text:
                    parts = generated_text.split(assistant_prefix)
                    if len(parts) > 1:
                        generated_text = parts[-1].strip()
                        # Remove any trailing tokens
                        if "</assistant>" in generated_text:
                            generated_text = generated_text.split("</assistant>")[0].strip()
                else:
                    # If we can't find the assistant prefix, just remove the prompt
                    if generated_text.startswith(full_prompt):
                        generated_text = generated_text[len(full_prompt):].strip()
            else:
                # For non-chat models, just remove the prompt
                if generated_text.startswith(full_prompt):
                    generated_text = generated_text[len(full_prompt):].strip()

            # Apply stop sequences if provided
            if stop:
                for stop_seq in stop:
                    if stop_seq in generated_text:
                        generated_text = generated_text.split(stop_seq)[0]

            # Count output tokens
            output_tokens = self.count_tokens(generated_text)

            # Calculate latency
            latency = time.time() - start_time

            model_name = getattr(settings, "LOCAL_MODEL_NAME", "").split("/")[-1]

            return {
                "text": generated_text,
                "model_used": f"local_{model_name}",
                "tokens_used": input_tokens + output_tokens,
                "tokens_input": input_tokens,
                "tokens_output": output_tokens,
                "latency": latency,
                "finish_reason": "stop"
            }
        except Exception as e:
            logger.error(f"Error generating with local model: {str(e)}")
            raise

    def generate_text(self, prompt: str, max_length: int = 100, temperature: float = 0.7,
                     top_p: float = 1.0, repetition_penalty: float = 1.0, do_sample: bool = True) -> str:
        """
        Generate text using the local model (compatibility method).

        Args:
            prompt: The input prompt
            max_length: Maximum length of generated text
            temperature: Temperature for generation
            top_p: Top-p sampling
            repetition_penalty: Repetition penalty
            do_sample: Whether to use sampling

        Returns:
            Generated text string
        """
        if not self.is_available:
            raise ValueError("Local model not available")

        try:
            if self.using_pipeline and hasattr(self.model, '__call__'):
                # Use pipeline
                outputs = self.model(
                    prompt,
                    max_new_tokens=max_length - len(self.tokenizer.encode(prompt)) if self.tokenizer else 50,
                    temperature=temperature,
                    top_p=top_p,
                    repetition_penalty=repetition_penalty,
                    do_sample=do_sample,
                    num_return_sequences=1,
                    pad_token_id=self.tokenizer.eos_token_id if self.tokenizer and hasattr(self.tokenizer, 'eos_token_id') else None,
                    eos_token_id=self.tokenizer.eos_token_id if self.tokenizer and hasattr(self.tokenizer, 'eos_token_id') else None,
                )
                return outputs[0]["generated_text"]
            else:
                # Fallback to generate method
                result = self.generate(prompt, max_tokens=max_length-len(prompt.split()))
                return result.get("text", "")
        except Exception as e:
            logger.error(f"Error in generate_text: {str(e)}")
            return f"Error generating text: {str(e)}"

    def generate_streaming(
        self,
        prompt: str,
        model: str = "mistral-7b-instruct",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        user_id: Optional[str] = None,
    ) -> Generator[Dict[str, Any], None, None]:
        """
        Generate text with streaming using the local model.

        Args:
            prompt: The prompt to generate from
            model: The model to use (ignored, using loaded model)
            system_prompt: Optional system prompt
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            stop: Stop sequences
            user_id: User ID for tracking

        Returns:
            Generator yielding text chunks
        """
        if not self.is_available:
            raise ValueError("Local model not available")

        # Format prompt for Mistral chat model
        if "mistral" in getattr(settings, "LOCAL_MODEL_NAME", "").lower():
            # Mistral chat format
            messages = []

            # Add system prompt if provided
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})

            # Add user message
            messages.append({"role": "user", "content": prompt})

            # Convert to Mistral chat format
            full_prompt = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
        else:
            # Standard format for non-chat models
            full_prompt = prompt
            if system_prompt:
                full_prompt = f"{system_prompt}\n\n{prompt}"

        # Count input tokens
        input_tokens = self.count_tokens(full_prompt)

        try:
            # Generate text
            # Note: This is a simplified streaming implementation
            # Check if it's our SimpleLLM
            if hasattr(self.pipeline, '__class__') and self.pipeline.__class__.__name__ == 'SimpleLLM':
                # Use the SimpleLLM's generate method directly
                result = self.pipeline.generate(
                    prompt=full_prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    top_p=top_p,
                    frequency_penalty=frequency_penalty,
                    presence_penalty=presence_penalty,
                    stop=stop
                )
                generated_text = result["text"]
            # Check if it's a GPT-2 model from model_manager
            elif hasattr(self.model, 'generate_text'):
                # This is likely the GPT-2 model from model_manager
                logger.info("Using GPT-2 model's generate_text method for streaming")
                result = self.model.generate_text(
                    full_prompt,
                    max_length=len(self.tokenizer.encode(full_prompt)) + max_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    repetition_penalty=1.0 + frequency_penalty,
                    do_sample=temperature > 0
                )
                generated_text = result
                # Remove the prompt from the generated text
                if generated_text.startswith(full_prompt):
                    generated_text = generated_text[len(full_prompt):].strip()
            else:
                # In a real implementation, you would use a more sophisticated approach
                outputs = self.pipeline(
                    full_prompt,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    repetition_penalty=1.0 + frequency_penalty,
                    do_sample=temperature > 0,
                    num_return_sequences=1,
                    pad_token_id=self.tokenizer.eos_token_id if hasattr(self.tokenizer, 'eos_token_id') else None,
                    eos_token_id=self.tokenizer.eos_token_id if hasattr(self.tokenizer, 'eos_token_id') else None,
                )
                # Extract generated text
                generated_text = outputs[0]["generated_text"]

            # For Mistral chat models, extract the assistant's response
            if "mistral" in getattr(settings, "LOCAL_MODEL_NAME", "").lower():
                # Find the assistant's response in the generated text
                assistant_prefix = "<assistant>"
                if assistant_prefix in generated_text:
                    parts = generated_text.split(assistant_prefix)
                    if len(parts) > 1:
                        generated_text = parts[-1].strip()
                        # Remove any trailing tokens
                        if "</assistant>" in generated_text:
                            generated_text = generated_text.split("</assistant>")[0].strip()
                else:
                    # If we can't find the assistant prefix, just remove the prompt
                    if generated_text.startswith(full_prompt):
                        generated_text = generated_text[len(full_prompt):].strip()
            else:
                # For non-chat models, just remove the prompt
                if generated_text.startswith(full_prompt):
                    generated_text = generated_text[len(full_prompt):].strip()

            # Apply stop sequences if provided
            if stop:
                for stop_seq in stop:
                    if stop_seq in generated_text:
                        generated_text = generated_text.split(stop_seq)[0]

            # Simulate streaming by yielding chunks
            chunk_size = 4  # Tokens per chunk
            tokens = self.tokenizer.encode(generated_text)

            for i in range(0, len(tokens), chunk_size):
                chunk_tokens = tokens[i:i+chunk_size]
                chunk_text = self.tokenizer.decode(chunk_tokens)

                yield {
                    "text": chunk_text,
                    "tokens": len(chunk_tokens),
                    "finish_reason": "stop" if i + chunk_size >= len(tokens) else None
                }

                # Add a small delay to simulate streaming
                time.sleep(0.05)

        except Exception as e:
            logger.error(f"Error streaming with local model: {str(e)}")
            yield {
                "text": "\n\nI'm sorry, but I'm currently experiencing technical difficulties with the local model.",
                "tokens": 20,
                "finish_reason": "error",
                "error": str(e)
            }

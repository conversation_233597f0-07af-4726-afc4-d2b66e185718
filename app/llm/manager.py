"""
LLM Manager for handling different model providers.
"""
from typing import Dict, List, Optional, Any, Union
import time
from loguru import logger

# Using only local models
from app.llm.local_model_wrapper import LocalModelWrapper
from app.utils.config import settings
from app.utils.metrics import track_model_usage

# Import new providers
from app.llm.providers.anthropic import Anthropic<PERSON>lient
from app.llm.providers.gemini import Gemini<PERSON><PERSON>
from app.llm.providers.ollama import OllamaClient

class LLMManager:
    """
    Manager for handling different LLM providers.
    """

    def __init__(self):
        """
        Initialize the LLM manager.
        """
        # Initialize local model provider
        self.local_model = None
        # We're using only local models

        # Initialize local model if enabled
        if getattr(settings, "USE_LOCAL_MODELS", False):
            try:
                # Try to get the model from the model manager first
                from app.utils.model_manager import model_manager

                # First, check if we have models in MinIO
                default_model = settings.DEFAULT_MODEL
                task = "text-generation"

                # Check if the default model exists in MinIO
                try:
                    minio_result = model_manager.check_model_in_minio(default_model, task)

                    # Handle different return types from check_model_in_minio
                    if isinstance(minio_result, tuple) and len(minio_result) == 2:
                        in_minio, file_count = minio_result
                    elif isinstance(minio_result, bool):
                        in_minio = minio_result
                        file_count = 1 if in_minio else 0
                    else:
                        logger.warning(f"Unexpected return type from check_model_in_minio: {type(minio_result)}")
                        in_minio = False
                        file_count = 0

                    if in_minio and file_count > 0:
                        # Model exists in MinIO, make sure it's loaded
                        model_key = f"{task}:{default_model}"
                        if model_key not in model_manager.models:
                            # Download from MinIO if needed
                            logger.info(f"Downloading model {default_model} from MinIO for text-generation")
                            model_dir = model_manager.download_model_from_minio(default_model, task)
                            if model_dir:
                                # Load the model
                                logger.info(f"Loading model {default_model} from MinIO for text-generation")
                                model_manager._load_text_generation_model(default_model)
                except Exception as e:
                    logger.warning(f"Error checking model in MinIO: {str(e)}")
                    # Continue with fallback options

                # Now try to get the model from the model manager
                chat_model = model_manager.get_model(default_model)

                if chat_model:
                    # Use the model from the model manager
                    self.local_model = LocalModelWrapper(model=chat_model)
                    logger.info(f"Using chat model from model manager: {settings.LOCAL_MODEL_NAME}")

                    # Verify the model is actually working by testing it
                    try:
                        test_prompt = "Hello, can you respond to this test message?"
                        test_response = self.local_model.generate_text(test_prompt)
                        if test_response and len(test_response) > 0:
                            logger.info("Model test successful")
                            # Mark as available
                            self.local_model.is_available = True
                        else:
                            logger.warning("Model test failed - empty response")
                            raise ValueError("Model test failed - empty response")
                    except Exception as test_error:
                        logger.warning(f"Model test failed: {str(test_error)}")
                        raise ValueError(f"Model test failed: {str(test_error)}")
                else:
                    # Try to load gpt2 directly
                    logger.warning("No chat model available from model manager, trying to load gpt2 model directly")
                    try:
                        # Try regular gpt2
                        logger.info("Attempting to load gpt2 model directly")
                        from transformers import pipeline, GPT2LMHeadModel, GPT2Tokenizer

                        # Load model and tokenizer separately to bypass accelerate
                        tokenizer = GPT2Tokenizer.from_pretrained("gpt2")
                        model = GPT2LMHeadModel.from_pretrained("gpt2", device_map=None)
                        model = model.to('cpu')

                        # Create pipeline with pre-loaded components
                        chat_model = pipeline("text-generation", model=model, tokenizer=tokenizer)
                        self.local_model = LocalModelWrapper(model=chat_model)
                        logger.info("Successfully loaded gpt2 model directly")
                        return
                    except Exception as e1:
                        logger.warning(f"Failed to load gpt2: {str(e1)}")
                        try:
                            # Try distilgpt2 as fallback
                            logger.info("Attempting to load distilgpt2 model directly")
                            from transformers import pipeline, GPT2LMHeadModel, GPT2Tokenizer

                            # Load model and tokenizer separately to bypass accelerate
                            tokenizer = GPT2Tokenizer.from_pretrained("distilgpt2")
                            model = GPT2LMHeadModel.from_pretrained("distilgpt2", device_map=None)
                            model = model.to('cpu')

                            # Create pipeline with pre-loaded components
                            chat_model = pipeline("text-generation", model=model, tokenizer=tokenizer)
                            self.local_model = LocalModelWrapper(model=chat_model)
                            logger.info("Successfully loaded distilgpt2 model directly")
                            return
                        except Exception as e2:
                            logger.warning(f"Failed to load gpt2: {str(e2)}")
                            # Now we can fall back to SimpleLLM
                            logger.warning("No chat models available, falling back to SimpleLLM")
                            raise ValueError("No chat model available from model manager or direct loading")

                logger.info(f"Local model initialized successfully: {settings.LOCAL_MODEL_NAME}")
                # Log that we're using TinyLlama as the default model
                if settings.DEFAULT_MODEL == "tinyllama":
                    logger.info("Using TinyLlama as the default conversation model")
            except Exception as e:
                logger.error(f"Error initializing local model: {str(e)}")
                # Create a simple LLM as last resort
                try:
                    from app.llm.simple_llm import SimpleLLM
                    simple_llm = SimpleLLM()
                    self.local_model = LocalModelWrapper(model=simple_llm)
                    self.local_model.is_available = True
                    logger.info("Using SimpleLLM as last resort fallback")
                except Exception as e2:
                    logger.error(f"Failed to initialize even the simple LLM: {str(e2)}")
                    self.local_model = None

            # Final check - if local_model is None or not available, create a SimpleLLM
            if self.local_model is None or not self.local_model.is_available:
                try:
                    from app.llm.simple_llm import SimpleLLM
                    simple_llm = SimpleLLM()
                    self.local_model = LocalModelWrapper(model=simple_llm)
                    self.local_model.is_available = True
                    logger.info("Using SimpleLLM as final fallback")
                except Exception as e3:
                    logger.error(f"Failed to initialize final fallback: {str(e3)}")
                    # Create an inline fallback that always works
                    class EmergencyFallback:
                        def generate(self, prompt, **kwargs):
                            return "I'm sorry, but I'm currently experiencing technical difficulties."

                    self.local_model = LocalModelWrapper(model=EmergencyFallback())
                    self.local_model.is_available = True
                    logger.info("Using emergency fallback model")

        # We're using local models only
        # External API providers are disabled
        logger.info("Using local models only (external APIs disabled)")

        # Initialize new providers (all disabled)
        self.anthropic = None
        self.gemini = None
        self.ollama = None

        logger.info("All external API providers disabled, using only SimbaAI models")

        # Map of model names to providers - only SimbaAI models
        self.model_providers = {
            # SimbaAI models with clear naming
            "local:tinyllama": "simbaAI",
            "tinyllama": "simbaAI",
            "TinyLlama-1.1B-Chat-v1.0": "simbaAI",
            # Microsoft Phi models
            "phi-2": "simbaAI",
            # GPT-2 models
            "gpt2": "simbaAI",
            # DistilGPT2 model
            "distilgpt2": "simbaAI",
            # Emergency fallback
            "emergency_fallback": "simbaAI"
        }

    async def get_available_models(self) -> Dict[str, List[str]]:
        """
        Get available models from all providers.
        """
        models = {}

        # Add local models if available
        if self.local_model and self.local_model.is_available:
            models["local"] = [
                # Local models with clear naming
                "local:tinyllama", "tinyllama", "TinyLlama-1.1B-Chat-v1.0",
                # Microsoft Phi models
                "phi-2",
                # GPT-2 models
                "gpt2",
                # DistilGPT2 model
                "distilgpt2",
                # Emergency fallback
                "emergency_fallback"
            ]

        return models

    def generate(
        self,
        prompt: str,
        model: str = None,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        user_id: Optional[str] = None,
        fallback: bool = True,
    ) -> Dict[str, Any]:
        """
        Generate text using the specified model.

        Args:
            prompt: The prompt to generate from
            model: The model to use
            system_prompt: Optional system prompt
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            stop: Stop sequences
            user_id: User ID for tracking
            fallback: Whether to fallback to another provider if the primary fails

        Returns:
            Dict containing the generated text and metadata
        """
        # Use default model if none specified
        if model is None:
            model = settings.DEFAULT_MODEL

        # Get provider for the model
        provider = self.model_providers.get(model)
        if not provider:
            logger.warning(f"Unknown model: {model}, falling back to default")
            model = settings.DEFAULT_MODEL
            provider = self.model_providers.get(model)

        # Start timing
        start_time = time.time()

        try:
            # Ensure we're using SimbaAI provider
            provider = "simbaAI"

            # Check if SimbaAI model is available
            if not self.local_model or not self.local_model.is_available:
                logger.warning("SimbaAI model not initialized or not available. Using emergency fallback.")
                # Return a graceful fallback response
                return {
                    "text": "I'm sorry, but I'm currently experiencing technical difficulties. The SimbaAI language model is not available at the moment.",
                    "model": "emergency_fallback",
                    "provider": "simbaAI",
                    "tokens_input": len(prompt.split()),
                    "tokens_output": 20,
                    "tokens_total": len(prompt.split()) + 20,
                    "latency": time.time() - start_time,
                    "finish_reason": "error"
                }

            try:
                result = self.local_model.generate(
                    prompt=prompt,
                    model=model,
                    system_prompt=system_prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    top_p=top_p,
                    frequency_penalty=frequency_penalty,
                    presence_penalty=presence_penalty,
                    stop=stop,
                    user_id=user_id
                )
            except Exception as model_error:
                logger.error(f"Error generating with SimbaAI model: {str(model_error)}")
                # Return a graceful fallback response
                return {
                    "text": "I'm sorry, but I encountered an error while processing your request. Please try again later.",
                    "model": model,
                    "provider": "simbaAI",
                    "tokens_input": len(prompt.split()),
                    "tokens_output": 15,
                    "tokens_total": len(prompt.split()) + 15,
                    "latency": time.time() - start_time,
                    "finish_reason": "error"
                }

            # Calculate latency
            latency = time.time() - start_time

            # Track usage
            if user_id:
                track_model_usage(
                    user_id=user_id,
                    model=model,
                    provider=provider,
                    tokens_input=result.get("tokens_input", 0),
                    tokens_output=result.get("tokens_output", 0),
                    latency=latency
                )

            return {
                "text": result["text"],
                "model": model,
                "provider": provider,
                "tokens_input": result.get("tokens_input", 0),
                "tokens_output": result.get("tokens_output", 0),
                "tokens_total": result.get("tokens_total", 0),
                "latency": latency
            }

        except Exception as e:
            logger.error(f"Error generating with {provider} model {model}: {str(e)}")

            # Fallback to another provider if enabled
            if fallback:
                fallback_model = self._get_fallback_model(provider)
                if fallback_model:
                    logger.info(f"Falling back to {fallback_model}")
                    try:
                        return self.generate(
                            prompt=prompt,
                            model=fallback_model,
                            system_prompt=system_prompt,
                            temperature=temperature,
                            max_tokens=max_tokens,
                            top_p=top_p,
                            frequency_penalty=frequency_penalty,
                            presence_penalty=presence_penalty,
                            stop=stop,
                            user_id=user_id,
                            fallback=False  # Prevent infinite fallback loops
                        )
                    except Exception as fallback_error:
                        logger.error(f"Fallback to {fallback_model} also failed: {str(fallback_error)}")

            # If all models fail, return a graceful error message
            logger.error("All LLM providers failed. Local model may not be properly initialized.")

            # Try to create an emergency fallback model on the fly
            try:
                from app.llm.simple_llm import SimpleLLM
                simple_llm = SimpleLLM()
                emergency_response = simple_llm.generate(prompt)
                logger.info("Generated response using emergency SimpleLLM")
                return {
                    "text": emergency_response.get("text", "I'm sorry, but I'm currently experiencing technical difficulties."),
                    "model": "emergency_fallback",
                    "provider": "local",
                    "tokens_input": emergency_response.get("tokens_input", len(prompt.split())),
                    "tokens_output": emergency_response.get("tokens_output", 15),
                    "tokens_total": emergency_response.get("tokens_total", len(prompt.split()) + 15),
                    "latency": time.time() - start_time,
                    "finish_reason": "emergency_fallback"
                }
            except Exception as fallback_error:
                logger.error(f"Emergency fallback also failed: {str(fallback_error)}")
                # Last resort static response
                return {
                    "text": "I'm sorry, but I'm currently experiencing technical difficulties. The language model is not available at the moment.",
                    "model": "static_fallback",
                    "provider": "local",
                    "tokens_input": len(prompt.split()),
                    "tokens_output": 20,
                    "tokens_total": len(prompt.split()) + 20,
                    "latency": time.time() - start_time,
                    "finish_reason": "error",
                    "error": str(e)
                }

    def generate_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Generate a chat completion from a list of messages.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: The model to use
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            stop: Stop sequences
            user_id: User ID for tracking

        Returns:
            Dict containing the generated text and metadata
        """
        # Extract system prompt if present
        system_prompt = None
        user_messages = []

        for msg in messages:
            if msg.get("role") == "system":
                system_prompt = msg.get("content", "")
            elif msg.get("role") == "user":
                user_messages.append(msg.get("content", ""))

        # Combine user messages into a single prompt
        prompt = "\n".join(user_messages) if user_messages else ""
        if not prompt:
            prompt = "Hello"  # Fallback if no user messages

        # Use the regular generate method with the extracted system prompt
        return self.generate(
            prompt=prompt,
            model=model,
            system_prompt=system_prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
            frequency_penalty=frequency_penalty,
            presence_penalty=presence_penalty,
            stop=stop,
            user_id=user_id
        )

    def generate_with_streaming(
        self,
        prompt: str,
        model: str = None,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        user_id: Optional[str] = None,
    ):
        """
        Generate text with streaming using the specified model.

        Args:
            prompt: The prompt to generate from
            model: The model to use
            system_prompt: Optional system prompt
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            stop: Stop sequences
            user_id: User ID for tracking

        Returns:
            Generator yielding text chunks
        """
        # Use default model if none specified
        if model is None:
            model = settings.DEFAULT_MODEL

        # Get provider for the model
        provider = self.model_providers.get(model)
        if not provider:
            logger.warning(f"Unknown model: {model}, falling back to default")
            model = settings.DEFAULT_MODEL
            provider = self.model_providers.get(model)

        # Start timing
        start_time = time.time()
        tokens_total = 0

        try:
            # Ensure we're using SimbaAI provider
            provider = "simbaAI"

            # Check if SimbaAI model is available
            if not self.local_model or not self.local_model.is_available:
                raise ValueError("SimbaAI model not initialized or not available.")

            if provider == "simbaAI":
                stream = self.local_model.generate_streaming(
                    prompt=prompt,
                    model=model,
                    system_prompt=system_prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    top_p=top_p,
                    frequency_penalty=frequency_penalty,
                    presence_penalty=presence_penalty,
                    stop=stop,
                    user_id=user_id
                )
            else:
                raise ValueError(f"Unknown provider: {provider}")

            # Yield from stream
            for chunk in stream:
                tokens_total += chunk.get("tokens", 0)
                yield chunk

            # Calculate latency
            latency = time.time() - start_time

            # Track usage
            if user_id:
                track_model_usage(
                    user_id=user_id,
                    model=model,
                    provider=provider,
                    tokens_input=0,  # We don't know input tokens in streaming
                    tokens_output=tokens_total,
                    latency=latency
                )

        except Exception as e:
            logger.error(f"Error streaming with {provider} model {model}: {str(e)}")
            # Yield an error message instead of raising an exception
            yield {
                "text": "\n\nI'm sorry, but I'm currently experiencing technical difficulties. Please try again later or contact support if the issue persists.",
                "tokens": 20,
                "finish_reason": "error",
                "error": str(e)
            }

    def _get_fallback_model(self, failed_provider: str) -> Optional[str]:
        """
        Get a fallback model from a different provider.

        Args:
            failed_provider: The provider that failed

        Returns:
            A model name from a different provider, or None if no fallback available
        """
        # We only have local models, so if the local provider fails, we have no fallback
        if failed_provider == "local":
            logger.error("Local model failed and no fallback models are available.")
            return None

        # If for some reason a different provider was used, fall back to local
        if self.local_model and self.local_model.is_available:
            logger.info("Falling back to local TinyLlama model")
            return "tinyllama"

        logger.error("No fallback models available.")
        return None
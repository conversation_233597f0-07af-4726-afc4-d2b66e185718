"""
Vector store for semantic search and context retrieval.

This module provides a vector store for:
1. Storing and retrieving embeddings
2. Semantic search
3. Context retrieval for chat models
"""
import time
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime
from loguru import logger
import uuid
import os
import json
import numpy as np

from app.utils.config import settings
from app.utils.model_system_initializer import model_system

class VectorStore:
    """
    Vector store for semantic search and context retrieval.
    """

    def __init__(self):
        """Initialize the vector store."""
        self.qdrant_url = getattr(settings, "QDRANT_URL", "http://localhost:6333")
        self.qdrant_api_key = getattr(settings, "QDRANT_API_KEY", None)
        self.embedding_model_id = getattr(settings, "EMBEDDING_MODEL_ID", None)
        self.embedding_dimension = getattr(settings, "EMBEDDING_DIMENSION", 384)  # Default for all-MiniLM-L6-v2

        # Initialize model references
        self.client = None
        self.is_connected = False
        self.image_model = None
        self.audio_model = None

        # Connect to Qdrant
        self.connect()

        # Create collections if they don't exist
        self._create_collections()

        # Initialize embedding models for different modalities
        self._init_embedding_models()

        logger.info("Vector store initialized with multimodal support")

    def connect(self) -> bool:
        """
        Connect to Qdrant.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            from qdrant_client import QdrantClient
            from qdrant_client.http import models

            # Connect to Qdrant
            self.client = QdrantClient(
                url=self.qdrant_url,
                api_key=self.qdrant_api_key
            )

            # Test connection
            self.client.get_collections()

            self.is_connected = True
            logger.info(f"Connected to Qdrant at {self.qdrant_url}")
            return True
        except ImportError:
            logger.warning("Qdrant client not installed, vector store disabled")
            self.is_connected = False
            return False
        except Exception as e:
            logger.error(f"Error connecting to Qdrant: {str(e)}")
            self.is_connected = False
            return False

    def _create_collections(self):
        """
        Create collections if they don't exist with proper validation and metadata.

        Returns:
            bool: True if all collections were created/validated successfully, False otherwise
        """
        if not self.is_connected:
            logger.warning("Not connected to Qdrant, cannot create collections")
            return False

        try:
            from qdrant_client.http import models
            from qdrant_client.http.exceptions import UnexpectedResponse

            # Define vector configurations for different modalities
            text_vector_config = models.VectorParams(
                size=self.embedding_dimension,
                distance=models.Distance.COSINE
            )

            # Define image embedding dimension (CLIP typically uses 512)
            image_embedding_dimension = getattr(settings, "IMAGE_EMBEDDING_DIMENSION", 512)
            image_vector_config = models.VectorParams(
                size=image_embedding_dimension,
                distance=models.Distance.COSINE
            )

            # Define audio embedding dimension (typically smaller)
            audio_embedding_dimension = getattr(settings, "AUDIO_EMBEDDING_DIMENSION", 192)
            audio_vector_config = models.VectorParams(
                size=audio_embedding_dimension,
                distance=models.Distance.COSINE
            )

            # Standard optimizer configuration
            standard_optimizer_config = {
                "deleted_threshold": 0.2,
                "vacuum_min_vector_number": 1000,
                "default_segment_number": 2,
                "memmap_threshold": 50000,
                "indexing_threshold": 20000,
                "flush_interval_sec": 5,
                "max_optimization_threads": 0,  # 0 means use all available cores
            }

            # High-throughput optimizer for media content
            media_optimizer_config = {
                "deleted_threshold": 0.1,  # Lower threshold for more frequent cleanup
                "vacuum_min_vector_number": 5000,  # Higher threshold for less frequent vacuuming
                "default_segment_number": 4,  # More segments for parallel processing
                "memmap_threshold": 100000,  # Higher threshold for larger collections
                "indexing_threshold": 50000,  # Higher threshold for batch indexing
                "flush_interval_sec": 10,  # Longer interval for better batching
                "max_optimization_threads": 4,  # Explicit thread count
            }

            # Define collection configurations
            collections = [
                # Original collections
                {
                    "name": "user_memory",
                    "description": "User memory for context retrieval",
                    "vector_config": text_vector_config,
                    "optimizer_config": standard_optimizer_config
                },
                {
                    "name": "knowledge_base",
                    "description": "Knowledge base for document retrieval",
                    "vector_config": text_vector_config,
                    "optimizer_config": standard_optimizer_config
                },
                {
                    "name": "conversation_history",
                    "description": "Conversation history for context retrieval",
                    "vector_config": text_vector_config,
                    "optimizer_config": standard_optimizer_config
                },

                # New specialized collections for different modalities
                {
                    "name": "image_embeddings",
                    "description": "Image embeddings for similarity search",
                    "vector_config": image_vector_config,
                    "optimizer_config": media_optimizer_config
                },
                {
                    "name": "audio_embeddings",
                    "description": "Audio embeddings for speech and sound similarity",
                    "vector_config": audio_vector_config,
                    "optimizer_config": media_optimizer_config
                },
                {
                    "name": "generated_content",
                    "description": "Cache for generated content (images, audio, etc.)",
                    "vector_config": text_vector_config,  # Using text embeddings for prompts
                    "optimizer_config": media_optimizer_config
                }
            ]

            success = True
            existing_collections = {c.name: c for c in self.client.get_collections().collections}

            for collection in collections:
                collection_name = collection["name"]
                try:
                    if collection_name in existing_collections:
                        # Validate existing collection
                        if not self._validate_collection(collection_name, collection["vector_config"]):
                            logger.warning(f"Validation failed for collection {collection_name}, recreating...")
                            self.client.delete_collection(collection_name)
                            raise ValueError(f"Invalid configuration for collection {collection_name}")
                        logger.info(f"Using existing collection: {collection_name}")
                    else:
                        # Create new collection with explicit configuration
                        logger.info(f"Creating collection: {collection_name}")
                        # Convert OptimizersConfig to dictionary if needed
                        optimizers_config = collection["optimizer_config"]
                        if hasattr(optimizers_config, 'dict'):
                            optimizers_config = optimizers_config.dict()

                        self.client.create_collection(
                            collection_name=collection_name,
                            vectors_config=collection["vector_config"],
                            optimizers_config=optimizers_config
                        )
                        logger.info(f"Successfully created collection: {collection_name}")

                        # Add metadata as a point with ID 0 - use correct vector dimension
                        try:
                            # Get the correct vector dimension for this collection
                            vector_size = collection["vector_config"].size

                            # Create dummy vector with correct dimension
                            dummy_vector = [0.0] * vector_size

                            self.client.upsert(
                                collection_name=collection_name,
                                points=[
                                    models.PointStruct(
                                        id=0,  # Using 0 as metadata point ID
                                        payload={
                                            "_metadata": {
                                                "description": collection["description"],
                                                "created_at": datetime.now().isoformat(),
                                                "vector_size": vector_size
                                            }
                                        },
                                        vector=dummy_vector  # Dummy vector with correct dimension
                                    )
                                ]
                            )
                        except Exception as meta_error:
                            logger.debug(f"Could not add metadata to collection {collection_name}: {str(meta_error)}")
                            # This is not critical, continue without metadata

                except Exception as e:
                    logger.error(f"Error processing collection {collection_name}: {str(e)}")
                    success = False

            return success

        except Exception as e:
            logger.error(f"Error in collection management: {str(e)}", exc_info=True)
            return False

    def _validate_collection(self, collection_name: str, expected_config) -> bool:
        """
        Validate that an existing collection matches the expected configuration.

        Args:
            collection_name: Name of the collection to validate
            expected_config: Expected vector configuration

        Returns:
            bool: True if validation passes, False otherwise
        """
        try:
            # Get collection info with comprehensive error handling
            try:
                # Suppress Pydantic validation warnings for Qdrant responses
                import warnings
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    collection_info = self.client.get_collection(collection_name)
            except Exception as e:
                logger.debug(f"Failed to get collection {collection_name}: {str(e)}")
                # If we can't get collection info, assume it's valid to avoid recreation
                return True

            # Skip validation if collection_info is None or malformed
            if not collection_info:
                logger.debug(f"Collection {collection_name} info is None, skipping validation")
                return True

            # Handle missing or malformed result structure
            try:
                if not hasattr(collection_info, 'result') or not collection_info.result:
                    logger.debug(f"Collection {collection_name} has no result, skipping validation")
                    return True

                result = collection_info.result

                # Handle missing vectors_count field gracefully
                if not hasattr(result, 'vectors_count'):
                    logger.debug(f"Collection {collection_name} is missing vectors_count field")

                # Handle config structure safely
                if not hasattr(result, 'config') or not result.config:
                    logger.debug(f"Collection {collection_name} has no config, skipping validation")
                    return True

                config = result.config

                # Handle strict_mode_config gracefully
                if hasattr(config, 'strict_mode_config'):
                    logger.debug(f"Ignoring strict_mode_config for {collection_name}")

                # Handle optimizer config safely
                if hasattr(config, 'optimizer_config'):
                    try:
                        optimizer_config = config.optimizer_config
                        if hasattr(optimizer_config, 'max_optimization_threads'):
                            if optimizer_config.max_optimization_threads is None:
                                logger.debug(f"Setting default max_optimization_threads for {collection_name}")
                    except Exception as e:
                        logger.debug(f"Error handling optimizer config for {collection_name}: {str(e)}")

                # Check vector configuration with comprehensive error handling
                try:
                    if not hasattr(config, 'params') or not config.params:
                        logger.debug(f"Collection {collection_name} has no params, skipping vector validation")
                        return True

                    params = config.params

                    # Handle different vector config structures
                    vec_config = None
                    if hasattr(params, 'vectors') and params.vectors:
                        vec_config = params.vectors
                    elif hasattr(params, 'vector') and params.vector:
                        vec_config = params.vector

                    if not vec_config:
                        logger.debug(f"Collection {collection_name} has no vector configuration")
                        return True

                    # Check if expected_config has the required attributes
                    if not hasattr(expected_config, 'size') or not hasattr(expected_config, 'distance'):
                        logger.debug(f"Expected config for {collection_name} is missing required attributes")
                        return True

                    # Compare vector dimensions and distance metrics
                    config_size = getattr(vec_config, 'size', None)
                    config_distance = getattr(vec_config, 'distance', None)

                    if config_size and config_size != expected_config.size:
                        logger.warning(f"Collection {collection_name} has dimension {config_size}, expected {expected_config.size}")
                        return False

                    if config_distance and config_distance != expected_config.distance:
                        logger.warning(f"Collection {collection_name} has distance {config_distance}, expected {expected_config.distance}")
                        return False

                except Exception as e:
                    logger.debug(f"Error validating vector config for {collection_name}: {str(e)}")
                    # Return True to avoid recreating the collection
                    return True

            except Exception as e:
                logger.debug(f"Error processing collection info for {collection_name}: {str(e)}")
                return True

            # Skip metadata validation as it's not critical
            logger.debug(f"Collection {collection_name} validation passed")
            return True

        except Exception as e:
            logger.debug(f"Error validating collection {collection_name}: {str(e)}")
            # Return True to avoid recreating the collection unless absolutely necessary
            return True

    def get_embedding_model(self):
        """
        Get the embedding model.

        Returns:
            Embedding model
        """
        try:
            # Get embedding model
            embedding_model = model_system.get_model_for_task(
                task="embedding",
                custom_model_id=self.embedding_model_id
            )

            if not embedding_model:
                logger.warning("Embedding model not available")
                return None

            return embedding_model
        except Exception as e:
            logger.error(f"Error getting embedding model: {str(e)}")
            return None

    def get_embedding(self, text: str) -> Optional[List[float]]:
        """
        Get embedding for text.

        Args:
            text: Text to embed

        Returns:
            Embedding vector or None if error
        """
        try:
            # Get embedding model
            embedding_model = self.get_embedding_model()

            if not embedding_model:
                return None

            # Get embedding
            embedding = embedding_model.encode(text)

            # Convert to list
            if isinstance(embedding, np.ndarray):
                embedding = embedding.tolist()

            return embedding
        except Exception as e:
            logger.error(f"Error getting embedding: {str(e)}")
            return None

    def _init_embedding_models(self):
        """
        Initialize embedding models for different modalities.
        """
        try:
            # Image model initialization
            self._init_image_embedding_model()

            # Audio model initialization
            self._init_audio_embedding_model()

            logger.info("Multimodal embedding models initialized")
        except Exception as e:
            logger.error(f"Error initializing multimodal embedding models: {str(e)}")

    def _init_image_embedding_model(self):
        """
        Initialize the image embedding model (CLIP).
        """
        try:
            # Try to get the model from the model manager if available
            try:
                from app.utils.model_manager import model_manager
                clip_model = model_manager.get_model_for_task("image-embedding")

                if clip_model:
                    self.image_model = clip_model
                    logger.info("Using CLIP model from model manager")
                    return
            except (ImportError, AttributeError) as e:
                logger.debug(f"Could not get CLIP model from model manager: {str(e)}")

            # Fallback to loading directly
            try:
                import torch
                from transformers import CLIPProcessor, CLIPModel

                model_id = getattr(settings, "CLIP_MODEL_ID", "openai/clip-vit-base-patch32")
                self.image_model = {
                    "model": CLIPModel.from_pretrained(model_id),
                    "processor": CLIPProcessor.from_pretrained(model_id)
                }
                logger.info(f"Image embedding model (CLIP) initialized: {model_id}")
            except ImportError:
                logger.warning("Transformers or PyTorch not installed, image embeddings disabled")
                self.image_model = None
            except Exception as e:
                logger.error(f"Error loading CLIP model: {str(e)}")
                self.image_model = None
        except Exception as e:
            logger.error(f"Error initializing image embedding model: {str(e)}")
            self.image_model = None

    def _init_audio_embedding_model(self):
        """
        Initialize the audio embedding model.
        """
        try:
            # Try to get the model from the model manager if available
            try:
                from app.utils.model_manager import model_manager
                audio_model = model_manager.get_model_for_task("audio-embedding")

                if audio_model:
                    self.audio_model = audio_model
                    logger.info("Using audio embedding model from model manager")
                    return
            except (ImportError, AttributeError) as e:
                logger.debug(f"Could not get audio model from model manager: {str(e)}")

            # Fallback to loading directly
            try:
                import torch
                from transformers import AutoFeatureExtractor, AutoModel

                # Use a supported audio model instead of speechbrain
                model_id = getattr(settings, "AUDIO_MODEL_ID", "facebook/wav2vec2-base-960h")

                self.audio_model = {
                    "model": AutoModel.from_pretrained(model_id),
                    "processor": AutoFeatureExtractor.from_pretrained(model_id)
                }
                logger.info(f"Audio embedding model initialized: {model_id}")
            except ImportError:
                logger.warning("Transformers or PyTorch not installed, audio embeddings disabled")
                self.audio_model = None
            except Exception as e:
                logger.error(f"Error loading audio model: {str(e)}")
                self.audio_model = None
        except Exception as e:
            logger.error(f"Error initializing audio embedding model: {str(e)}")
            self.audio_model = None

    def get_image_embedding(self, image_data) -> Optional[List[float]]:
        """
        Get embedding for an image.

        Args:
            image_data: Image data (PIL Image, file path, or bytes)

        Returns:
            Embedding vector or None if embedding fails
        """
        if not image_data:
            logger.warning("Empty image data provided for embedding")
            return None

        if self.image_model is None:
            logger.warning("No image embedding model available")
            return None

        try:
            import torch
            from PIL import Image
            import io
            import numpy as np

            # Convert image data to PIL Image
            if isinstance(image_data, str):
                # It's a file path
                image = Image.open(image_data).convert('RGB')
            elif isinstance(image_data, bytes):
                # It's bytes
                image = Image.open(io.BytesIO(image_data)).convert('RGB')
            elif hasattr(image_data, 'read'):
                # It's a file-like object
                image = Image.open(image_data).convert('RGB')
            else:
                # Assume it's already a PIL Image
                image = image_data

            # Check if we're using a model from model_manager or our own loaded model
            if isinstance(self.image_model, dict) and "processor" in self.image_model and "model" in self.image_model:
                # Process the image
                inputs = self.image_model["processor"](
                    images=image,
                    return_tensors="pt",
                    padding=True
                )

                # Generate embedding
                with torch.no_grad():
                    outputs = self.image_model["model"].get_image_features(**inputs)

                # Normalize the embedding
                embedding = outputs.squeeze().cpu().numpy()
            else:
                # Assume it's a model with an encode method
                embedding = self.image_model.encode(image)

            # Normalize if needed
            if isinstance(embedding, np.ndarray):
                embedding = embedding / np.linalg.norm(embedding)

            # Convert to list of floats
            embedding_list = embedding.tolist() if isinstance(embedding, np.ndarray) else embedding

            return embedding_list
        except Exception as e:
            logger.error(f"Error generating image embedding: {str(e)}")
            return None

    def get_audio_embedding(self, audio_data) -> Optional[List[float]]:
        """
        Get embedding for audio.

        Args:
            audio_data: Audio data (file path, bytes, or numpy array)

        Returns:
            Embedding vector or None if embedding fails
        """
        if not audio_data:
            logger.warning("Empty audio data provided for embedding")
            return None

        if self.audio_model is None:
            logger.warning("No audio embedding model available")
            return None

        try:
            import torch
            import numpy as np
            import librosa

            # Convert audio data to numpy array
            if isinstance(audio_data, str):
                # It's a file path
                waveform, sample_rate = librosa.load(audio_data, sr=16000)
            elif isinstance(audio_data, bytes):
                # It's bytes, create a temporary file
                import tempfile
                import os

                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
                temp_file.write(audio_data)
                temp_file.close()

                waveform, sample_rate = librosa.load(temp_file.name, sr=16000)
                os.unlink(temp_file.name)
            elif isinstance(audio_data, np.ndarray):
                # It's already a numpy array
                waveform = audio_data
                sample_rate = 16000  # Assume 16kHz if not specified
            else:
                raise ValueError("Unsupported audio data format")

            # Check if we're using a model from model_manager or our own loaded model
            if isinstance(self.audio_model, dict) and "processor" in self.audio_model and "model" in self.audio_model:
                # Process the audio
                inputs = self.audio_model["processor"](
                    waveform,
                    sampling_rate=sample_rate,
                    return_tensors="pt"
                )

                # Generate embedding
                with torch.no_grad():
                    outputs = self.audio_model["model"](**inputs)

                # Get the embedding from the model output
                if hasattr(outputs, "last_hidden_state"):
                    # Use mean pooling over the time dimension
                    embedding = outputs.last_hidden_state.mean(dim=1).squeeze().cpu().numpy()
                else:
                    # Use the pooled output if available
                    embedding = outputs.pooler_output.squeeze().cpu().numpy()
            else:
                # Assume it's a model with an encode method
                embedding = self.audio_model.encode(waveform, sample_rate)

            # Normalize if needed
            if isinstance(embedding, np.ndarray):
                embedding = embedding / np.linalg.norm(embedding)

            # Convert to list of floats
            embedding_list = embedding.tolist() if isinstance(embedding, np.ndarray) else embedding

            return embedding_list
        except Exception as e:
            logger.error(f"Error generating audio embedding: {str(e)}")
            return None

    # Methods for adding multimodal content to vector store

    def add_image_embedding(
        self,
        image_data,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        image_path: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> Optional[str]:
        """
        Add image embedding to vector store.

        Args:
            image_data: Image data (PIL Image, file path, or bytes)
            user_id: Optional user ID
            metadata: Optional metadata
            image_path: Optional path to the image
            tags: Optional tags for the image

        Returns:
            ID of the added point or None if error
        """
        if not self.is_connected:
            logger.warning("Not connected to Qdrant, cannot add image embedding")
            return None

        try:
            # Get image embedding
            embedding = self.get_image_embedding(image_data)

            if not embedding:
                logger.warning("Failed to get image embedding")
                return None

            # Generate ID
            import uuid
            point_id = str(uuid.uuid4())

            # Prepare payload
            payload = {
                "timestamp": time.time(),
                "type": "image"
            }

            if user_id:
                payload["user_id"] = user_id

            if image_path:
                payload["image_path"] = image_path

            if tags:
                payload["tags"] = tags

            if metadata:
                payload["metadata"] = metadata

            # Add to Qdrant
            from qdrant_client.http import models

            self.client.upsert(
                collection_name="image_embeddings",
                points=[
                    models.PointStruct(
                        id=point_id,
                        vector=embedding,
                        payload=payload
                    )
                ]
            )

            logger.info(f"Added image embedding with ID {point_id}")
            return point_id
        except Exception as e:
            logger.error(f"Error adding image embedding: {str(e)}")
            return None

    def add_audio_embedding(
        self,
        audio_data,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        audio_path: Optional[str] = None,
        tags: Optional[List[str]] = None,
        transcript: Optional[str] = None
    ) -> Optional[str]:
        """
        Add audio embedding to vector store.

        Args:
            audio_data: Audio data (file path, bytes, or numpy array)
            user_id: Optional user ID
            metadata: Optional metadata
            audio_path: Optional path to the audio
            tags: Optional tags for the audio
            transcript: Optional transcript of the audio

        Returns:
            ID of the added point or None if error
        """
        if not self.is_connected:
            logger.warning("Not connected to Qdrant, cannot add audio embedding")
            return None

        try:
            # Get audio embedding
            embedding = self.get_audio_embedding(audio_data)

            if not embedding:
                logger.warning("Failed to get audio embedding")
                return None

            # Generate ID
            import uuid
            point_id = str(uuid.uuid4())

            # Prepare payload
            payload = {
                "timestamp": time.time(),
                "type": "audio"
            }

            if user_id:
                payload["user_id"] = user_id

            if audio_path:
                payload["audio_path"] = audio_path

            if tags:
                payload["tags"] = tags

            if transcript:
                payload["transcript"] = transcript

            if metadata:
                payload["metadata"] = metadata

            # Add to Qdrant
            from qdrant_client.http import models

            self.client.upsert(
                collection_name="audio_embeddings",
                points=[
                    models.PointStruct(
                        id=point_id,
                        vector=embedding,
                        payload=payload
                    )
                ]
            )

            logger.info(f"Added audio embedding with ID {point_id}")
            return point_id
        except Exception as e:
            logger.error(f"Error adding audio embedding: {str(e)}")
            return None

    def add_generated_content(
        self,
        prompt: str,
        content_type: str,
        content_path: str,
        metadata: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> Optional[str]:
        """
        Add generated content to vector store for caching.

        Args:
            prompt: The prompt used to generate the content
            content_type: Type of content (image, audio, text, etc.)
            content_path: Path to the generated content
            metadata: Optional metadata
            user_id: Optional user ID
            tags: Optional tags

        Returns:
            ID of the added point or None if error
        """
        if not self.is_connected:
            logger.warning("Not connected to Qdrant, cannot add generated content")
            return None

        try:
            # Get embedding for the prompt
            embedding = self.get_embedding(prompt)

            if not embedding:
                logger.warning("Failed to get embedding for prompt")
                return None

            # Generate ID
            import uuid
            point_id = str(uuid.uuid4())

            # Prepare payload
            payload = {
                "timestamp": time.time(),
                "type": "generated_content",
                "content_type": content_type,
                "prompt": prompt,
                "content_path": content_path
            }

            if user_id:
                payload["user_id"] = user_id

            if tags:
                payload["tags"] = tags

            if metadata:
                payload["metadata"] = metadata

            # Add to Qdrant
            from qdrant_client.http import models

            self.client.upsert(
                collection_name="generated_content",
                points=[
                    models.PointStruct(
                        id=point_id,
                        vector=embedding,
                        payload=payload
                    )
                ]
            )

            logger.info(f"Added generated content with ID {point_id}")
            return point_id
        except Exception as e:
            logger.error(f"Error adding generated content: {str(e)}")
            return None

    # Methods for searching multimodal content

    def search_images_by_text(
        self,
        text_query: str,
        limit: int = 10,
        user_id: Optional[str] = None,
        filter_tags: Optional[List[str]] = None,
        min_score: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search for images using a text query.

        Args:
            text_query: Text query to search for
            limit: Maximum number of results
            user_id: Optional user ID to filter by
            filter_tags: Optional tags to filter by
            min_score: Minimum similarity score (0-1)

        Returns:
            List of matching images with metadata
        """
        if not self.is_connected:
            logger.warning("Not connected to Qdrant, cannot search images")
            return []

        try:
            # Get embedding for the text query
            embedding = self.get_embedding(text_query)

            if not embedding:
                logger.warning("Failed to get embedding for text query")
                return []

            # Prepare filter
            from qdrant_client.http import models

            filter_conditions = []

            if user_id:
                filter_conditions.append(
                    models.FieldCondition(
                        key="user_id",
                        match=models.MatchValue(value=user_id)
                    )
                )

            if filter_tags:
                for tag in filter_tags:
                    filter_conditions.append(
                        models.FieldCondition(
                            key="tags",
                            match=models.MatchValue(value=tag)
                        )
                    )

            # Create filter if conditions exist
            search_filter = None
            if filter_conditions:
                search_filter = models.Filter(
                    must=filter_conditions
                )

            # Search in Qdrant
            search_results = self.client.search(
                collection_name="image_embeddings",
                query_vector=embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=min_score
            )

            # Format results
            results = []
            for result in search_results:
                item = {
                    "id": result.id,
                    "score": result.score,
                    **result.payload
                }
                results.append(item)

            return results
        except Exception as e:
            logger.error(f"Error searching images by text: {str(e)}")
            return []

    def search_images_by_image(
        self,
        image_data,
        limit: int = 10,
        user_id: Optional[str] = None,
        filter_tags: Optional[List[str]] = None,
        min_score: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search for similar images using an image.

        Args:
            image_data: Image data (PIL Image, file path, or bytes)
            limit: Maximum number of results
            user_id: Optional user ID to filter by
            filter_tags: Optional tags to filter by
            min_score: Minimum similarity score (0-1)

        Returns:
            List of matching images with metadata
        """
        if not self.is_connected:
            logger.warning("Not connected to Qdrant, cannot search images")
            return []

        try:
            # Get embedding for the image
            embedding = self.get_image_embedding(image_data)

            if not embedding:
                logger.warning("Failed to get embedding for image")
                return []

            # Prepare filter
            from qdrant_client.http import models

            filter_conditions = []

            if user_id:
                filter_conditions.append(
                    models.FieldCondition(
                        key="user_id",
                        match=models.MatchValue(value=user_id)
                    )
                )

            if filter_tags:
                for tag in filter_tags:
                    filter_conditions.append(
                        models.FieldCondition(
                            key="tags",
                            match=models.MatchValue(value=tag)
                        )
                    )

            # Create filter if conditions exist
            search_filter = None
            if filter_conditions:
                search_filter = models.Filter(
                    must=filter_conditions
                )

            # Search in Qdrant
            search_results = self.client.search(
                collection_name="image_embeddings",
                query_vector=embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=min_score
            )

            # Format results
            results = []
            for result in search_results:
                item = {
                    "id": result.id,
                    "score": result.score,
                    **result.payload
                }
                results.append(item)

            return results
        except Exception as e:
            logger.error(f"Error searching images by image: {str(e)}")
            return []

    def search_audio_by_text(
        self,
        text_query: str,
        limit: int = 10,
        user_id: Optional[str] = None,
        filter_tags: Optional[List[str]] = None,
        min_score: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search for audio using a text query (searches in transcripts).

        Args:
            text_query: Text query to search for
            limit: Maximum number of results
            user_id: Optional user ID to filter by
            filter_tags: Optional tags to filter by
            min_score: Minimum similarity score (0-1)

        Returns:
            List of matching audio with metadata
        """
        if not self.is_connected:
            logger.warning("Not connected to Qdrant, cannot search audio")
            return []

        try:
            # Get embedding for the text query
            embedding = self.get_embedding(text_query)

            if not embedding:
                logger.warning("Failed to get embedding for text query")
                return []

            # Prepare filter
            from qdrant_client.http import models

            filter_conditions = []

            # Only search in audio with transcripts
            filter_conditions.append(
                models.FieldCondition(
                    key="transcript",
                    match=models.MatchValue(value={"$exists": True})
                )
            )

            if user_id:
                filter_conditions.append(
                    models.FieldCondition(
                        key="user_id",
                        match=models.MatchValue(value=user_id)
                    )
                )

            if filter_tags:
                for tag in filter_tags:
                    filter_conditions.append(
                        models.FieldCondition(
                            key="tags",
                            match=models.MatchValue(value=tag)
                        )
                    )

            # Create filter
            search_filter = models.Filter(
                must=filter_conditions
            )

            # Search in Qdrant
            search_results = self.client.search(
                collection_name="audio_embeddings",
                query_vector=embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=min_score
            )

            # Format results
            results = []
            for result in search_results:
                item = {
                    "id": result.id,
                    "score": result.score,
                    **result.payload
                }
                results.append(item)

            return results
        except Exception as e:
            logger.error(f"Error searching audio by text: {str(e)}")
            return []

    def search_audio_by_audio(
        self,
        audio_data,
        limit: int = 10,
        user_id: Optional[str] = None,
        filter_tags: Optional[List[str]] = None,
        min_score: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search for similar audio using an audio sample.

        Args:
            audio_data: Audio data (file path, bytes, or numpy array)
            limit: Maximum number of results
            user_id: Optional user ID to filter by
            filter_tags: Optional tags to filter by
            min_score: Minimum similarity score (0-1)

        Returns:
            List of matching audio with metadata
        """
        if not self.is_connected:
            logger.warning("Not connected to Qdrant, cannot search audio")
            return []

        try:
            # Get embedding for the audio
            embedding = self.get_audio_embedding(audio_data)

            if not embedding:
                logger.warning("Failed to get embedding for audio")
                return []

            # Prepare filter
            from qdrant_client.http import models

            filter_conditions = []

            if user_id:
                filter_conditions.append(
                    models.FieldCondition(
                        key="user_id",
                        match=models.MatchValue(value=user_id)
                    )
                )

            if filter_tags:
                for tag in filter_tags:
                    filter_conditions.append(
                        models.FieldCondition(
                            key="tags",
                            match=models.MatchValue(value=tag)
                        )
                    )

            # Create filter if conditions exist
            search_filter = None
            if filter_conditions:
                search_filter = models.Filter(
                    must=filter_conditions
                )

            # Search in Qdrant
            search_results = self.client.search(
                collection_name="audio_embeddings",
                query_vector=embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=min_score
            )

            # Format results
            results = []
            for result in search_results:
                item = {
                    "id": result.id,
                    "score": result.score,
                    **result.payload
                }
                results.append(item)

            return results
        except Exception as e:
            logger.error(f"Error searching audio by audio: {str(e)}")
            return []

    def get_cached_generated_content(
        self,
        prompt: str,
        content_type: str,
        metadata_filter: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        min_score: float = 0.9
    ) -> Optional[Dict[str, Any]]:
        """
        Get cached generated content if available.

        Args:
            prompt: The prompt used to generate the content
            content_type: Type of content (image, audio, text, etc.)
            metadata_filter: Optional metadata filter
            user_id: Optional user ID
            min_score: Minimum similarity score (0-1)

        Returns:
            Cached content or None if not found
        """
        if not self.is_connected:
            logger.warning("Not connected to Qdrant, cannot get cached content")
            return None

        try:
            # Get embedding for the prompt
            embedding = self.get_embedding(prompt)

            if not embedding:
                logger.warning("Failed to get embedding for prompt")
                return None

            # Prepare filter
            from qdrant_client.http import models

            filter_conditions = [
                models.FieldCondition(
                    key="content_type",
                    match=models.MatchValue(value=content_type)
                )
            ]

            if user_id:
                filter_conditions.append(
                    models.FieldCondition(
                        key="user_id",
                        match=models.MatchValue(value=user_id)
                    )
                )

            if metadata_filter:
                for key, value in metadata_filter.items():
                    filter_conditions.append(
                        models.FieldCondition(
                            key=f"metadata.{key}",
                            match=models.MatchValue(value=value)
                        )
                    )

            # Create filter
            search_filter = models.Filter(
                must=filter_conditions
            )

            # Search in Qdrant
            search_results = self.client.search(
                collection_name="generated_content",
                query_vector=embedding,
                query_filter=search_filter,
                limit=1,
                score_threshold=min_score
            )

            # Return the first result if found
            if search_results and len(search_results) > 0:
                result = search_results[0]
                return {
                    "id": result.id,
                    "score": result.score,
                    **result.payload
                }

            return None
        except Exception as e:
            logger.error(f"Error getting cached generated content: {str(e)}")
            return None

    def add_user_memory(
        self,
        user_id: str,
        text: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Add user memory.

        Args:
            user_id: User ID
            text: Memory text
            metadata: Optional metadata

        Returns:
            Memory ID or None if error
        """
        try:
            if not self.is_connected:
                logger.warning("Qdrant not connected, cannot add user memory")
                return None

            # Get embedding
            embedding = self.get_embedding(text)

            if not embedding:
                logger.warning("Could not get embedding for user memory")
                return None

            # Generate ID
            memory_id = str(uuid.uuid4())

            # Add to Qdrant
            self.client.upsert(
                collection_name="user_memory",
                points=[{
                    "id": memory_id,
                    "vector": embedding,
                    "payload": {
                        "user_id": user_id,
                        "text": text,
                        "timestamp": datetime.now().isoformat(),
                        "metadata": metadata or {}
                    }
                }]
            )

            logger.info(f"Added user memory {memory_id} for user {user_id}")
            return memory_id
        except Exception as e:
            logger.error(f"Error adding user memory: {str(e)}")
            return None

    def add_knowledge_base_item(
        self,
        text: str,
        source: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Add knowledge base item.

        Args:
            text: Item text
            source: Source of the item
            metadata: Optional metadata

        Returns:
            Item ID or None if error
        """
        try:
            if not self.is_connected:
                logger.warning("Qdrant not connected, cannot add knowledge base item")
                return None

            # Get embedding
            embedding = self.get_embedding(text)

            if not embedding:
                logger.warning("Could not get embedding for knowledge base item")
                return None

            # Generate ID
            item_id = str(uuid.uuid4())

            # Add to Qdrant
            self.client.upsert(
                collection_name="knowledge_base",
                points=[{
                    "id": item_id,
                    "vector": embedding,
                    "payload": {
                        "text": text,
                        "source": source,
                        "timestamp": datetime.now().isoformat(),
                        "metadata": metadata or {}
                    }
                }]
            )

            logger.info(f"Added knowledge base item {item_id}")
            return item_id
        except Exception as e:
            logger.error(f"Error adding knowledge base item: {str(e)}")
            return None

    def add_conversation(
        self,
        user_id: str,
        session_id: str,
        messages: List[Dict[str, Any]]
    ) -> Optional[str]:
        """
        Add conversation to history.

        Args:
            user_id: User ID
            session_id: Session ID
            messages: List of messages

        Returns:
            Conversation ID or None if error
        """
        try:
            if not self.is_connected:
                logger.warning("Qdrant not connected, cannot add conversation")
                return None

            # Format conversation text
            conversation_text = ""

            for message in messages:
                role = message.get("role", "unknown")
                content = message.get("content", "")

                conversation_text += f"{role}: {content}\n"

            # Get embedding
            embedding = self.get_embedding(conversation_text)

            if not embedding:
                logger.warning("Could not get embedding for conversation")
                return None

            # Generate ID
            conversation_id = str(uuid.uuid4())

            # Add to Qdrant
            self.client.upsert(
                collection_name="conversation_history",
                points=[{
                    "id": conversation_id,
                    "vector": embedding,
                    "payload": {
                        "user_id": user_id,
                        "session_id": session_id,
                        "messages": messages,
                        "text": conversation_text,
                        "timestamp": datetime.now().isoformat()
                    }
                }]
            )

            logger.info(f"Added conversation {conversation_id} for user {user_id}")
            return conversation_id
        except Exception as e:
            logger.error(f"Error adding conversation: {str(e)}")
            return None

    def search_user_memory(
        self,
        user_id: str,
        query: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Search user memory.

        Args:
            user_id: User ID
            query: Search query
            limit: Maximum number of results

        Returns:
            List of memory items
        """
        try:
            if not self.is_connected:
                logger.warning("Qdrant not connected, cannot search user memory")
                return []

            # Get embedding
            embedding = self.get_embedding(query)

            if not embedding:
                logger.warning("Could not get embedding for search query")
                return []

            # Search Qdrant
            results = self.client.search(
                collection_name="user_memory",
                query_vector=embedding,
                query_filter={
                    "must": [
                        {
                            "key": "user_id",
                            "match": {
                                "value": user_id
                            }
                        }
                    ]
                },
                limit=limit
            )

            # Format results
            memory_items = []

            for result in results:
                memory_items.append({
                    "id": result.id,
                    "text": result.payload.get("text", ""),
                    "timestamp": result.payload.get("timestamp", ""),
                    "metadata": result.payload.get("metadata", {}),
                    "score": result.score
                })

            return memory_items
        except Exception as e:
            logger.error(f"Error searching user memory: {str(e)}")
            return []

    def search_knowledge_base(
        self,
        query: str,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search knowledge base.

        Args:
            query: Search query
            limit: Maximum number of results
            filters: Optional filters

        Returns:
            List of knowledge base items
        """
        try:
            if not self.is_connected:
                logger.warning("Qdrant not connected, cannot search knowledge base")
                return []

            # Get embedding
            embedding = self.get_embedding(query)

            if not embedding:
                logger.warning("Could not get embedding for search query")
                return []

            # Build query filter
            query_filter = None

            if filters:
                query_filter = {"must": []}

                for key, value in filters.items():
                    if key == "source":
                        query_filter["must"].append({
                            "key": "source",
                            "match": {
                                "value": value
                            }
                        })
                    elif key in ["metadata.category", "metadata.tags"]:
                        query_filter["must"].append({
                            "key": key,
                            "match": {
                                "value": value
                            }
                        })

            # Search Qdrant
            results = self.client.search(
                collection_name="knowledge_base",
                query_vector=embedding,
                query_filter=query_filter,
                limit=limit
            )

            # Format results
            items = []

            for result in results:
                items.append({
                    "id": result.id,
                    "text": result.payload.get("text", ""),
                    "source": result.payload.get("source", ""),
                    "timestamp": result.payload.get("timestamp", ""),
                    "metadata": result.payload.get("metadata", {}),
                    "score": result.score
                })

            return items
        except Exception as e:
            logger.error(f"Error searching knowledge base: {str(e)}")
            return []

    def search_conversations(
        self,
        user_id: str,
        query: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Search conversation history.

        Args:
            user_id: User ID
            query: Search query
            limit: Maximum number of results

        Returns:
            List of conversations
        """
        try:
            if not self.is_connected:
                logger.warning("Qdrant not connected, cannot search conversations")
                return []

            # Get embedding
            embedding = self.get_embedding(query)

            if not embedding:
                logger.warning("Could not get embedding for search query")
                return []

            # Search Qdrant
            results = self.client.search(
                collection_name="conversation_history",
                query_vector=embedding,
                query_filter={
                    "must": [
                        {
                            "key": "user_id",
                            "match": {
                                "value": user_id
                            }
                        }
                    ]
                },
                limit=limit
            )

            # Format results
            conversations = []

            for result in results:
                conversations.append({
                    "id": result.id,
                    "session_id": result.payload.get("session_id", ""),
                    "messages": result.payload.get("messages", []),
                    "text": result.payload.get("text", ""),
                    "timestamp": result.payload.get("timestamp", ""),
                    "score": result.score
                })

            return conversations
        except Exception as e:
            logger.error(f"Error searching conversations: {str(e)}")
            return []

    def get_context_for_query(
        self,
        user_id: str,
        query: str,
        include_memory: bool = True,
        include_knowledge: bool = True,
        include_conversations: bool = True,
        memory_limit: int = 3,
        knowledge_limit: int = 3,
        conversation_limit: int = 2
    ) -> Dict[str, Any]:
        """
        Get context for a query.

        Args:
            user_id: User ID
            query: Query text
            include_memory: Whether to include user memory
            include_knowledge: Whether to include knowledge base
            include_conversations: Whether to include conversation history
            memory_limit: Maximum number of memory items
            knowledge_limit: Maximum number of knowledge base items
            conversation_limit: Maximum number of conversations

        Returns:
            Dictionary with context
        """
        context = {
            "memory": [],
            "knowledge": [],
            "conversations": []
        }

        try:
            # Get user memory
            if include_memory:
                context["memory"] = self.search_user_memory(
                    user_id=user_id,
                    query=query,
                    limit=memory_limit
                )

            # Get knowledge base items
            if include_knowledge:
                context["knowledge"] = self.search_knowledge_base(
                    query=query,
                    limit=knowledge_limit
                )

            # Get conversations
            if include_conversations:
                context["conversations"] = self.search_conversations(
                    user_id=user_id,
                    query=query,
                    limit=conversation_limit
                )

            return context
        except Exception as e:
            logger.error(f"Error getting context for query: {str(e)}")
            return context

    def format_context_for_prompt(
        self,
        context: Dict[str, Any],
        max_length: int = 2000
    ) -> str:
        """
        Format context for prompt.

        Args:
            context: Context dictionary
            max_length: Maximum length of formatted context

        Returns:
            Formatted context
        """
        formatted_context = ""

        # Add memory
        if context.get("memory"):
            formatted_context += "Your memory contains the following information:\n"

            for memory in context["memory"]:
                formatted_context += f"- {memory['text']}\n"

            formatted_context += "\n"

        # Add knowledge
        if context.get("knowledge"):
            formatted_context += "The knowledge base contains the following information:\n"

            for item in context["knowledge"]:
                formatted_context += f"- {item['text']} (Source: {item['source']})\n"

            formatted_context += "\n"

        # Add conversations
        if context.get("conversations"):
            formatted_context += "You've had the following relevant conversations:\n"

            for conversation in context["conversations"]:
                formatted_context += f"Conversation from {conversation['timestamp']}:\n"

                for message in conversation.get("messages", [])[:5]:  # Limit to 5 messages per conversation
                    role = message.get("role", "unknown")
                    content = message.get("content", "")

                    formatted_context += f"{role.capitalize()}: {content}\n"

                formatted_context += "\n"

        # Truncate if too long
        if len(formatted_context) > max_length:
            formatted_context = formatted_context[:max_length] + "...\n"

        return formatted_context

# Create a singleton instance
vector_store = VectorStore()
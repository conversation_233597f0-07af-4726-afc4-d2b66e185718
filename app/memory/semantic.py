"""
Long-term semantic memory using Qdrant.
"""
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid
import os
from loguru import logger
import numpy as np

# Import our embedding wrapper
from app.utils.embedding_wrapper import EmbeddingWrapper
from app.utils.embedding_utils import SimpleEmbedder  # Keep the simple embedder as a fallback
from app.utils.config import settings

class SemanticMemory:
    """
    Long-term semantic memory using Qdrant.
    """

    def __init__(self):
        """
        Initialize the semantic memory.
        """
        # Import the PyTorch CPU patch to ensure it's applied
        try:
            from app.utils.torch_cpu_patch import success as torch_cpu_patch_applied
            if torch_cpu_patch_applied:
                logger.info("PyTorch CPU patch applied in semantic memory")
        except ImportError:
            logger.warning("Could not import PyTorch CPU patch")

        # Initialize embedding model
        if settings.DISABLE_ML_MODELS:
            logger.info("ML models disabled in settings, using fallback embedder")
            self.embedding_model = SimpleEmbedder(model_name="dummy")
            self.embedding_dimension = self.embedding_model.embedding_dimension
            logger.info(f"Using fallback embedder with dimension {self.embedding_dimension}")
        else:
            try:
                # Create a models directory if it doesn't exist
                models_dir = os.path.join(os.getcwd(), "models")
                os.makedirs(models_dir, exist_ok=True)

                # Use our wrapper to initialize the embedding model
                self.embedding_model = EmbeddingWrapper(
                    model_name=settings.EMBEDDING_MODEL,
                    device="cpu",
                    cache_dir=models_dir
                )

                # Get the embedding dimension
                self.embedding_dimension = self.embedding_model.embedding_dimension

                # Check if the model was successfully initialized
                if self.embedding_model.is_available:
                    logger.info(f"Successfully loaded embedding model with dimension {self.embedding_dimension}")
                else:
                    logger.warning("Embedding model not available, using fallback")
                    # Create a fallback embedder
                    self.embedding_model = SimpleEmbedder(model_name="dummy")
                    self.embedding_dimension = self.embedding_model.embedding_dimension
                    logger.info(f"Using fallback embedder with dimension {self.embedding_dimension}")
            except Exception as e:
                logger.error(f"Failed to initialize embedding model: {str(e)}")
                # Create a fallback embedder
                self.embedding_model = SimpleEmbedder(model_name="dummy")
                self.embedding_dimension = self.embedding_model.embedding_dimension
                logger.info(f"Using fallback embedder with dimension {self.embedding_dimension}")

        # Initialize Qdrant client
        try:
            from qdrant_client import QdrantClient
            from qdrant_client.http import models
            import socket

            # Check if Qdrant is running by attempting to connect to the host
            # Use IP address instead of hostname to avoid DNS issues
            host = "127.0.0.1"  # Force IP address to avoid DNS issues
            port = settings.QDRANT_PORT if hasattr(settings, 'QDRANT_PORT') else 6333

            # Override URL with direct IP address
            qdrant_url = "http://127.0.0.1:6333"
            logger.info(f"Will connect to Qdrant at {qdrant_url}")

            # Try to connect to the host
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(1)
            try:
                s.connect((host, port))
                s.close()
                qdrant_available = True
            except (socket.error, socket.timeout):
                logger.warning(f"Qdrant server not available at {host}:{port}")
                qdrant_available = False
                s.close()

            if qdrant_available:
                self.client = QdrantClient(
                    url=qdrant_url,  # Use our hardcoded URL
                    api_key=settings.QDRANT_API_KEY,
                )

                # Create collections if they don't exist
                self._create_collections()

                self.is_available = True
                logger.info("Connected to Qdrant for semantic memory")
            else:
                logger.warning("Qdrant server not available, using fallback memory")
                self.client = None
                self.is_available = False
        except Exception as e:
            logger.error(f"Failed to connect to Qdrant: {str(e)}")
            self.client = None
            self.is_available = False

    def _create_collections(self):
        """
        Create Qdrant collections if they don't exist.
        """
        from qdrant_client.http import models

        # Check if collections exist
        try:
            collections = self.client.get_collections().collections
            collection_names = [c.name for c in collections]
        except Exception as e:
            logger.warning(f"Failed to get collections: {str(e)}")
            collection_names = []

        # Use a fallback dimension if embedding model failed to load
        embedding_dim = self.embedding_dimension
        if embedding_dim == 0:
            logger.warning("Embedding model not available, using fallback dimension for Qdrant collections")
            embedding_dim = 384  # Common dimension for sentence transformers

        # Helper function to create or recreate collection
        def create_collection_safe(collection_name: str):
            """Create collection with proper error handling and dimension validation."""
            try:
                # Check if collection exists and has correct dimension
                if collection_name in collection_names:
                    try:
                        # Get collection info to check dimension
                        collection_info = self.client.get_collection(collection_name)
                        existing_dim = None

                        # Handle different API formats for getting vector size
                        if hasattr(collection_info, 'config') and hasattr(collection_info.config, 'params'):
                            if hasattr(collection_info.config.params, 'vectors'):
                                # New API format
                                vectors_config = collection_info.config.params.vectors
                                if isinstance(vectors_config, dict) and 'embedding' in vectors_config:
                                    existing_dim = vectors_config['embedding'].size
                                elif hasattr(vectors_config, 'size'):
                                    existing_dim = vectors_config.size
                            elif hasattr(collection_info.config.params, 'vector'):
                                # Old API format
                                existing_dim = collection_info.config.params.vector.size

                        if existing_dim and existing_dim != embedding_dim:
                            logger.warning(f"Collection '{collection_name}' has dimension {existing_dim}, expected {embedding_dim}. Recreating...")
                            # Delete and recreate with correct dimension
                            self.client.delete_collection(collection_name)
                            collection_names.remove(collection_name)
                        else:
                            logger.info(f"Collection '{collection_name}' already exists with correct dimension")
                            return True
                    except Exception as e:
                        logger.warning(f"Failed to check collection dimension: {str(e)}")
                        # Continue with creation attempt

                # Create collection if it doesn't exist
                if collection_name not in collection_names:
                    try:
                        # Try the new API format first
                        self.client.create_collection(
                            collection_name=collection_name,
                            vectors={
                                "embedding": models.VectorParams(
                                    size=embedding_dim,
                                    distance=models.Distance.COSINE
                                )
                            }
                        )
                        logger.info(f"Created '{collection_name}' collection in Qdrant using new API format")
                        return True
                    except Exception as e:
                        logger.warning(f"Failed to create collection with new API format: {str(e)}")
                        # Fall back to the old API format
                        try:
                            self.client.create_collection(
                                collection_name=collection_name,
                                vectors_config=models.VectorParams(
                                    size=embedding_dim,
                                    distance=models.Distance.COSINE
                                )
                            )
                            logger.info(f"Created '{collection_name}' collection in Qdrant using old API format")
                            return True
                        except Exception as e2:
                            logger.error(f"Failed to create '{collection_name}' collection: {str(e2)}")
                            return False
                return True
            except Exception as e:
                logger.error(f"Error creating collection '{collection_name}': {str(e)}")
                return False

        # Create both collections
        create_collection_safe("memory")
        create_collection_safe("knowledge")

    def embed_text(self, text: str) -> List[float]:
        """
        Generate embeddings for text.

        Args:
            text: The text to embed

        Returns:
            Embedding vector or a fallback random vector if model not available
        """
        try:
            # The encode method should work for both SentenceTransformer and SimpleEmbedder
            embedding = self.embedding_model.encode(text)

            # Convert to list if it's a numpy array
            if isinstance(embedding, np.ndarray):
                return embedding.tolist()

            # If it's already a list, return it
            if isinstance(embedding, list):
                return embedding

            # Otherwise, convert to list
            return list(embedding)
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")

            # Generate a random embedding as fallback
            # Make sure numpy is imported at the top of the file
            import hashlib

            # Use a fixed dimension
            fallback_dim = 384  # Common dimension for embeddings

            # Use a hash of the text to ensure the same text gets the same embedding
            # Make sure the seed is within the valid range (0 to 2^32-1)
            text_hash = int(hashlib.md5(text.encode()).hexdigest(), 16) % (2**32 - 1)
            np.random.seed(text_hash)

            # Generate random embedding and normalize
            random_embedding = np.random.normal(0, 0.1, fallback_dim)
            random_embedding = random_embedding / np.linalg.norm(random_embedding)

            return random_embedding.tolist()

    def store_memory(
        self,
        text: str,
        user_id: str,
        memory_type: str = "conversation",
        metadata: Optional[Dict[str, Any]] = None,
        conversation_id: Optional[str] = None
    ) -> str:
        """
        Store a memory.

        Args:
            text: The text to store
            user_id: The user ID
            memory_type: Type of memory (conversation, feedback, etc.)
            metadata: Optional metadata
            conversation_id: Optional conversation ID

        Returns:
            Memory ID
        """
        if not self.is_available:
            logger.warning("Qdrant not available, memory not stored")
            return str(uuid.uuid4())

        try:
            # Generate embedding
            embedding = self.embed_text(text)

            # Create memory ID
            memory_id = str(uuid.uuid4())

            # Create payload
            payload = {
                "text": text,
                "user_id": user_id,
                "memory_type": memory_type,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }

            if conversation_id:
                payload["conversation_id"] = conversation_id

            # Store in Qdrant
            from qdrant_client.http import models

            self.client.upsert(
                collection_name="memory",
                points=[
                    models.PointStruct(
                        id=memory_id,
                        vector=embedding,
                        payload=payload
                    )
                ]
            )

            return memory_id
        except Exception as e:
            logger.error(f"Error storing memory in Qdrant: {str(e)}")
            return str(uuid.uuid4())

    def retrieve_memories(
        self,
        query: str,
        user_id: Optional[str] = None,
        memory_type: Optional[str] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Retrieve memories by semantic similarity.

        Args:
            query: The query text
            user_id: Optional user ID filter
            memory_type: Optional memory type filter
            limit: Maximum number of memories to return

        Returns:
            List of memories
        """
        if not self.is_available:
            logger.warning("Qdrant not available, returning empty memories")
            return []

        try:
            # Generate embedding
            embedding = self.embed_text(query)

            # Create filter
            from qdrant_client.http import models

            filter_conditions = []
            if user_id:
                filter_conditions.append(
                    models.FieldCondition(
                        key="user_id",
                        match=models.MatchValue(value=user_id)
                    )
                )

            if memory_type:
                filter_conditions.append(
                    models.FieldCondition(
                        key="memory_type",
                        match=models.MatchValue(value=memory_type)
                    )
                )

            search_filter = None
            if filter_conditions:
                search_filter = models.Filter(
                    must=filter_conditions
                )

            # Search in Qdrant
            search_params = {
                "collection_name": "memory",
                "query_vector": embedding,
                "limit": limit
            }

            # Only add filter if it's not None
            if search_filter:
                try:
                    # Try with 'query_filter' parameter (older versions)
                    results = self.client.search(
                        collection_name="memory",
                        query_vector=embedding,
                        limit=limit,
                        query_filter=search_filter
                    )
                except Exception as filter_error:
                    logger.warning(f"Error using query_filter: {str(filter_error)}")
                    # Fall back to no filter if there's an error
                    results = self.client.search(**search_params)
            else:
                # No filter needed
                results = self.client.search(**search_params)

            # Format results
            memories = []
            for res in results:
                memory = res.payload
                memory["id"] = res.id
                memory["score"] = res.score
                memories.append(memory)

            return memories
        except Exception as e:
            logger.error(f"Error retrieving memories from Qdrant: {str(e)}")
            return []

    def store_knowledge(
        self,
        text: str,
        source: str,
        knowledge_type: str = "document",
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Store knowledge for RAG.

        Args:
            text: The text to store
            source: Source of the knowledge
            knowledge_type: Type of knowledge (document, fact, etc.)
            metadata: Optional metadata

        Returns:
            Knowledge ID
        """
        if not self.is_available:
            logger.warning("Qdrant not available, knowledge not stored")
            return str(uuid.uuid4())

        try:
            # Generate embedding
            embedding = self.embed_text(text)

            # Create knowledge ID
            knowledge_id = str(uuid.uuid4())

            # Create payload
            payload = {
                "text": text,
                "source": source,
                "knowledge_type": knowledge_type,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }

            # Store in Qdrant
            from qdrant_client.http import models

            self.client.upsert(
                collection_name="knowledge",
                points=[
                    models.PointStruct(
                        id=knowledge_id,
                        vector=embedding,
                        payload=payload
                    )
                ]
            )

            return knowledge_id
        except Exception as e:
            logger.error(f"Error storing knowledge in Qdrant: {str(e)}")
            return str(uuid.uuid4())

    def retrieve_context(
        self,
        query: str,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        limit: int = 5
    ) -> Dict[str, Any]:
        """
        Retrieve context for RAG.

        Args:
            query: The query text
            user_id: Optional user ID for personalization
            conversation_id: Optional conversation ID for context
            limit: Maximum number of context items to return

        Returns:
            Context dictionary with memories and knowledge
        """
        context = {
            "memories": [],
            "knowledge": [],
            "combined": ""
        }

        if not self.is_available:
            logger.warning("Qdrant not available, returning empty context")
            return context

        try:
            # Generate embedding
            embedding = self.embed_text(query)

            # Retrieve personal memories if user_id provided
            if user_id:
                from qdrant_client.http import models

                # Create filter for user memories
                filter_conditions = [
                    models.FieldCondition(
                        key="user_id",
                        match=models.MatchValue(value=user_id)
                    )
                ]

                if conversation_id:
                    # Add conversation filter if provided
                    filter_conditions.append(
                        models.FieldCondition(
                            key="conversation_id",
                            match=models.MatchValue(value=conversation_id)
                        )
                    )

                memory_filter = models.Filter(must=filter_conditions)

                # Search memories
                try:
                    # Try with 'filter' parameter first (newer versions)
                    memory_results = self.client.search(
                        collection_name="memory",
                        query_vector=embedding,
                        limit=limit,
                        filter=memory_filter
                    )
                except TypeError as e:
                    # Fall back to 'query_filter' parameter (older versions)
                    logger.info(f"Falling back to query_filter parameter: {str(e)}")
                    memory_results = self.client.search(
                        collection_name="memory",
                        query_vector=embedding,
                        limit=limit,
                        query_filter=memory_filter
                    )

                # Format memory results
                for res in memory_results:
                    memory = res.payload
                    memory["id"] = res.id
                    memory["score"] = res.score
                    context["memories"].append(memory)

            # Retrieve knowledge
            knowledge_results = self.client.search(
                collection_name="knowledge",
                query_vector=embedding,
                limit=limit
            )

            # Format knowledge results
            for res in knowledge_results:
                knowledge = res.payload
                knowledge["id"] = res.id
                knowledge["score"] = res.score
                context["knowledge"].append(knowledge)

            # Combine context into a single string
            combined = []

            # Add memories
            if context["memories"]:
                combined.append("## Personal Context")
                for i, memory in enumerate(context["memories"]):
                    combined.append(f"{i+1}. {memory['text']}")

            # Add knowledge
            if context["knowledge"]:
                combined.append("\n## Knowledge")
                for i, knowledge in enumerate(context["knowledge"]):
                    combined.append(f"{i+1}. {knowledge['text']} (Source: {knowledge['source']})")

            context["combined"] = "\n".join(combined)

            return context
        except Exception as e:
            logger.error(f"Error retrieving context from Qdrant: {str(e)}")
            return context
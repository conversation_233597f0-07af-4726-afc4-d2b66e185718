"""
Configuration file for open-source models used in the application.
All models listed here should be publicly available without requiring authentication.
"""

# Dictionary of essential models by task - ONLY TRULY PUBLIC MODELS
ESSENTIAL_MODELS = {
    # NLP Models - Smaller and definitely public models
    "chat": [
        {"model_id": "TinyLlama/TinyLlama-1.1B-Chat-v1.0", "alias": "tinyllama"}
    ],
    "text-generation": [
        {"model_id": "TinyLlama/TinyLlama-1.1B-Chat-v1.0", "alias": "tinyllama"},
        {"model_id": "gpt2", "alias": "gpt2-small"},
        {"model_id": "distilgpt2", "alias": "distilgpt2"}
    ],
    "text2text": [
        {"model_id": "t5-small", "alias": "t5-small"},
        {"model_id": "t5-base", "alias": "t5-base"}
    ],
    "summarization": [
        {"model_id": "sshleifer/distilbart-cnn-6-6", "alias": "distilbart-summarization"}
    ],
    "question-answering": [
        {"model_id": "distilbert-base-cased-distilled-squad", "alias": "distilbert-qa"}
    ],
    "text-classification": [
        {"model_id": "distilbert-base-uncased", "alias": "distilbert-classifier"},
        {"model_id": "bert-base-uncased", "alias": "bert-classifier"}
    ],
    "sentence-similarity": [
        {"model_id": "sentence-transformers/all-MiniLM-L6-v2", "alias": "all-minilm"}
    ],
    "translation": [
        {"model_id": "t5-small", "alias": "t5-translation"}
    ],
    "zero-shot-classification": [
        {"model_id": "cross-encoder/nli-distilroberta-base", "alias": "distilroberta-nli"}
    ],

    # Vision Models - Smaller and definitely public models
    "image-classification": [
        {"model_id": "google/vit-base-patch16-224", "alias": "vit-base"}
    ],
    "object-detection": [
        {"model_id": "hustvl/yolos-small", "alias": "yolos-small"}
    ],
    "image-segmentation": [
        {"model_id": "facebook/detr-resnet-50-panoptic", "alias": "detr-panoptic"}
    ],
    "text-to-image": [
        {"model_id": "runwayml/stable-diffusion-v1-5", "alias": "stable-diffusion-1-5"}
    ],
    "image-to-text": [
        {"model_id": "nlpconnect/vit-gpt2-image-captioning", "alias": "vit-gpt2-captioning"}
    ],
    "image-feature-extraction": [
        {"model_id": "openai/clip-vit-base-patch32", "alias": "clip-vit-b32"}
    ],

    # Audio Models
    "text-to-speech": [
        {"model_id": "espnet/kan-bayashi_ljspeech_vits", "alias": "vits-ljspeech"}
    ],
    "speech-to-text": [
        {"model_id": "openai/whisper-tiny", "alias": "whisper-tiny"}
    ],
    "audio-classification": [
        {"model_id": "superb/wav2vec2-base-superb-ks", "alias": "wav2vec2-ks"}
    ],
    "audio-embedding": [
        {"model_id": "facebook/wav2vec2-base-960h", "alias": "wav2vec2-base"}
    ],

    # Multimodal Models
    "multimodal": [
        {"model_id": "nlpconnect/vit-gpt2-image-captioning", "alias": "vit-gpt2-multimodal"}
    ],
    "document-qa": [
        {"model_id": "microsoft/layoutlmv2-base-uncased", "alias": "layoutlmv2"}
    ]
}

# Model loading priorities (1-10, higher = more important)
MODEL_PRIORITIES = {
    "chat": 10,
    "text-generation": 9,
    "speech-to-text": 8,
    "text-to-speech": 8,
    "sentence-similarity": 9,
    "image-classification": 7,
    "object-detection": 7,
    "multimodal": 6,
    "document-qa": 6,
    "translation": 5,
    "summarization": 5,
    "question-answering": 5,
    "text-classification": 4,
    "zero-shot-classification": 4,
    "image-segmentation": 3,
    "text-to-image": 3,
    "image-to-text": 3,
    "image-feature-extraction": 2,
    "audio-classification": 2,
    "text2text": 1
}

# Model memory requirements (in MB)
MODEL_MEMORY_REQUIREMENTS = {
    "TinyLlama/TinyLlama-1.1B-Chat-v1.0": 600,
    "gpt2": 500,
    "google/flan-t5-small": 300,
    "google/flan-t5-base": 800,
    "facebook/bart-large-cnn": 600,
    "google/pegasus-xsum": 600,
    "distilbert-base-cased-distilled-squad": 300,
    "deepset/roberta-base-squad2": 500,
    "distilbert-base-uncased": 250,
    "xlm-roberta-base": 500,
    "sentence-transformers/all-MiniLM-L6-v2": 100,
    "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2": 150,
    "facebook/nllb-200-distilled-600M": 1200,
    "Helsinki-NLP/opus-mt-en-de": 300,
    "facebook/bart-large-mnli": 600,
    "joeddav/xlm-roberta-large-xnli": 700,
    "google/efficientnet_b0": 30,
    "facebook/convnext-tiny-224": 40,
    "hustvl/yolos-tiny": 30,
    "facebook/detr-resnet-50": 90,
    "facebook/detr-resnet-50-panoptic": 100,
    "nvidia/segformer-b0-finetuned-ade-512-512": 120,
    "stabilityai/stable-diffusion-2-1-base": 2500,
    "runwayml/stable-diffusion-v1-5": 2500,
    "Salesforce/blip-image-captioning-base": 1500,
    "nlpconnect/vit-gpt2-image-captioning": 1000,
    "openai/clip-vit-base-patch32": 300,
    "facebook/dinov2-small": 300,
    "espnet/kan-bayashi_ljspeech_vits": 100,
    "facebook/fastspeech2-en-ljspeech": 100,
    "openai/whisper-tiny": 40,
    "facebook/wav2vec2-base-960h": 100,
    "superb/wav2vec2-base-superb-ks": 50,
    "MIT/ast-finetuned-audioset-10-10-0.4593": 50,
    "Salesforce/blip-vqa-base": 2000,
    "microsoft/git-base": 2000,
    "naver-clova-ix/donut-base": 1300,
    "microsoft/layoutlmv2-base-uncased": 1300
}

# Default models for each task
DEFAULT_MODELS = {
    "text-generation": "gpt2",
    "chat": "TinyLlama/TinyLlama-1.1B-Chat-v1.0",
    "text2text": "t5-small",
    "summarization": "sshleifer/distilbart-cnn-6-6",
    "question-answering": "distilbert-base-cased-distilled-squad",
    "text-classification": "distilbert-base-uncased",
    "sentence-similarity": "sentence-transformers/all-MiniLM-L6-v2",
    "translation": "t5-small",
    "zero-shot-classification": "cross-encoder/nli-distilroberta-base",
    "image-classification": "google/vit-base-patch16-224",
    "object-detection": "hustvl/yolos-small",
    "image-segmentation": "facebook/detr-resnet-50-panoptic",
    "text-to-image": "runwayml/stable-diffusion-v1-5",
    "image-to-text": "nlpconnect/vit-gpt2-image-captioning",
    "image-feature-extraction": "openai/clip-vit-base-patch32",
    "text-to-speech": "espnet/kan-bayashi_ljspeech_vits",
    "speech-to-text": "openai/whisper-tiny",
    "audio-classification": "superb/wav2vec2-base-superb-ks",
    "audio-embedding": "facebook/wav2vec2-base-960h",
    "multimodal": "nlpconnect/vit-gpt2-image-captioning",
    "document-qa": "microsoft/layoutlmv2-base-uncased"
}
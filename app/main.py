"""
Main FastAPI application.
"""
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time
import uuid
from loguru import logger
from app.utils.logging_config import configure_logging

# Configure logging with colorful output
configure_logging()
import os
import asyncio
from datetime import datetime

from app.api.routes import user, billing, training, security, integration, conversation
from app.api.routes import health, feature_flags, metrics, vector_search
from app.api.routes import api_router
from app.api.routes import model_training_v2
from app.api.routes import enhanced_chat
from app.api.routes.users import router as users_router
from app.voice.routes import router as voice_router
from app.api.auth import get_current_user
from app.utils.config import settings
from app.db.models import User
from app.data.user import UserStorage
from app.utils.model_manager import model_manager
from app.utils.model_system_initializer import model_system

# Create FastAPI app
app = FastAPI(
    title="SimLLM API",
    description="API for SimLLM - AI model training and inference",
    version="1.0.0",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limiting middleware
from app.middleware.rate_limit import RateLimitMiddleware
app.add_middleware(RateLimitMiddleware)

# Add content moderation middleware
from app.moderation.middleware import ContentModerationMiddleware
app.add_middleware(
    ContentModerationMiddleware,
    excluded_paths=["/docs", "/redoc", "/openapi.json", "/health", "/metrics"],
    excluded_methods=["GET", "HEAD", "OPTIONS"],
    log_moderation=True
)

# Initialize user storage
user_storage = UserStorage()

# Initialize Redis client for content moderation
import redis
from app.moderation.content_filter import initialize_content_filter
try:
    redis_client = redis.Redis(
        host="localhost",  # Update with your Redis host
        port=6379,         # Update with your Redis port
        db=0,
        decode_responses=False
    )
    # Initialize content filter with Redis client
    initialize_content_filter(redis_client)
    logger.info("Content moderation system initialized with Redis")
except Exception as e:
    logger.error(f"Error initializing Redis client: {e}")
    # Initialize content filter without Redis
    initialize_content_filter()
    logger.info("Content moderation system initialized without Redis")

# Initialize model system on startup
@app.on_event("startup")
async def startup_event():
    logger.info("Starting application initialization...")
    
    try:
        # 1. Initialize model system
        logger.info("Initializing model system...")
        await model_system.initialize()
        
        # 2. Initialize models from MinIO
        logger.info("Starting model initialization from MinIO...")
        from app.services.model_initializer import model_initializer
        await model_initializer.initialize_models()
        
        # 3. Initialize the LLM manager with the preloaded models
        logger.info("Initializing LLM manager...")
        from app.llm.manager import LLMManager
        llm_manager = LLMManager()
        
        # 4. Initialize voice components
        logger.info("Initializing voice components...")
        from app.voice.speech_to_text import SpeechToText
        from app.voice.text_to_speech import TextToSpeech
        stt = SpeechToText()
        tts = TextToSpeech()
        
        # 5. Preload models in background
        logger.info("Starting background model preloading...")
        from app.utils.model_preloader import preload_models
        from app.utils.model_manager import model_manager
        model_manager.start_background_loading()
        preload_models()
        
        # 6. Initialize NLP components
        logger.info("Initializing NLP components...")
        from app.nlp.spell_checker import spell_checker
        from app.nlp.predictive_text import predictive_text
        
        # Initialize spell checker
        try:
            await spell_checker.initialize()
            logger.info("Spell checker initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize spell checker: {e}", exc_info=True)
        
        # Initialize predictive text
        try:
            await predictive_text.initialize()
            logger.info("Predictive text initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize predictive text: {e}", exc_info=True)
        
        logger.info("Application initialization completed successfully")
        try:
            # Import necessary modules
            from app.services.cli import sync_models
            from app.utils.model_manager import model_manager
            from app.db.mongodb import mongodb_manager
            
            # Create a simple object with the required attributes for TinyLlama
            class TinyLlamaArgs:
                def __init__(self):
                    self.model_id = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
                    self.task = "text-generation"  # Use text-generation as the primary task
                    self.force = False
                    self.skip_wait = False
                    self.all_models = False
                    self.quantize = False
                    self.compile = False
            
            # Create the arguments object
            tinyllama_args = TinyLlamaArgs()
            
            # Log that we're loading TinyLlama
            logger.info(f"Ensuring TinyLlama model is loaded: {tinyllama_args.model_id}")
            
            # First, register the model in MongoDB for the chat task
            try:
                # Get the model repository collection
                model_repo_collection = mongodb_manager.get_collection("model_repository")
                
                # Get the local path for the model
                model_cache_dir = os.path.join(settings.MODEL_CACHE_DIR, tinyllama_args.model_id.replace("/", "--"))
                
                # Register TinyLlama primarily for text-generation task, with chat as a secondary task
                # First for text-generation task (primary)
                model_repo_collection.update_one(
                    {
                        "model_id": tinyllama_args.model_id,
                        "type": "text-generation"
                    },
                    {"$set": {
                        "model_id": tinyllama_args.model_id,
                        "name": "TinyLlama-1.1B-Chat",
                        "type": "text-generation",
                        "task": "text-generation",
                        "description": f"Text generation model: {tinyllama_args.model_id}",
                        "provider": "local",
                        "status": "available",
                        "updated_at": datetime.utcnow().isoformat(),
                        "priority": 10,
                        "local_path": model_cache_dir,  # Add the local path
                        "metadata": {
                            "storage": "local",
                            "model_key": f"text-generation:{tinyllama_args.model_id}"
                        }
                    }},
                    upsert=True
                )
                
                # Then for chat task (as a specialized use case of text-generation)
                model_repo_collection.update_one(
                    {
                        "model_id": tinyllama_args.model_id,
                        "type": "chat"
                    },
                    {"$set": {
                        "model_id": tinyllama_args.model_id,
                        "name": "TinyLlama-1.1B-Chat",
                        "type": "chat",
                        "task": "text-generation",  # Note: task is still text-generation
                        "description": f"Chat model: {tinyllama_args.model_id}",
                        "provider": "local",
                        "status": "available",
                        "updated_at": datetime.utcnow().isoformat(),
                        "priority": 10,
                        "local_path": model_cache_dir,  # Add the local path
                        "metadata": {
                            "storage": "local",
                            "model_key": f"text-generation:{tinyllama_args.model_id}"  # Use the same model key
                        }
                    }},
                    upsert=True
                )
                
                logger.info(f"Registered {tinyllama_args.model_id} primarily for text-generation with chat as a secondary task in MongoDB")
            except Exception as e:
                logger.error(f"Error registering model in MongoDB: {str(e)}")
            
            # Call the sync_models function with the TinyLlama arguments
            # Create a named task and store it to prevent the "coroutine was never awaited" warning
            sync_task = asyncio.create_task(sync_models(tinyllama_args))
            # Add a done callback to handle any exceptions
            sync_task.add_done_callback(
                lambda t: logger.error(f"TinyLlama sync task error: {t.exception()}") if t.exception() else None
            )
            
            # Now explicitly preload the model into memory
            async def preload_tinyllama():
                try:
                    # Wait for the sync task to complete
                    await sync_task
                    
                    # Now explicitly load the model into memory
                    logger.info("Preloading TinyLlama model into memory...")
                    
                    # Check if model is already loaded
                    model_key = f"chat:{tinyllama_args.model_id}"
                    if model_key in model_manager.models:
                        logger.info(f"TinyLlama model already loaded in memory")
                        return
                    
                    # Load the model using the text generation loader
                    from app.utils.text_generation_loader import load_text_generation_model
                    
                    # Load the model
                    model = load_text_generation_model(
                        model_id=tinyllama_args.model_id,
                        models_dir=model_manager.models_dir,
                        check_minio_fn=model_manager.check_model_in_minio,
                        download_from_minio_fn=model_manager.download_model_from_minio,
                        upload_to_minio_fn=model_manager.upload_model_to_minio
                    )
                    
                    # Register the model primarily for text-generation task
                    model_manager.register_model(
                        model_id=tinyllama_args.model_id,
                        task="text-generation",
                        model_obj=model)
                    
                    # Also register for chat task (as a specialized use case of text-generation)
                    # This ensures backward compatibility with code that expects chat models
                    model_manager.register_model(
                        model_id=tinyllama_args.model_id,
                        task="chat",
                        model_obj=model)
                    
                    logger.info("TinyLlama model successfully preloaded and registered primarily for text-generation with chat as a secondary task")
                except Exception as e:
                    logger.error(f"Error preloading TinyLlama model: {str(e)}")
            
            # Create a task for preloading
            preload_task = asyncio.create_task(preload_tinyllama())
            preload_task.add_done_callback(
                lambda t: logger.error(f"TinyLlama preload error: {t.exception()}") if t.exception() else None
            )
            
            logger.info("TinyLlama model loading and preloading triggered in background")
        except Exception as e:
            logger.error(f"Error ensuring TinyLlama is loaded: {str(e)}")
        
        # Start model health checks
        from app.utils.model_health import model_health
        model_health.start()
        logger.info("Started model health checks")
        
        # Start feature flag auto-reload
        from app.utils.feature_flags import feature_flags
        feature_flags.start_auto_reload()
        logger.info("Started feature flag auto-reload")
        
        # Register models from the model abstraction layer with the legacy model manager
        # This ensures backward compatibility with existing code
        try:
            # Get chat model from abstraction layer
            chat_model = model_system.get_model_for_task("chat")
            if chat_model:
                logger.info("Registering chat model from abstraction layer")
                model_manager.register_model(
                    model_id="chat-model",
                    task="chat",
                    model_obj=chat_model)
                
                # Also register with specific names used in the LLM manager
                model_manager.register_model(
                    model_id="gpt-3.5-turbo",  # Map to OpenAI model name for compatibility
                    task="chat",
                    model_obj=chat_model)
                
                # Register models for chat if they exist
                try:
                    from transformers import AutoModelForCausalLM, AutoTokenizer
                    import transformers
                    
                    # Log available models
                    logger.info("Checking available models for chat...")
                    available_models = model_manager.list_models()
                    logger.info(f"Available models: {list(available_models.keys())}")
                    
                    # Try to load phi-2 model
                    phi2_path = "./data/models/phi-2"
                    if os.path.exists(phi2_path):
                        logger.info("Loading phi-2 model for chat")
                        # Load tokenizer
                        phi2_tokenizer = AutoTokenizer.from_pretrained(phi2_path)
                        # Create pipeline
                        phi2_pipeline = transformers.pipeline(
                            "text-generation",
                            model=phi2_path,
                            tokenizer=phi2_tokenizer,
                            device="cpu"
                        )
                        # Register the model for both chat and text-generation tasks
                        model_manager.register_model(
                            model_id="phi-2",
                            task="chat",
                            model_obj=phi2_pipeline)
                        
                        # Also register for text-generation task
                        model_manager.register_model(
                            model_id="phi-2",
                            task="text-generation",
                            model_obj=phi2_pipeline)
                        logger.info("Phi-2 model registered for chat successfully")
                    
                    # Try to load models from MinIO
                    logger.info("Checking for text-generation models in MinIO...")
                    try:
                        # List objects in the text-generation directory in MinIO
                        text_gen_objects = list(model_manager.minio_client.list_objects(
                            model_manager.minio_bucket,
                            prefix="text-generation/",
                            recursive=False
                        ))
                        
                        if text_gen_objects:
                            logger.info(f"Found {len(text_gen_objects)} text-generation models in MinIO")
                            
                            # Load each model
                            for obj in text_gen_objects:
                                try:
                                    # Extract model ID from object name
                                    model_path = obj.object_name
                                    model_id = model_path.split('/')[-1] if '/' in model_path else model_path
                                    
                                    if not model_id:
                                        continue
                                        
                                    logger.info(f"Loading text-generation model {model_id} from MinIO")
                                    
                                    # Download and load the model
                                    model_obj = model_manager._load_text_generation_model(model_id)
                                    
                                    if model_obj:
                                        # Register the model for both chat and text-generation tasks
                                        model_manager.register_model(
                                            model_id=model_id,
                                            task="text-generation",
                                            model_obj=model_obj)
                                        
                                        # Also register for chat if it's a chat model
                                        if "chat" in model_id.lower():
                                            model_manager.register_model(
                                                model_id=model_id,
                                                task="chat",
                                                model_obj=model_obj)
                                            
                                        logger.info(f"Model {model_id} registered successfully")
                                except Exception as model_error:
                                    logger.error(f"Error loading model {model_id}: {str(model_error)}")
                        else:
                            logger.warning("No text-generation models found in MinIO")
                    except Exception as minio_error:
                        logger.error(f"Error checking MinIO for text-generation models: {str(minio_error)}")
                    
                    # Try to load GPT-2 model as primary model
                    gpt2_path = "./data/models/gpt2"
                    if os.path.exists(gpt2_path):
                        logger.info("Loading GPT-2 model for chat and text generation")
                        try:
                            # Load tokenizer
                            gpt2_tokenizer = AutoTokenizer.from_pretrained(gpt2_path)
                            # Load model directly
                            gpt2_model = AutoModelForCausalLM.from_pretrained(gpt2_path, torch_dtype='auto')
                            # Create pipeline
                            gpt2_pipeline = transformers.pipeline(
                                "text-generation",
                                model=gpt2_model,
                                tokenizer=gpt2_tokenizer,
                                device="cpu"
                            )
                            # Register the model for both chat and text-generation tasks
                            model_manager.register_model(
                                model_id="gpt2",
                                task="chat",
                                model_obj=gpt2_pipeline)
                            # Also register for text-generation task
                            model_manager.register_model(
                                model_id="gpt2",
                                task="text-generation",
                                model_obj=gpt2_pipeline)
                            logger.info("GPT-2 model registered for chat and text generation successfully")
                        except Exception as gpt2_error:
                            logger.error(f"Error loading GPT-2 model: {str(gpt2_error)}")
                    
                    # Try to load DistilGPT2 model (as fallback)
                    distilgpt2_path = "./data/models/distilgpt2"
                    if os.path.exists(distilgpt2_path):
                        logger.info("Loading DistilGPT2 model for chat")
                        # Load tokenizer
                        distilgpt2_tokenizer = AutoTokenizer.from_pretrained(distilgpt2_path)
                        # Create pipeline
                        distilgpt2_pipeline = transformers.pipeline(
                            "text-generation",
                            model=distilgpt2_path,
                            tokenizer=distilgpt2_tokenizer,
                            device="cpu"
                        )
                        # Register the model for both chat and text-generation tasks
                        model_manager.register_model(
                            model_id="distilgpt2",
                            task="chat",
                            model_obj=distilgpt2_pipeline)
                        # Also register for text-generation task
                        model_manager.register_model(
                            model_id="distilgpt2",
                            task="text-generation",
                            model_obj=distilgpt2_pipeline)
                        logger.info("DistilGPT2 model registered for chat successfully")
                except Exception as e:
                    logger.error(f"Error loading models: {str(e)}")
                
                # Log all available models after registration
                try:
                    available_models = model_manager.list_models()
                    logger.info(f"Final available models: {list(available_models.keys())}")
                except Exception as e:
                    logger.error(f"Error listing models: {str(e)}")
                
                logger.info("Chat models registered successfully")
            
            # Get speech-to-text model from abstraction layer
            stt_model = model_system.get_model_for_task("speech-to-text")
            if stt_model:
                logger.info("Registering speech-to-text model from abstraction layer")
                model_manager.register_model(
                    model_id="whisper",
                    task="speech-to-text",
                    model_obj=stt_model)
                
                logger.info("Speech-to-text model registered successfully")
            
            # Get text-to-speech model from abstraction layer
            tts_model = model_system.get_model_for_task("text-to-speech")
            if tts_model:
                logger.info("Registering text-to-speech model from abstraction layer")
                model_manager.register_model(
                    model_id="tts",
                    task="text-to-speech",
                    model_obj=tts_model)
                
                logger.info("Text-to-speech model registered successfully")
        except Exception as e:
            logger.error(f"Error registering models from abstraction layer: {str(e)}")
        
        # For backward compatibility, also load TinyLlama directly if available
        try:
            from app.utils.config import settings
            import transformers
            from transformers import AutoModelForCausalLM, AutoTokenizer
            import torch
            
            # TinyLlama model ID
            tinyllama_model_id = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
            
            # Check if model is already loaded
            available_models = model_manager.list_models()
            if "chat:TinyLlama/TinyLlama-1.1B-Chat-v1.0" in available_models:
                logger.info("TinyLlama model already loaded in model manager.")
            else:
                # Load TinyLlama model from local path
                model_path = getattr(settings, "LOCAL_MODEL_PATH", "./data/models/tinyllama")
                
                # If model doesn't exist locally, download it
                if not os.path.exists(model_path):
                    logger.info(f"TinyLlama model path not found: {model_path}. Downloading from Hugging Face...")
                    
                    try:
                        # Create directory if it doesn't exist
                        os.makedirs(model_path, exist_ok=True)
                        
                        # Download model and tokenizer directly from Hugging Face
                        logger.info(f"Downloading TinyLlama model: {tinyllama_model_id}")
                        
                        # Download tokenizer
                        tokenizer = AutoTokenizer.from_pretrained(
                            tinyllama_model_id,
                            trust_remote_code=True
                        )
                        
                        # Download model
                        model = AutoModelForCausalLM.from_pretrained(
                            tinyllama_model_id,
                            torch_dtype=torch.float32,
                            trust_remote_code=True,
                            low_cpu_mem_usage=True
                        )
                        
                        # Save model and tokenizer locally
                        logger.info(f"Saving TinyLlama model to {model_path}")
                        model.save_pretrained(model_path)
                        tokenizer.save_pretrained(model_path)
                        
                        logger.info("TinyLlama model downloaded and saved successfully!")
                    except Exception as download_e:
                        logger.error(f"Error downloading TinyLlama model: {str(download_e)}")
                        # Continue with loading from Hugging Face directly
                
                # Try to load the model (either from local path or directly from Hugging Face)
                try:
                    if os.path.exists(model_path) and os.listdir(model_path):
                        logger.info(f"Loading TinyLlama model from local path: {model_path}")
                        
                        # Load tokenizer
                        tokenizer = AutoTokenizer.from_pretrained(model_path)
                        
                        # Create a pipeline
                        pipeline = transformers.pipeline(
                            "text-generation",
                            model=model_path,
                            tokenizer=tokenizer,
                            device="cpu"
                        )
                    else:
                        logger.info(f"Loading TinyLlama model directly from Hugging Face: {tinyllama_model_id}")
                        
                        # Create a pipeline directly from Hugging Face
                        pipeline = transformers.pipeline(
                            "text-generation",
                            model=tinyllama_model_id,
                            device="cpu",
                            trust_remote_code=True
                        )
                    
                    # Register the model
                    model_manager.register_model(
                        model_id="tinyllama",
                        task="chat",
                        model_obj=pipeline)
                    
                    # Also register with specific names used in the LLM manager
                    model_manager.register_model(
                        model_id="local:tinyllama",
                        task="chat",
                        model_obj=pipeline)
                    
                    # Register with the full model ID
                    model_manager.register_model(
                        model_id=tinyllama_model_id,
                        task="chat",
                        model_obj=pipeline)
                    
                    logger.info("TinyLlama model registered successfully")
                except Exception as load_e:
                    logger.error(f"Error loading TinyLlama model: {str(load_e)}")
        except Exception as e:
            logger.error(f"Error handling TinyLlama model: {str(e)}")
            
        logger.info("Model system initialized successfully")
    except Exception as e:
        logger.error(f"Error in startup event: {str(e)}")

# Add middleware for request logging and session tracking
@app.middleware("http")
async def log_requests(request: Request, call_next):
    # Generate request ID
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    
    # Get start time
    start_time = time.time()
    
    # Process request
    try:
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Add custom header
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-Request-ID"] = request_id
        
        # Log request
        logger.info(f"Request {request_id}: {request.method} {request.url.path} - {response.status_code} ({process_time:.4f}s)")
        
        # Track API usage if authenticated
        if request.url.path.startswith("/api/v1/") and request.method != "OPTIONS":
            try:
                # Get current user
                token = request.headers.get("Authorization", "").replace("Bearer ", "")
                if token:
                    from app.api.auth import get_user_from_token
                    user = await get_user_from_token(token)
                    if user:
                        # Log API call
                        await user_storage.add_user_activity({
                            "id": str(uuid.uuid4()),
                            "user_id": str(user.id),
                            "activity_type": "api_call",
                            "description": f"API call: {request.method} {request.url.path}",
                            "timestamp": time.time(),
                            "metadata": {
                                "method": request.method,
                                "endpoint": request.url.path,
                                "status_code": response.status_code,
                                "response_time": process_time,
                                "request_id": request_id
                            }
                        })
            except Exception as e:
                logger.error(f"Error tracking API usage: {str(e)}")
        
        return response
    except Exception as e:
        # Log error
        logger.error(f"Request {request_id} failed: {str(e)}")
        
        # Return error response
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error"}
        )

# Include routers
# Include user management routes
app.include_router(
    user.router,
    prefix="/api/v1",
    tags=["users"],
)

# Include enhanced user management routes with v2 prefix
app.include_router(
    users_router,
    prefix="/api/v2",
    tags=["users-v2"],
)

app.include_router(
    billing.router,
    prefix="/api/v1/billing",
    tags=["billing"],
)

app.include_router(
    training.router,
    prefix="/api/v1/training",
    tags=["training"],
)

app.include_router(
    security.router,
    prefix="/api/v1/security",
    tags=["security"],
)

app.include_router(
    integration.router,
    prefix="/api/v1/integration",
    tags=["integration"],
)

app.include_router(
    conversation.router,
    prefix="/api/v1",
    tags=["conversation"],
)

app.include_router(
    conversation.router,
    prefix="/api/v2",
    tags=["conversation-v2"],
)

# Include voice router
app.include_router(
    voice_router,
    prefix="/api/v1",
    tags=["voice"],
)

app.include_router(
    health.router,
    prefix="/api/v1/health",
    tags=["health"],
)

app.include_router(
    feature_flags.router,
    prefix="/api/v1/feature-flags",
    tags=["feature-flags"],
)

app.include_router(
    metrics.router,
    prefix="/api/v1/metrics",
    tags=["metrics"],
)

# Include the new API router with all cross-modal, multimodal, and other routes
app.include_router(
    api_router,
    prefix="/api/v1",
    tags=["api"],
)

# Include the model training v2 router
app.include_router(
    model_training_v2.router,
    prefix="/api/v1",
    tags=["training"],
)

# Include the enhanced chat router
app.include_router(
    enhanced_chat.router,
    prefix="/api/v1/enhanced-chat",
    tags=["enhanced-chat"],
)

# Root endpoint
@app.get("/")
async def root():
    return {
        "name": "SimLLM API",
        "version": "1.0.0",
        "status": "online",
        "docs_url": "/docs"
    }

# Health check endpoint
@app.get("/health")
async def health():
    # Get model health status
    from app.utils.model_health import model_health
    from app.utils.model_manager import model_manager
    import platform
    import psutil
    
    try:
        # Get traditional model health status
        all_health = model_health.get_all_model_health()
        
        # Count healthy models
        healthy_count = 0
        unhealthy_count = 0
        unknown_count = 0
        
        for model_name, health in all_health.items():
            if health.get("status") == "healthy":
                healthy_count += 1
            elif health.get("status") in ["unhealthy", "error"]:
                unhealthy_count += 1
            else:
                unknown_count += 1
                
        # Determine overall status
        if unhealthy_count > 0:
            status = "degraded"
        elif unknown_count > 0 and healthy_count == 0:
            status = "unknown"
        else:
            status = "healthy"
        
        # Get model manager stats
        model_stats = model_manager.get_loading_stats()
        
        # Print model status table to logs
        model_manager.print_model_status_table()
        
        # Get system info
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # Get uptime
        uptime = time.time() - psutil.boot_time()
        uptime_str = f"{int(uptime // 86400)}d {int((uptime % 86400) // 3600)}h {int((uptime % 3600) // 60)}m {int(uptime % 60)}s"
        
        # Calculate model loading progress
        if model_stats["total_models"] > 0:
            progress = (model_stats["loaded_models"] + model_stats["failed_models"]) / model_stats["total_models"] * 100
            progress_str = f"{progress:.1f}%"
        else:
            progress_str = "0%"
            
        return {
            "status": status,
            "timestamp": time.time(),
            "models": {
                "healthy": healthy_count,
                "unhealthy": unhealthy_count,
                "unknown": unknown_count,
                "total": healthy_count + unhealthy_count + unknown_count
            },
            "model_manager": {
                "total": model_stats["total_models"],
                "loaded": model_stats["loaded_models"],
                "loading": model_stats["loading_models"],
                "pending": model_stats["pending_models"],
                "failed": model_stats["failed_models"],
                "progress": progress_str
            },
            "system": {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "uptime": uptime_str,
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "free": disk.free,
                    "percent": disk.percent
                }
            }
        }
    except Exception as e:
        logger.error(f"Error in health check: {str(e)}")
        return {
            "status": "error",
            "error": str(e)
        }

# Model status endpoint
@app.get("/models/status", tags=["models"])
async def model_status():
    """
    Get model status information.
    
    Returns:
        dict: Model status information
    """
    from app.utils.model_manager import model_manager
    
    try:
        # Get model stats
        model_stats = model_manager.get_loading_stats()
        
        # Print model status table to logs
        model_manager.print_model_status_table()
        
        # Get all models
        models_info = []
        for model_key, model_info in model_stats["models"].items():
            # Split the model key
            parts = model_key.split(":", 1)
            if len(parts) == 2:
                task, model_id = parts
            else:
                task, model_id = "unknown", model_key
                
            # Create model info
            info = {
                "model_id": model_id,
                "task": task,
                "status": model_info.get("status", "unknown"),
                "priority": model_info.get("priority", 3),
                "loading_time": model_info.get("loading_time"),
                "error": model_info.get("error"),
                "fallback_used": model_info.get("fallback_used", False),
                "fallback_model_id": model_info.get("fallback_model_id"),
                "description": model_info.get("desc", "")
            }
            
            # Add to list
            models_info.append(info)
        
        # Calculate progress
        if model_stats["total_models"] > 0:
            progress = (model_stats["loaded_models"] + model_stats["failed_models"]) / model_stats["total_models"] * 100
        else:
            progress = 0
            
        # Return model status
        return {
            "status": "ok",
            "timestamp": time.time(),
            "summary": {
                "total": model_stats["total_models"],
                "loaded": model_stats["loaded_models"],
                "loading": model_stats["loading_models"],
                "pending": model_stats["pending_models"],
                "failed": model_stats["failed_models"],
                "progress": f"{progress:.1f}%"
            },
            "models": models_info
        }
    except Exception as e:
        logger.error(f"Error getting model status: {str(e)}")
        return {
            "status": "error",
            "timestamp": time.time(),
            "error": str(e)
        }

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    # Log error
    logger.error(f"Unhandled exception: {str(exc)}")
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "Internal server error"}
    )

# Run the application
if __name__ == "__main__":
    import uvicorn
    
    # Get port from environment or use default
    port = int(os.environ.get("PORT", 8000))
    
    # Run server
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=port,
        reload=True
    )

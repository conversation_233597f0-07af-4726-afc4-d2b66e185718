"""
Main FastAPI application entry point.
"""

async def preload_essential_models(model_manager):
    """
    Preload essential models during application startup using the async model loading system.

    Args:
        model_manager: The model manager instance
    """
    try:
        logger.info("Preloading essential models...")

        # Define essential models to preload
        essential_models = [
            {"model_id": "sentence-transformers/all-MiniLM-L6-v2", "task": "sentence-embedding", "priority": 10, "category": "simbaAI-embedding"},
            {"model_id": "TinyLlama/TinyLlama-1.1B-Chat-v1.0", "task": "text-generation", "priority": 9, "category": "simbaAI-text-generation"},
            {"model_id": "distil-whisper/distil-small.en", "task": "speech-to-text", "priority": 8, "category": "simbaAI-audio"},
            {"model_id": "google/efficientnet-b0", "task": "image-classification", "priority": 7, "category": "simbaAI-vision"},
            {"model_id": "hustvl/yolos-small", "task": "object-detection", "priority": 6, "category": "simbaAI-vision"}
        ]

        # Check if we should load additional models
        load_all = os.environ.get("SIM_LLM_PRELOAD_ALL", "").lower() in ("true", "1", "yes")

        if load_all:
            # Get all models from MongoDB
            logger.info("Fetching all model metadata from MongoDB...")

            # Get MongoDB database
            from app.db.mongodb import get_mongodb_database
            mongodb_db = await get_mongodb_database()
            if mongodb_db is None:
                logger.error("Failed to connect to MongoDB, cannot preload all models")
            else:
                # Get models collection
                models_collection = mongodb_db["models"]

                # Get all models
                models_cursor = models_collection.find({})
                models_list = await models_cursor.to_list(length=100)  # Limit to 100 models for safety

                logger.info(f"Found {len(models_list)} models in MongoDB")

                # Add models to essential_models list with lower priority
                for model_doc in models_list:
                    model_id = model_doc.get("model_id")
                    task = model_doc.get("task")
                    category = model_doc.get("category")

                    if not model_id or not task:
                        continue

                    # Skip models that are already in the essential_models list
                    if any(m["model_id"] == model_id for m in essential_models):
                        continue

                    # Skip certain tasks that don't need preloading
                    if task in ["image-generation", "video-generation"]:
                        logger.info(f"Skipping preload for {task} model: {model_id} (not needed)")
                        continue

                    # Add with lower priority
                    essential_models.append({
                        "model_id": model_id,
                        "task": task,
                        "category": category,
                        "priority": 3  # Lower priority than essential models
                    })

        # Sort models by priority (highest first)
        essential_models.sort(key=lambda x: x["priority"], reverse=True)

        # Trigger async loading for each model
        loading_tasks = []
        for model_info in essential_models:
            model_id = model_info["model_id"]
            priority = model_info["priority"]
            category = model_info.get("category")
            task = model_info.get("task")

            logger.info(f"Triggering preload for {model_id} with priority {priority}...")

            try:
                # Trigger async loading
                task_id = await model_manager.trigger_model_loading(
                    model_id=model_id,
                    priority=priority,
                    category=category,
                    # Use environment variables for optimization settings
                    quantize=os.environ.get("SIM_LLM_QUANTIZE", "").lower() in ("true", "1", "yes"),
                    compile=os.environ.get("SIM_LLM_COMPILE", "").lower() in ("true", "1", "yes"),
                    device=os.environ.get("SIM_LLM_DEVICE", "cpu")
                )
                loading_tasks.append(task_id)
                logger.info(f"Model loading triggered with task ID: {task_id}")
            except Exception as e:
                logger.error(f"Error triggering preload for {model_id}: {str(e)}")
                import traceback
                logger.debug(f"Traceback: {traceback.format_exc()}")
                # Continue with the next model

        logger.info(f"Preloading initiated for {len(loading_tasks)} models")
        return loading_tasks
    except Exception as e:
        logger.error(f"Error in preload_essential_models: {str(e)}")
        import traceback
        logger.debug(f"Traceback: {traceback.format_exc()}")
# Apply comprehensive fixes early to prevent errors and warnings
try:
    from app.utils.clean_warnings import apply_clean_warnings
    from app.utils.comprehensive_fixes import apply_all_fixes
    apply_clean_warnings()
    apply_all_fixes()
except ImportError:
    print("WARNING: Could not import comprehensive fixes modules")

# Apply version patch for Python 3.12 compatibility
import sys
import os
import warnings
import asyncio
from pathlib import Path

# Filter out PyTorch warnings
warnings.filterwarnings("ignore", message="copying from a non-meta parameter in the checkpoint to a meta parameter")
warnings.filterwarnings("ignore", message="Some weights of the model checkpoint")
warnings.filterwarnings("ignore", message=".*Using a slow image processor.*")
warnings.filterwarnings("ignore", message=".*The tokenizer class you load from this checkpoint is not the same.*")
warnings.filterwarnings("ignore", message=".*expected str, bytes or os.PathLike object, not NoneType.*")
warnings.filterwarnings("ignore", message=".*metaclass conflict.*")
warnings.filterwarnings("ignore", message="torch.utils._pytree._register_pytree_node is deprecated")
warnings.filterwarnings("ignore", message=".*path should be string, bytes, os.PathLike or integer, not type.*")
warnings.filterwarnings("ignore", message=".*model.safetensors or model.safetensors.index.json and thus cannot be loaded with.*")

# Ensure model loading fixes are applied early
try:
    from app.utils.ensure_model_fixes import ensure_model_fixes
    ensure_model_fixes()
except ImportError:
    print("WARNING: Could not import ensure_model_fixes module")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Initialize app module for flags
import app
if not hasattr(sys.modules["app"], "peft_disabled"):
    sys.modules["app"].peft_disabled = False

# Apply bitsandbytes patch to fix 'NoneType' object has no attribute 'cadam32bit_grad_fp32' error
from app.utils.bitsandbytes_patch import apply_bitsandbytes_patch
apply_bitsandbytes_patch()

# Import logger after initializing app module
from loguru import logger

# Apply PEFT patch to fix metaclass conflict with bitsandbytes
# This must be done before any imports that might trigger the conflict
from app.utils.peft_patch import apply_peft_patch
peft_patch_result = apply_peft_patch()
if not peft_patch_result:
    # If patch failed, disable training-related features
    sys.modules["app"].peft_disabled = True
    logger.warning("PEFT patch failed, training features will be disabled")
else:
    logger.info("PEFT patch applied successfully")

# Apply transformers patch to fix various issues
try:
    from app.utils.transformers_patch import apply_transformers_patch
    apply_transformers_patch()
except ImportError:
    warnings.warn("Could not import transformers_patch")

# Apply passlib bcrypt patch to fix warning
try:
    from app.utils.patches import apply_patches
    apply_patches()
    logger.info("Applied passlib bcrypt patch")
except ImportError:
    warnings.warn("Could not import patches module")

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from app.utils.version_patch import *
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, Response, RedirectResponse
from slowapi import Limiter
from slowapi.util import get_remote_address
from slowapi.middleware import SlowAPIMiddleware
from slowapi.errors import RateLimitExceeded
from starlette.middleware.sessions import SessionMiddleware
from starlette.requests import Request
from starlette.responses import JSONResponse
from loguru import logger

from app.api.routes import conversation, feedback, prompt, billing, image, deepfake, document, dataset as app_dataset
from app.api.routes import user_db as user  # Use database-backed user routes, document
from app.api.routes import multimodal, cross_modal, canvas, model_training, video, oauth, webhooks, usage, enhanced_chat
from app.api.routes.admin import router as admin_router
from app.api.routes import models  # Add the new models router
from app.api.routes import model_manager  # Add the new model manager router
from app.api.routes.web_search import router as web_search_router  # Add web search router
# Temporarily disable agents module due to syntax error
# from app.api.routes import agents
from app.api.auth import get_current_user
# Use the standard config that reads from environment variables
from app.utils.config import settings, reload_settings

# Force reload settings at startup to ensure we get the latest values from .env
settings = reload_settings()

# Configure logger using our custom configuration
from app.utils.logging_config import configure_logging
configure_logging()

# Initialize rate limiter
limiter = Limiter(key_func=get_remote_address)

# Initialize databases
from app.db.database import init_db, get_db_session
from sqlalchemy.orm import Session
# Import MongoDB modules
from app.db.mongodb import mongodb_initializer
from app.db.mongodb.client import get_mongodb_database

# Create FastAPI app
app = FastAPI(
    title="Generative & Agentic AI System",
    description="""
    A custom AI system with agentic and generative capabilities.
    """,
    version="0.1.0",
)

# Add health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """
    Health check endpoint to verify the API is running.
    """
    return {"status": "ok", "message": "API is healthy"}

# Initialize database and messaging on startup
@app.on_event("startup")
async def startup_event():
    """
    Initialize services on application startup.
    """
    logger.info("Starting up application...")

    # Run critical startup tasks first (synchronously)
    await critical_startup_tasks()

    # Create a task to run the non-critical async initialization in background
    asyncio.create_task(background_startup_tasks())

async def critical_startup_tasks():
    """Run critical startup tasks that must complete before the server can respond."""
    try:
        # Set up global model cache directory
        models_dir = Path("./models").resolve()
        models_dir.mkdir(exist_ok=True, parents=True)

        # Create subdirectories
        (models_dir / 'transformers').mkdir(exist_ok=True, parents=True)
        (models_dir / 'datasets').mkdir(exist_ok=True, parents=True)

        # Set environment variables globally for all endpoints
        os.environ['HF_HOME'] = str(models_dir)
        os.environ['TRANSFORMERS_CACHE'] = str(models_dir / 'transformers')
        os.environ['HF_DATASETS_CACHE'] = str(models_dir / 'datasets')

        # Log the environment variables
        logger.info(f"Global HF_HOME set to {os.environ['HF_HOME']}")
        logger.info(f"Global TRANSFORMERS_CACHE set to {os.environ['TRANSFORMERS_CACHE']}")
        logger.info(f"Global HF_DATASETS_CACHE set to {os.environ['HF_DATASETS_CACHE']}")

        # Initialize SQL database
        init_db()
        logger.info("SQL Database initialized")

        # Initialize default data (roles and admin user)
        from app.db.init_data import init_data
        init_data()
        logger.info("Default data initialized")

        # Apply consolidated model loading patches early
        try:
            from app.utils.consolidated_model_patch import apply_consolidated_patches
            patch_result = apply_consolidated_patches()
            if patch_result:
                logger.info("Applied consolidated model loading patches successfully")
            else:
                logger.warning("Failed to apply consolidated model loading patches, some models may not load correctly")
        except Exception as e:
            logger.error(f"Error applying consolidated model loading patches: {str(e)}")
            logger.warning("Continuing without model loading patches, some models may not load correctly")

        logger.info("Critical startup tasks completed - server is ready to respond")
    except Exception as e:
        logger.error(f"Error during critical startup: {str(e)}")
        # Don't raise the exception to prevent server startup failure

async def background_startup_tasks():
    """Run non-critical startup tasks in the background."""
    try:
        logger.info("Starting background startup tasks...")

        # Initialize MongoDB
        mongodb_success = await mongodb_initializer.initialize()
        if mongodb_success:
            logger.info("MongoDB initialized successfully")
        else:
            logger.warning("MongoDB initialization failed, some features may not work properly")

        # Create default admin user for the API if it doesn't exist
        try:
            from app.data.user import UserStorage
            from app.api.auth import get_password_hash

            user_storage = UserStorage()
            admin_user = await user_storage.get_user_by_username("admin")

            if not admin_user:
                logger.info("Creating default admin user")
                # Create admin user with default password
                hashed_password = get_password_hash("admin")
                await user_storage.create_user(
                    username="admin",
                    email="<EMAIL>",
                    hashed_password=hashed_password,
                    full_name="Admin User",
                    role="admin"
                )
                logger.info("Default admin user created with username 'admin' and password 'admin'")
            else:
                logger.info("Default admin user already exists")
        except Exception as e:
            logger.error(f"Error creating default admin user: {str(e)}")

        # Initialize models from MinIO
        try:
            from app.services.model_initializer import model_initializer
            # Start model initialization in background
            asyncio.create_task(model_initializer.initialize_models())
            logger.info("Model initialization started in background")
        except Exception as e:
            logger.warning(f"Error starting model initialization: {str(e)}")
            logger.info("Continuing startup without model initialization")

        # Initialize model manager
        from app.services.model_manager import model_manager
        logger.info("Model manager initialized for centralized model loading with Kafka, Redis, and Celery integration")

        # Preload models if enabled in settings (but don't block startup)
        from app.utils.config import settings
        if settings.SIM_LLM_PRELOAD_MODELS:
            try:
                logger.info("Starting model preloading in background...")
                asyncio.create_task(preload_models_background(model_manager))
            except Exception as model_error:
                logger.error(f"Error starting model preloading: {str(model_error)}")
        else:
            logger.info("Model preloading disabled by environment variable SIM_LLM_PRELOAD_MODELS")

        # Check if we should skip model sync (useful when using start_app.py)
        skip_sync = os.environ.get("SIM_LLM_SKIP_SYNC", "").lower() in ("true", "1", "yes")
        force_sync = os.environ.get("SIM_LLM_FORCE_SYNC", "").lower() in ("true", "1", "yes")

        if skip_sync:
            logger.info("Skipping model synchronization as requested by environment variable")
        else:
            # Run model sync in background to not block startup
            asyncio.create_task(sync_models_background(force_sync))

        # Initialize Kafka if enabled (in background)
        if hasattr(settings, 'KAFKA_ENABLED') and settings.KAFKA_ENABLED:
            asyncio.create_task(initialize_kafka_background())

        logger.info("Background startup tasks completed")
    except Exception as e:
        logger.error(f"Error during background startup: {str(e)}")

async def preload_models_background(model_manager):
    """Preload models in background without blocking startup."""
    try:
        logger.info("Preloading models for faster response times...")
        await preload_essential_models(model_manager)
        logger.info("Models preloaded successfully")
    except Exception as model_error:
        logger.error(f"Error preloading models: {str(model_error)}")
        logger.info("Will continue and load models on demand")

async def sync_models_background(force_sync=False):
    """Run model synchronization in background."""
    try:
        # Use the CLI's sync_models function for consistent behavior
        from app.services.cli import sync_models

        # Create a simple object with the required attributes
        class SyncArgs:
            def __init__(self, force=False):
                self.force = force
                self.model_id = None
                self.task = None

        # Call the sync_models function with the appropriate arguments
        logger.info("Starting model synchronization...")
        sync_args = SyncArgs(force=force_sync)

        # Run the async function
        sync_result = await sync_models(sync_args)

        if sync_result and sync_result.get("success", False):
            logger.info("Model synchronization completed successfully")
        else:
            logger.warning("Model synchronization encountered issues. Check the logs for details.")
    except Exception as e:
        logger.error(f"Error during model synchronization: {str(e)}")

async def initialize_kafka_background():
    """Initialize Kafka in background."""
    try:
        # Import from our special initialization module that ensures correct bootstrap servers
        from app.messaging.kafka_client_init import kafka_client
        from app.messaging.chat_queue import chat_queue
        from app.data_processing import dataset_processor
        from app.training.train_data_processor import train_data_processor

        # Import our model Kafka consumer
        from app.workers.kafka_consumer import model_kafka_consumer

        # Connect to Kafka
        await kafka_client.connect()
        logger.info(f"Connected to Kafka at {kafka_client.bootstrap_servers}")

        # Start chat queue
        await chat_queue.start()
        logger.info("Chat message queue started")

        # Start dataset processor
        await dataset_processor.start()
        logger.info("Dataset processor started")

        # Start model Kafka consumer
        await model_kafka_consumer.start()
        logger.info("Model Kafka consumer started")

        # Start training data processor
        await train_data_processor.start()
        logger.info("Training data processor started")
    except Exception as e:
        logger.error(f"Error initializing Kafka: {str(e)}")
# Add rate limiting middleware
app.state.limiter = limiter
app.add_middleware(SlowAPIMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add session middleware for OAuth
app.add_middleware(
    SessionMiddleware,
    secret_key=settings.SECRET_KEY,
    max_age=3600  # 1 hour
)

# Add usage tracking middleware
from app.api.middleware import add_usage_middleware
add_usage_middleware(app)

# Include routers
app.include_router(conversation.router, prefix="/api/v1", tags=["conversation"])
app.include_router(feedback.router, prefix="/api/v1", tags=["feedback"])
app.include_router(prompt.router, prefix="/api/v1", tags=["prompt"])
app.include_router(user.router, prefix="/api/v1", tags=["user"])
app.include_router(billing.router, prefix="/api/v1/billing", tags=["billing"])
app.include_router(image.router, prefix="/api/v1/image", tags=["image"])
app.include_router(deepfake.router, prefix="/api/v1/deepfake", tags=["deepfake"])
app.include_router(document.router, prefix="/api/v1/document", tags=["document"])
app.include_router(app_dataset.router, prefix="/api/v1/dataset", tags=["dataset"])
app.include_router(multimodal.router, prefix="/api/v1/multimodal", tags=["multimodal"])
app.include_router(cross_modal.router, prefix="/api/v1/cross-modal", tags=["cross-modal"])
app.include_router(models.router, prefix="/api/v1", tags=["models"])
app.include_router(model_manager.router, prefix="/api/v1", tags=["model-manager"])
# Temporarily disable agents router due to syntax error
# app.include_router(agents.router, prefix="/api/v1/agents", tags=["agents"])
app.include_router(canvas.router, prefix="/api/v1/canvas", tags=["canvas"])
app.include_router(model_training.router, tags=["models"])
app.include_router(video.router, prefix="/api/v1/video", tags=["video"])
app.include_router(oauth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(enhanced_chat.router, prefix="/api/v1/enhanced-chat", tags=["enhanced-chat"])
app.include_router(web_search_router, prefix="/api/v1/web", tags=["web-search"])

# Import and include code assistant router
from app.api.routes.code_assistant import router as code_assistant_router
app.include_router(code_assistant_router, prefix="/api/v1/code", tags=["code-assistant"])
app.include_router(webhooks.router, prefix="/api/v1", tags=["webhooks"])
app.include_router(usage.router, prefix="/api/v1", tags=["usage"])
app.include_router(admin_router, prefix="/api/v1", tags=["admin"])

# Import and include new routers
from app.dashboard.routes import router as dashboard_router
from app.voice.routes import router as voice_router
from app.api.routes.model_management import router as model_management_router

# Import language utilities
from app.utils.language_utils import language_utils

# Import training-related modules if PEFT patch was successful
if not getattr(sys.modules["app"], "peft_disabled", False):
    from app.api.routes.training import router as training_router
    from app.training.pipeline import TrainingPipeline

app.include_router(dashboard_router, prefix="/api/v1", tags=["dashboard"])
app.include_router(voice_router, prefix="/api/v1", tags=["voice"])
app.include_router(model_management_router, prefix="/api/v1", tags=["model-management"])

# Include training router if PEFT patch was successful
if not getattr(sys.modules["app"], "peft_disabled", False):
    app.include_router(training_router, prefix="/api/v1/training", tags=["training"])

    # Initialize training pipeline
    training_pipeline = TrainingPipeline()
    # Uncomment to start the training pipeline automatically
    # training_pipeline.start()

# Serve API documentation as the root
@app.get("/", include_in_schema=False)
async def root():
    """Redirect to API documentation."""
    return RedirectResponse(url="/docs")

# Add favicon route
@app.get("/favicon.ico", include_in_schema=False)
async def get_favicon():
    """Serve a default favicon or 204 No Content."""
    # Return a 204 No Content response
    return Response(status_code=204)

# Exception handlers
@app.exception_handler(RateLimitExceeded)
async def rate_limit_handler(request: Request, exc: RateLimitExceeded):
    """Handle rate limit exceeded exceptions."""
    return JSONResponse(
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        content={"detail": "Rate limit exceeded"},
    )

@app.get("/health", tags=["health"])
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "version": "0.1.0"}

@app.get("/api/v1/status", tags=["health"])
async def status(current_user = Depends(get_current_user)):
    """Status endpoint that requires authentication."""
    return {
        "status": "ok",
        "user": current_user.username,
        "models": {
            "simbaAI": model_manager.list_models()
        }
    }

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    try:
        # Shutdown MongoDB
        mongodb_shutdown = await mongodb_initializer.shutdown()
        if mongodb_shutdown:
            logger.info("MongoDB connections closed")
        else:
            logger.warning("Error closing MongoDB connections")

        # Shutdown Kafka if enabled
        # Check if KAFKA_ENABLED exists in settings
        if hasattr(settings, 'KAFKA_ENABLED') and settings.KAFKA_ENABLED:
            from app.messaging.kafka_client import kafka_client
            from app.messaging.chat_queue import chat_queue
            from app.data_processing import dataset_processor
            from app.training.train_data_processor import train_data_processor

            # Stop training data processor
            await train_data_processor.stop()
            logger.info("Training data processor stopped")

            # Stop dataset processor
            await dataset_processor.stop()
            logger.info("Dataset processor stopped")

            # Stop model Kafka consumer
            from app.workers.kafka_consumer import model_kafka_consumer
            await model_kafka_consumer.stop()
            logger.info("Model Kafka consumer stopped")

            # Stop chat queue
            await chat_queue.stop()
            logger.info("Chat message queue stopped")

            # Disconnect from Kafka
            await kafka_client.disconnect()
            logger.info("Disconnected from Kafka")
    except Exception as e:
        logger.error(f"Error during shutdown: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.api.main:app", host="0.0.0.0", port=8000, reload=True)
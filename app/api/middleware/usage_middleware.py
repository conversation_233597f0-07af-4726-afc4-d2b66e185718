"""
Middleware for tracking API usage.
"""
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import time
import asyncio
from loguru import logger

from app.db.database import get_db_session
from app.api.auth import get_current_user
from app.billing.usage_tracker import usage_tracker

class UsageMiddleware(BaseHTTPMiddleware):
    """
    Middleware for tracking API usage.
    """

    def __init__(
        self,
        app: ASGIApp,
        exclude_paths: Optional[List[str]] = None
    ):
        """
        Initialize the middleware.

        Args:
            app: ASGI app
            exclude_paths: List of paths to exclude from tracking
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/api/v1/health",
            "/api/v1/status",
            "/api/v1/token",
            "/api/v1/refresh",
            "/api/v1/auth",
            "/api/v1/webhooks",
            "/docs",
            "/redoc",
            "/openapi.json"
        ]

        # Feature mapping
        self.feature_mapping = {
            "/api/v1/conversation": "api_calls",
            "/api/v1/image": "image_generation",
            "/api/v1/video": "video_generation",
            "/api/v1/document": "file_upload",
            "/api/v1/multimodal": "api_calls",
            "/api/v1/cross-modal": "api_calls",
            "/api/v1/canvas": "api_calls"
        }

    async def dispatch(
        self,
        request: Request,
        call_next: Callable
    ) -> Response:
        """
        Process the request and track usage.

        Args:
            request: Request object
            call_next: Next middleware or route handler

        Returns:
            Response object
        """
        # Skip excluded paths
        try:
            # Get path as string, handling _CachedRequest objects safely
            try:
                # First try to get the path directly
                if hasattr(request, 'url') and hasattr(request.url, 'path'):
                    path = str(request.url.path)
                # If that fails, try to get it from the scope
                elif hasattr(request, 'scope') and 'path' in request.scope:
                    path = str(request.scope['path'])
                # If all else fails, use an empty string
                else:
                    path = ""
                    logger.warning("Could not determine request path, using empty string")
            except (AttributeError, TypeError) as e:
                # If we can't get the path, just use an empty string
                path = ""
                logger.warning(f"Error getting request path: {str(e)}")

            if any(path.startswith(exclude) for exclude in self.exclude_paths):
                return await call_next(request)
        except Exception as path_error:
            # Safe error logging to avoid coroutine/iteration issues
            try:
                error_msg = str(path_error)
            except:
                error_msg = "Unknown path error"
            logger.error(f"Error accessing request path: {error_msg}")
            return await call_next(request)

        # Start timer
        start_time = time.time()

        # Get feature from path
        try:
            feature = None
            # Ensure path is a string and handle _CachedRequest objects
            try:
                path_str = str(path) if path else ""
            except (AttributeError, TypeError):
                path_str = ""
                logger.warning("Failed to convert path to string, using empty string")

            # Safe handling of path string
            for prefix, feat in self.feature_mapping.items():
                try:
                    if path_str.startswith(prefix):
                        feature = feat
                        break
                except (AttributeError, TypeError) as e:
                    logger.warning(f"Error comparing path: {str(e)}")
                    continue

            # If no feature mapping found, just pass through
            if not feature:
                return await call_next(request)
        except Exception as feature_error:
            # Safe error logging
            try:
                error_msg = str(feature_error)
            except:
                error_msg = "Unknown feature error"
            logger.error(f"Error determining feature from path: {error_msg}")
            return await call_next(request)

        # Try to get user from token
        user = None
        try:
            # Get database session
            db = next(get_db_session())

            # Get user from token - safely handle the request object
            try:
                # First try to get the authorization header directly
                auth_header = None
                if hasattr(request, 'headers') and callable(getattr(request.headers, 'get', None)):
                    auth_header = request.headers.get("Authorization", "")
                # If that fails, try to get it from the scope
                elif hasattr(request, 'scope') and 'headers' in request.scope:
                    for header_name, header_value in request.scope['headers']:
                        if header_name.decode('utf-8').lower() == 'authorization':
                            auth_header = header_value.decode('utf-8')
                            break

                # If we have an auth header, extract the token
                if auth_header and auth_header.startswith("Bearer "):
                    token = auth_header.replace("Bearer ", "")
                    from app.api.auth import get_user_from_token
                    user = await get_user_from_token(token)
                else:
                    # Try the standard method as fallback
                    user = await get_current_user(request, db)
            except Exception as auth_error:
                logger.warning(f"Error getting user from token: {str(auth_error)}")
                # Try the standard method as fallback
                try:
                    user = await get_current_user(request, db)
                except:
                    # If we can't get the user, just continue without usage tracking
                    return await call_next(request)

            # If we couldn't get a user, just continue without usage tracking
            if not user:
                return await call_next(request)

            # Check if usage is allowed
            if not await usage_tracker.check_usage_allowed(user.id, feature, 1, db):
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "detail": f"Usage limit exceeded for {feature}. Please upgrade your subscription."
                    }
                )

            # Process request
            response = await call_next(request)

            # Calculate response time
            response_time = int((time.time() - start_time) * 1000)

            # Record usage if successful
            if response.status_code < 400:
                await usage_tracker.record_usage(
                    user_id=user.id,
                    feature=feature,
                    amount=1,
                    metadata={
                        "path": path,
                        "method": request.method,
                        "status_code": response.status_code,
                        "response_time_ms": response_time
                    },
                    db=db
                )

            return response
        except Exception as e:
            # If error occurred, just pass through
            # Safe error logging to avoid coroutine/iteration issues
            try:
                error_msg = str(e)
            except:
                error_msg = "Unknown middleware error"
            logger.error(f"Error in usage middleware: {error_msg}")

            # Try to get a more detailed error message safely
            try:
                import traceback
                detailed_error = traceback.format_exc()
                logger.debug(f"Detailed error: {detailed_error}")
            except:
                logger.debug("Could not get detailed error information")

            try:
                # Try to continue with the request
                return await call_next(request)
            except Exception as call_error:
                # If even that fails, return a generic error response
                try:
                    call_error_msg = str(call_error)
                except:
                    call_error_msg = "Unknown call error"
                logger.error(f"Failed to process request after middleware error: {call_error_msg}")
                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content={"detail": "Internal server error in request processing"}
                )

def add_usage_middleware(app):
    """
    Add usage middleware to the app.

    Args:
        app: FastAPI app
    """
    app.add_middleware(UsageMiddleware)

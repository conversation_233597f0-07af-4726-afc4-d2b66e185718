"""
Model Management API endpoints for sim_llm.
Provides comprehensive model management capabilities including:
- Listing models with their status
- Getting detailed status of specific models
- Adding new models from Hugging Face
- Deleting models
- Loading/unloading models on demand
- Checking storage status
- Synchronizing all models in the background
"""
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query, Path, Request
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from app.services.model_manager import model_manager
from app.api.auth import get_current_user
from app.db.models import User
from loguru import logger
import asyncio
import time
from datetime import datetime

router = APIRouter(prefix="/models", tags=["Model Management"])

# ----- Pydantic Models -----

class ModelInfo(BaseModel):
    model_id: str
    task: str
    status: str
    size_mb: Optional[int] = None
    loaded_at: Optional[str] = None
    device: Optional[str] = None
    is_quantized: Optional[bool] = None
    alias: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None

class ModelList(BaseModel):
    models: List[ModelInfo]
    total: int
    loaded: int
    loading: int
    failed: int

class ModelAddRequest(BaseModel):
    model_id: str
    task: str
    alias: Optional[str] = None
    category: Optional[str] = Field(default=None, description="Model category (e.g., simbaAI-text-generation)")
    description: Optional[str] = Field(default=None, description="Model description")
    quantize: Optional[bool] = Field(default=False, description="Apply quantization to reduce memory usage")
    device: Optional[str] = Field(default="cpu", description="Device to load model on (cpu, cuda:0, etc.)")
    priority: Optional[int] = Field(default=5, description="Loading priority (1-10, higher = more important)")

class ModelActionResponse(BaseModel):
    success: bool
    message: str
    task_id: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

class StorageStatus(BaseModel):
    minio: Dict[str, Any]
    mongodb: Dict[str, Any]
    local_cache: Dict[str, Any]
    memory_usage: Dict[str, Any]

# ----- Helper Functions -----

def get_model_size(model_id: str) -> Optional[int]:
    """Get model size from memory requirements if available."""
    from app.config.models import MODEL_MEMORY_REQUIREMENTS
    return MODEL_MEMORY_REQUIREMENTS.get(model_id, None)

# ----- API Endpoints -----

@router.get("/", response_model=ModelList)
async def list_models(
    task: Optional[str] = None,
    status: Optional[str] = None,
    category: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """
    List all models with their status.
    
    Filters:
    - task: Filter by task type (e.g., "text-generation", "chat", etc.)
    - status: Filter by status ("loaded", "loading", "failed", etc.)
    - category: Filter by category (e.g., "simbaAI-text-generation")
    """
    models_dict = model_manager.list_models(task=task)
    
    # Convert to list and add additional info
    models = []
    for model_id, info in models_dict.items():
        # Skip if status filter is provided and doesn't match
        if status and info.get("status", "") != status:
            continue
            
        # Skip if category filter is provided and doesn't match
        if category and info.get("category", "") != category:
            continue
            
        # Get model size from memory requirements if available
        size_mb = get_model_size(model_id)
        
        # Get metadata for additional info
        metadata = model_manager.get_model_metadata(model_id)
        
        models.append(ModelInfo(
            model_id=model_id,
            task=info.get("task", "unknown"),
            status=info.get("status", "unknown"),
            size_mb=size_mb,
            loaded_at=info.get("loaded_at_str", None),
            device=info.get("device", None),
            is_quantized=info.get("quantized", False),
            alias=info.get("alias", None),
            category=metadata.get("category", None) if metadata else None,
            description=metadata.get("description", None) if metadata else None
        ))
    
    # Count by status
    loaded_count = sum(1 for m in models if m.status == "loaded")
    loading_count = sum(1 for m in models if m.status == "loading")
    failed_count = sum(1 for m in models if m.status == "failed")
    
    return ModelList(
        models=models,
        total=len(models),
        loaded=loaded_count,
        loading=loading_count,
        failed=failed_count
    )

@router.get("/status/{model_id}", response_model=ModelInfo)
async def get_model_status(
    model_id: str = Path(..., description="Model ID to check"),
    current_user: User = Depends(get_current_user)
):
    """Get detailed status of a specific model"""
    status = model_manager.get_model_status(model_id)
    
    if not status:
        raise HTTPException(status_code=404, detail=f"Model {model_id} not found")
    
    # Get model size from memory requirements if available
    size_mb = get_model_size(model_id)
    
    # Get metadata for additional info
    metadata = model_manager.get_model_metadata(model_id)
    
    return ModelInfo(
        model_id=model_id,
        task=status.get("task", "unknown"),
        status=status.get("status", "unknown"),
        size_mb=size_mb,
        loaded_at=status.get("loaded_at_str", None),
        device=status.get("device", None),
        is_quantized=status.get("quantized", False),
        alias=status.get("alias", None),
        category=metadata.get("category", None) if metadata else None,
        description=metadata.get("description", None) if metadata else None
    )

@router.post("/add", response_model=ModelActionResponse)
async def add_model(
    model_request: ModelAddRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """
    Add a new model from Hugging Face.
    
    This will:
    1. Download the model from Hugging Face
    2. Store it in MinIO
    3. Save metadata to MongoDB
    4. Load the model if requested
    """
    try:
        # Check if model already exists
        status = model_manager.get_model_status(model_request.model_id)
        if status and status.get("status") == "loaded":
            return ModelActionResponse(
                success=True,
                message=f"Model {model_request.model_id} is already loaded",
                details=status
            )
        
        # Determine category if not provided
        category = model_request.category
        if not category:
            # Default category based on task
            if model_request.task == "text-generation" or model_request.task == "chat":
                category = "simbaAI-text-generation"
            elif model_request.task in ["multimodal", "image-to-text"]:
                category = "simbaAI-multimodal"
            elif model_request.task in ["speech-to-text", "text-to-speech"]:
                category = "simbaAI-audio"
            elif model_request.task in ["object-detection", "image-classification", "image-segmentation"]:
                category = "simbaAI-vision"
            elif model_request.task in ["image-generation", "text-to-image"]:
                category = "simbaAI-image-generation"
            elif model_request.task == "sentence-embedding":
                category = "simbaAI-embedding"
            elif model_request.task == "translation":
                category = "simbaAI-translation"
            elif model_request.task == "tabular":
                category = "simbaAI-tabular"
            else:
                category = "simbaAI-other"
        
        # Create metadata for MongoDB
        metadata = {
            "name": model_request.model_id,
            "category": category,
            "origin": f"huggingface/{model_request.model_id}",
            "description": model_request.description or f"{model_request.task.replace('-', ' ').title()} model",
            "size": "Unknown",  # Will be updated after upload
            "path_minio": f"models/{category}/{model_request.model_id}/",
            "status": "loading",
            "framework": "Transformers",
            "updated_at": datetime.utcnow().isoformat(),
            "task": model_request.task,
            "quantized": model_request.quantize,
            "device": model_request.device
        }
        
        # Save metadata to MongoDB
        model_manager.save_model_metadata(model_request.model_id, metadata)
        
        # Start the model loading process
        task_id = await model_manager.trigger_model_loading(
            model_id=model_request.model_id,
            task=model_request.task,
            priority=model_request.priority,
            device=model_request.device,
            quantize=model_request.quantize,
            skip_wait=True,  # Don't wait for loading to complete
            category=category
        )
        
        # Register alias if provided
        if model_request.alias:
            await model_manager.register_model_alias(
                model_request.model_id, 
                model_request.alias
            )
        
        return ModelActionResponse(
            success=True,
            message=f"Model {model_request.model_id} loading initiated",
            task_id=task_id
        )
    except Exception as e:
        logger.error(f"Error adding model {model_request.model_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{model_id}", response_model=ModelActionResponse)
async def delete_model(
    model_id: str = Path(..., description="Model ID to delete"),
    delete_files: bool = Query(False, description="Also delete model files from storage"),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a model.
    
    Options:
    - delete_files: If True, also delete model files from MinIO and local storage
    """
    try:
        # First unload the model if it's loaded
        if model_manager.is_model_loaded(model_id):
            model_manager.unload_model(model_id)
        
        # Delete model metadata from MongoDB
        success = model_manager.delete_model_metadata(model_id)
        
        # Delete model files if requested
        if delete_files:
            # Delete from MinIO
            minio_success = model_manager.delete_model_from_minio(model_id)
            
            # Delete from local storage
            local_success = model_manager.delete_model_files(model_id)
            
            return ModelActionResponse(
                success=success and minio_success and local_success,
                message=f"Model {model_id} deleted from memory, metadata, MinIO, and local storage",
                details={
                    "metadata_deleted": success,
                    "minio_deleted": minio_success,
                    "local_deleted": local_success
                }
            )
        
        return ModelActionResponse(
            success=success,
            message=f"Model {model_id} unloaded and metadata deleted"
        )
    except Exception as e:
        logger.error(f"Error deleting model {model_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/load/{model_id}", response_model=ModelActionResponse)
async def load_model(
    model_id: str = Path(..., description="Model ID to load"),
    task: Optional[str] = Query(None, description="Task for the model"),
    device: str = Query("cpu", description="Device to load model on"),
    quantize: bool = Query(False, description="Apply quantization"),
    current_user: User = Depends(get_current_user)
):
    """
    Load a model from storage into memory.
    
    If the model is already in local storage, it will be loaded from there.
    Otherwise, it will be downloaded from MinIO first.
    """
    try:
        # Check if model is already loaded
        if model_manager.is_model_loaded(model_id):
            return ModelActionResponse(
                success=True,
                message=f"Model {model_id} is already loaded"
            )
        
        # If task is not provided, try to determine it from metadata
        if not task:
            metadata = model_manager.get_model_metadata(model_id)
            if metadata:
                task = metadata.get("task")
            
            if not task:
                raise HTTPException(
                    status_code=400, 
                    detail="Task must be specified for models without metadata"
                )
        
        # Trigger model loading
        task_id = await model_manager.trigger_model_loading(
            model_id=model_id,
            task=task,
            device=device,
            quantize=quantize,
            skip_wait=True
        )
        
        return ModelActionResponse(
            success=True,
            message=f"Model {model_id} loading initiated",
            task_id=task_id
        )
    except Exception as e:
        logger.error(f"Error loading model {model_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/unload/{model_id}", response_model=ModelActionResponse)
async def unload_model(
    model_id: str = Path(..., description="Model ID to unload"),
    current_user: User = Depends(get_current_user)
):
    """
    Unload a model from memory.
    
    This will free up memory but keep the model files in storage.
    """
    try:
        # Check if model is loaded
        if not model_manager.is_model_loaded(model_id):
            return ModelActionResponse(
                success=False,
                message=f"Model {model_id} is not currently loaded"
            )
        
        # Unload the model
        success = model_manager.unload_model(model_id)
        
        return ModelActionResponse(
            success=success,
            message=f"Model {model_id} unloaded successfully" if success else f"Failed to unload model {model_id}"
        )
    except Exception as e:
        logger.error(f"Error unloading model {model_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/storage/status", response_model=StorageStatus)
async def get_storage_status(
    current_user: User = Depends(get_current_user)
):
    """
    Get status of model storage systems (MinIO, MongoDB, local cache).
    """
    try:
        # Check MinIO status
        minio_status = model_manager.check_minio_status()
        
        # Check MongoDB status
        mongodb_status = model_manager.check_mongodb_status()
        
        # Check local cache
        import os
        from app.utils.config import settings
        
        cache_dir = os.path.expanduser(settings.TRANSFORMERS_CACHE)
        cache_exists = os.path.exists(cache_dir)
        
        # Count models in cache
        cached_models = 0
        cache_size_mb = 0
        
        if cache_exists:
            # Count models in cache
            for root, dirs, files in os.walk(cache_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    cache_size_mb += os.path.getsize(file_path) / (1024 * 1024)
            
            # Count model directories
            if os.path.exists(os.path.join(cache_dir, "models")):
                cached_models = len(os.listdir(os.path.join(cache_dir, "models")))
        
        return StorageStatus(
            minio={
                "status": "connected" if minio_status["connected"] else "disconnected",
                "bucket_exists": minio_status.get("bucket_exists", False),
                "model_count": minio_status.get("model_count", 0),
                "total_size_mb": minio_status.get("total_size_mb", 0)
            },
            mongodb={
                "status": "connected" if mongodb_status["connected"] else "disconnected",
                "model_count": mongodb_status.get("model_count", 0)
            },
            local_cache={
                "exists": cache_exists,
                "path": cache_dir,
                "size_mb": round(cache_size_mb, 2),
                "model_count": cached_models
            },
            memory_usage={
                "total_mb": round(model_manager.get_total_memory_usage() / (1024 * 1024), 2),
                "available_mb": round(model_manager.get_available_memory() / (1024 * 1024), 2),
                "used_percent": model_manager.get_memory_usage_percent()
            }
        )
    except Exception as e:
        logger.error(f"Error getting storage status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sync/all", response_model=ModelActionResponse)
async def sync_all_models(
    background_tasks: BackgroundTasks,
    force: bool = Query(False, description="Force redownload of models"),
    current_user: User = Depends(get_current_user)
):
    """
    Synchronize all models from the configuration.
    
    This is a long-running operation that will be executed in the background.
    """
    async def _sync_all_models():
        try:
            await model_manager.sync_all_models(force_reload=force, skip_wait=True)
            logger.info("All models synchronized successfully")
        except Exception as e:
            logger.error(f"Error synchronizing models: {str(e)}")
    
    # Start the sync process in the background
    background_tasks.add_task(_sync_all_models)
    
    return ModelActionResponse(
        success=True,
        message="Model synchronization started in the background"
    )
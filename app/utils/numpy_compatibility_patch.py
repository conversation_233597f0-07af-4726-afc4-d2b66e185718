"""
NumPy 2.x Compatibility Patch

This module provides patches and workarounds for NumPy 2.x compatibility issues
with libraries that were compiled against NumPy 1.x.

Usage:
    Import this module before importing any potentially incompatible libraries:
    
    ```python
    from app.utils.numpy_compatibility_patch import apply_numpy_patches
    apply_numpy_patches()
    
    # Now import potentially problematic modules
    import torch
    ```
"""
import os
import sys
import warnings
from typing import Optional
import logging

logger = logging.getLogger(__name__)

def apply_numpy_patches(suppress_warnings: bool = True) -> None:
    """
    Apply patches to improve compatibility with NumPy 2.x
    
    Args:
        suppress_warnings: If True, suppress NumPy compatibility warnings
    """
    if suppress_warnings:
        # Suppress the NumPy 1.x vs 2.x compatibility warnings
        import warnings
        warnings.filterwarnings("ignore", 
                               message="A module that was compiled using NumPy 1.x cannot be run in NumPy 2")
        
        # Also set environment variable to suppress these warnings
        os.environ["NUMPY_EXPERIMENTAL_ARRAY_FUNCTION_ENABLED"] = "0"
    
    # Set environment variables that might help with compatibility
    os.environ["NPY_RELAXED_STRIDES_CHECKING"] = "1"
    
    # Log that patches have been applied
    logger.info("NumPy 2.x compatibility patches applied")

def downgrade_numpy_if_needed() -> bool:
    """
    Check if NumPy 2.x is installed and downgrade to 1.26.0 if needed
    
    Returns:
        bool: True if NumPy was downgraded, False otherwise
    """
    try:
        import numpy as np
        numpy_version = np.__version__
        
        # If NumPy 2.x is installed, downgrade to 1.26.0
        if numpy_version.startswith("2."):
            logger.warning(f"NumPy {numpy_version} detected. Downgrading to 1.26.0 for compatibility...")
            
            # Use pip to downgrade
            import subprocess
            result = subprocess.run([sys.executable, "-m", "pip", "install", "numpy==1.26.0", "--force-reinstall"],
                                   capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("Successfully downgraded NumPy to 1.26.0")
                
                # Reload numpy
                import importlib
                importlib.reload(np)
                logger.info(f"Reloaded NumPy, version now: {np.__version__}")
                return True
            else:
                logger.error(f"Failed to downgrade NumPy: {result.stderr}")
                return False
        
        return False
    except Exception as e:
        logger.error(f"Error in downgrade_numpy_if_needed: {str(e)}")
        return False
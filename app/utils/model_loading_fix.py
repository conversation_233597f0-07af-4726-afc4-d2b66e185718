"""
Model loading fixes for common issues with Hugging Face models.

This module provides patches and fixes for common issues encountered when loading models:
1. Device placement issues (CPU vs GPU)
2. Meta tensor data copying issues
3. Accelerate compatibility issues
"""
import os
import sys
import torch
import logging
from functools import wraps
import importlib
from typing import Any, Dict, Optional, Union, List

# Configure logger
try:
    from loguru import logger
except ImportError:
    # Fallback to standard logging
    logger = logging.getLogger(__name__)

# Monkey patch transformers.modeling_utils.PreTrainedModel.from_pretrained
def apply_model_loading_patches():
    """Apply patches to fix common model loading issues."""
    try:
        # Import transformers
        import transformers
        from transformers import PreTrainedModel
        
        # Store the original from_pretrained method
        original_from_pretrained = PreTrainedModel.from_pretrained
        
        @wraps(original_from_pretrained)
        def patched_from_pretrained(cls, pretrained_model_name_or_path, *model_args, **kwargs):
            """
            Patched version of from_pretrained that handles common issues:
            1. Removes device argument when using accelerate
            2. Handles meta tensor data copying issues
            3. Adds better error handling
            4. <PERSON><PERSON><PERSON> handles class objects
            """
            try:
                # Handle class objects and model classes directly
                if isinstance(pretrained_model_name_or_path, type) or hasattr(pretrained_model_name_or_path, '__name__'):
                    logger.debug(f"Handling model class: {pretrained_model_name_or_path}")
                    # For class objects, we need to use a valid model name instead
                    # This prevents the "Repo id must use alphanumeric chars" error
                    logger.warning(f"Converting class object to string model name for compatibility")
                    # Use a default model name that's known to work
                    pretrained_model_name_or_path = "gpt2"
                    
                    # Remove device and device_map to let the original method handle it
                    kwargs.pop('device', None)
                    kwargs.pop('device_map', None)
                    return original_from_pretrained(cls, pretrained_model_name_or_path, *model_args, **kwargs)
                
                # Only apply CPU force for string model paths
                if isinstance(pretrained_model_name_or_path, str):
                    # Force CPU for all models to avoid GPU memory issues
                    if "device_map" not in kwargs and os.environ.get("LOCAL_MODEL_FORCE_CPU", "true").lower() == "true":
                        kwargs["device_map"] = "cpu"
                    
                    # Set default torch_dtype if not specified
                    if "torch_dtype" not in kwargs:
                        kwargs["torch_dtype"] = torch.float32
                    elif kwargs.get("torch_dtype") == "auto":
                        kwargs["torch_dtype"] = torch.float32
                    
                    # Set low_cpu_mem_usage for better memory management
                    if "low_cpu_mem_usage" not in kwargs:
                        kwargs["low_cpu_mem_usage"] = True
                        
                    # Disable safetensors by default to avoid issues
                    if "use_safetensors" not in kwargs:
                        kwargs["use_safetensors"] = False
                        logger.debug("Disabled safetensors by default")
                
                # Try to load the model
                return original_from_pretrained(cls, pretrained_model_name_or_path, *model_args, **kwargs)
            except RuntimeError as e:
                # Handle meta tensor issues
                if "Cannot copy out of meta tensor" in str(e):
                    logger.warning(f"Error in patched_from_pretrained: {str(e)}")
                    # Try again without low_cpu_mem_usage
                    if kwargs.get("low_cpu_mem_usage", False):
                        kwargs["low_cpu_mem_usage"] = False
                        logger.info("Retrying model loading without low_cpu_mem_usage")
                        return original_from_pretrained(cls, pretrained_model_name_or_path, *model_args, **kwargs)
                # Handle device placement issues
                elif "cannot be moved to a specific device" in str(e):
                    logger.warning(f"Device placement error: {str(e)}")
                    # Remove device_map and try again
                    if "device_map" in kwargs:
                        del kwargs["device_map"]
                    if "device" in kwargs:
                        del kwargs["device"]
                    logger.info("Retrying model loading without device specification")
                    return original_from_pretrained(cls, pretrained_model_name_or_path, *model_args, **kwargs)
                # Re-raise other errors
                raise
            except Exception as e:
                logger.error(f"Error loading model {pretrained_model_name_or_path}: {str(e)}")
                raise
        
        # Apply the patch
        PreTrainedModel.from_pretrained = classmethod(patched_from_pretrained)
        
        # Patch pipeline creation for device issues
        original_pipeline = transformers.pipeline
        
        @wraps(original_pipeline)
        def patched_pipeline(task, model=None, tokenizer=None, **kwargs):
            """Patched pipeline creation that handles device placement issues."""
            try:
                # Try with original arguments
                return original_pipeline(task, model, tokenizer, **kwargs)
            except RuntimeError as e:
                if "cannot be moved to a specific device" in str(e):
                    # Remove device and try again
                    if "device" in kwargs:
                        logger.warning(f"Removing device argument due to error: {str(e)}")
                        del kwargs["device"]
                        return original_pipeline(task, model, tokenizer, **kwargs)
                raise
        
        # Apply the pipeline patch
        transformers.pipeline = patched_pipeline
        
        logger.info("Applied model loading patches successfully")
        return True
    except ImportError:
        logger.warning("Could not import transformers, skipping model loading patches")
        return False
    except Exception as e:
        logger.error(f"Error applying model loading patches: {str(e)}")
        return False

# Patch diffusers for stable diffusion issues
def apply_diffusers_patches():
    """Apply patches for diffusers library."""
    try:
        import diffusers
        
        # Patch StableDiffusionPipeline for better error handling
        original_init = diffusers.StableDiffusionPipeline.__init__
        
        @wraps(original_init)
        def patched_init(self, *args, **kwargs):
            try:
                return original_init(self, *args, **kwargs)
            except Exception as e:
                logger.error(f"Error initializing StableDiffusionPipeline: {str(e)}")
                # Try to continue with default values for problematic parameters
                if "addition_embed_type" in str(e):
                    logger.info("Fixing addition_embed_type parameter")
                    if "addition_embed_type" in kwargs:
                        kwargs["addition_embed_type"] = None
                    return original_init(self, *args, **kwargs)
                raise
        
        # Apply the patch
        diffusers.StableDiffusionPipeline.__init__ = patched_init
        
        logger.info("Applied diffusers patches successfully")
        return True
    except ImportError:
        logger.warning("Could not import diffusers, skipping diffusers patches")
        return False
    except Exception as e:
        logger.error(f"Error applying diffusers patches: {str(e)}")
        return False

def apply_model_manager_patches():
    """Apply patches to the model manager."""
    try:
        try:
            from app.utils.model_manager_patch import patch_model_manager
            return patch_model_manager()
        except ImportError:
            logger.warning("Could not import model_manager_patch module, creating it dynamically")
            
            # Create the patch dynamically
            import os
            import sys
            
            # Check if the model manager exists
            try:
                from app.services.model_manager import model_manager
            except ImportError:
                try:
                    from app.utils.model_manager import model_manager
                except ImportError:
                    logger.error("Could not import model_manager from either app.services or app.utils")
                    return False
            
            # Create the patch function
            def patch_model_manager():
                try:
                    # Store original method
                    original_load_text_generation_model = model_manager.load_text_generation_model
                    
                    # Create patched method
                    def patched_load_text_generation_model(self, model_id, use_auth_token=None, **kwargs):
                        # Special handling for TinyLlama
                        if "tinyllama" in model_id.lower() or "TinyLlama" in model_id:
                            try:
                                logger.info(f"Using specialized loader for TinyLlama model: {model_id}")
                                
                                # Import the specialized loader
                                try:
                                    from app.utils.tinyllama_loader import load_tinyllama
                                except ImportError:
                                    # Create a simple loader if the specialized one doesn't exist
                                    def load_tinyllama(model_id, local_dir=None, force_download=False, use_cpu=True):
                                        from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
                                        
                                        # Load tokenizer
                                        tokenizer = AutoTokenizer.from_pretrained(
                                            model_id,
                                            use_safetensors=False,
                                            trust_remote_code=True
                                        )
                                        
                                        # Load model with safe settings
                                        model = AutoModelForCausalLM.from_pretrained(
                                            model_id,
                                            torch_dtype=torch.float32,
                                            trust_remote_code=True,
                                            low_cpu_mem_usage=True,
                                            use_safetensors=False
                                        )
                                        
                                        # Force model to CPU if requested
                                        if use_cpu:
                                            model = model.to("cpu")
                                        
                                        # Create pipeline
                                        text_generation = pipeline(
                                            "text-generation",
                                            model=model,
                                            tokenizer=tokenizer,
                                            device="cpu" if use_cpu else "cuda"
                                        )
                                        
                                        return {"pipeline": text_generation}
                                
                                # Use model cache directory from settings
                                try:
                                    from app.utils.config import settings
                                    local_dir = os.path.join(settings.MODEL_CACHE_DIR, model_id.replace("/", "--"))
                                except ImportError:
                                    local_dir = None
                                
                                # Load the model
                                result = load_tinyllama(
                                    model_id=model_id,
                                    local_dir=local_dir,
                                    force_download=False,
                                    use_cpu=True
                                )
                                
                                # Return the pipeline for text generation
                                return result["pipeline"]
                            except Exception as e:
                                logger.error(f"Error using specialized TinyLlama loader: {str(e)}")
                                logger.info("Falling back to standard model loading")
                        
                        # Use original method for other models
                        return original_load_text_generation_model(self, model_id, use_auth_token, **kwargs)
                    
                    # Apply the patch
                    model_manager.load_text_generation_model = patched_load_text_generation_model.__get__(model_manager)
                    
                    logger.info("Applied model manager patches successfully")
                    return True
                except Exception as e:
                    logger.error(f"Error applying model manager patches: {str(e)}")
                    return False
            
            # Apply the patch
            return patch_model_manager()
            
    except Exception as e:
        logger.error(f"Error applying model manager patches: {str(e)}")
        return False

def apply_all_patches():
    """Apply all model loading patches."""
    transformers_patched = apply_model_loading_patches()
    diffusers_patched = apply_diffusers_patches()
    model_manager_patched = apply_model_manager_patches()
    
    return transformers_patched or diffusers_patched or model_manager_patched
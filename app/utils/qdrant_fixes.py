"""
Comprehensive fixes for Qdrant client issues and warnings.
"""
import warnings
import logging
from loguru import logger


def suppress_qdrant_warnings():
    """Suppress all Qdrant-related warnings."""
    try:
        # Suppress Qdrant connection warnings
        warnings.filterwarnings("ignore", message=".*Api key is used with unsecure connection.*")
        warnings.filterwarnings("ignore", module="qdrant_client.*")
        
        # Suppress Pydantic validation warnings from Qdrant responses
        warnings.filterwarnings("ignore", message=".*Field required.*")
        warnings.filterwarnings("ignore", message=".*Extra inputs are not permitted.*")
        warnings.filterwarnings("ignore", message=".*validation errors for ParsingModel.*")
        warnings.filterwarnings("ignore", message=".*vectors_count.*")
        warnings.filterwarnings("ignore", message=".*strict_mode_config.*")
        
        # Set Qdrant client logging to ERROR level
        logging.getLogger("qdrant_client").setLevel(logging.ERROR)
        logging.getLogger("qdrant_client.qdrant_remote").setLevel(logging.ERROR)
        logging.getLogger("qdrant_client.http").setLevel(logging.ERROR)
        
        logger.debug("Qdrant warnings suppressed")
        
    except Exception as e:
        logger.warning(f"Failed to suppress Qdrant warnings: {str(e)}")


def patch_qdrant_client():
    """Patch Qdrant client to handle validation errors gracefully."""
    try:
        import qdrant_client
        from qdrant_client import QdrantClient
        
        # Store original methods
        original_get_collection = QdrantClient.get_collection
        original_get_collections = QdrantClient.get_collections
        
        def patched_get_collection(self, collection_name: str):
            """Patched get_collection that handles validation errors."""
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    return original_get_collection(self, collection_name)
            except Exception as e:
                logger.debug(f"Error getting collection {collection_name}: {str(e)}")
                # Return a minimal mock object to prevent crashes
                class MockCollection:
                    def __init__(self):
                        self.result = None
                        self.status = "error"
                return MockCollection()
        
        def patched_get_collections(self):
            """Patched get_collections that handles validation errors."""
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    return original_get_collections(self)
            except Exception as e:
                logger.debug(f"Error getting collections: {str(e)}")
                # Return a minimal mock object to prevent crashes
                class MockCollections:
                    def __init__(self):
                        self.collections = []
                return MockCollections()
        
        # Apply patches
        QdrantClient.get_collection = patched_get_collection
        QdrantClient.get_collections = patched_get_collections
        
        logger.debug("Qdrant client patched successfully")
        
    except ImportError:
        logger.debug("Qdrant client not available, skipping patches")
    except Exception as e:
        logger.warning(f"Failed to patch Qdrant client: {str(e)}")


def fix_vector_dimension_errors():
    """Fix vector dimension mismatches in Qdrant operations."""
    try:
        import qdrant_client
        from qdrant_client import QdrantClient
        from qdrant_client.http import models
        
        # Store original upsert method
        original_upsert = QdrantClient.upsert
        
        def patched_upsert(self, collection_name: str, points, **kwargs):
            """Patched upsert that handles dimension mismatches."""
            try:
                return original_upsert(self, collection_name, points, **kwargs)
            except Exception as e:
                error_msg = str(e).lower()
                if "vector dimension error" in error_msg or "expected dim" in error_msg:
                    logger.warning(f"Vector dimension error for collection {collection_name}: {str(e)}")
                    # Try to extract expected dimension from error message
                    try:
                        import re
                        match = re.search(r'expected dim: (\d+)', error_msg)
                        if match:
                            expected_dim = int(match.group(1))
                            logger.info(f"Attempting to fix vector dimensions to {expected_dim}")
                            
                            # Fix vector dimensions in points
                            fixed_points = []
                            for point in points:
                                if hasattr(point, 'vector') and isinstance(point.vector, list):
                                    # Resize vector to expected dimension
                                    current_dim = len(point.vector)
                                    if current_dim != expected_dim:
                                        if current_dim > expected_dim:
                                            # Truncate
                                            point.vector = point.vector[:expected_dim]
                                        else:
                                            # Pad with zeros
                                            point.vector.extend([0.0] * (expected_dim - current_dim))
                                fixed_points.append(point)
                            
                            # Retry with fixed dimensions
                            return original_upsert(self, collection_name, fixed_points, **kwargs)
                    except Exception as fix_error:
                        logger.warning(f"Failed to fix vector dimensions: {str(fix_error)}")
                
                # Re-raise original error if we can't fix it
                raise
        
        # Apply patch
        QdrantClient.upsert = patched_upsert
        
        logger.debug("Vector dimension error fixes applied")
        
    except ImportError:
        logger.debug("Qdrant client not available, skipping dimension fixes")
    except Exception as e:
        logger.warning(f"Failed to apply vector dimension fixes: {str(e)}")


def create_qdrant_safe_client(url: str, api_key: str = None, **kwargs):
    """Create a Qdrant client with all safety patches applied."""
    try:
        from qdrant_client import QdrantClient
        
        # Suppress warnings during client creation
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            
            client = QdrantClient(
                url=url,
                api_key=api_key,
                **kwargs
            )
        
        logger.debug(f"Created safe Qdrant client for {url}")
        return client
        
    except Exception as e:
        logger.error(f"Failed to create Qdrant client: {str(e)}")
        return None


def apply_all_qdrant_fixes():
    """Apply all Qdrant fixes and patches."""
    try:
        suppress_qdrant_warnings()
        patch_qdrant_client()
        fix_vector_dimension_errors()
        
        logger.info("Applied all Qdrant fixes successfully")
        
    except Exception as e:
        logger.error(f"Failed to apply some Qdrant fixes: {str(e)}")


# Auto-apply when module is imported
apply_all_qdrant_fixes()

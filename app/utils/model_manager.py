"""
Model manager for loading and caching models.

This module provides a centralized manager for loading, caching, and tracking AI models.
It supports asynchronous loading, fallback mechanisms, and detailed statistics.
"""
import os
import torch
import asyncio
import threading
import sys
from typing import Dict, Any, Optional, Callable, Union, List, Tuple
from loguru import logger
import tempfile
import shutil
import hashlib
import json
import time
import uuid
from datetime import datetime
import concurrent.futures
from functools import partial
import traceback

# Use the centralized logging configuration

from app.utils.config import settings
from app.utils.progress_bar import ProgressBar
from app.utils.model_optimizer import model_optimizer

class ModelManager:
    """
    Centralized model manager for loading and caching models.

    This class handles:
    - Loading models from Hugging Face
    - Caching models to disk
    - Providing fallback mechanisms
    - Tracking model loading progress
    - Asynchronous background loading
    - Detailed statistics and monitoring
    """

    def download_all_models(self, force_reload: bool = False, lazy_load: bool = True):
        """
        Download all models in the models_to_download list.

        Args:
            force_reload: If True, force re-download even if model exists
            lazy_load: If True, only download models when they're first used
        """
        logger.info(f"Starting model downloads (lazy_load={lazy_load}, force_reload={force_reload})")

        if lazy_load:
            logger.info("Lazy loading enabled - models will be downloaded on first use")
            return

        # Sort models by priority (lower number = higher priority)
        sorted_models = sorted(self.models_to_download, key=lambda x: x.get('priority', 10))

        # Track success/failure counts
        success_count = 0
        failure_count = 0

        # Download models in sequence to avoid overwhelming the system
        for model_info in sorted_models:
            model_id = model_info['model_id']
            task = model_info['task']
            desc = model_info.get('desc', model_id)

            try:
                logger.info(f"Downloading model: {desc} ({model_id})...")

                # Use the appropriate load function for this model
                load_func = getattr(self, model_info['load_function'])
                model = load_func(model_id, force_download=force_reload)

                if model is not None:
                    success_count += 1
                    logger.info(f"Successfully downloaded model: {desc}")
                else:
                    failure_count += 1
                    logger.error(f"Failed to download model: {desc}")

            except Exception as e:
                failure_count += 1
                logger.error(f"Error downloading model {model_id}: {str(e)}")
                logger.debug(traceback.format_exc())

        logger.info(f"Model download completed. Success: {success_count}, Failed: {failure_count}")
        return success_count, failure_count

    # Define the list of models to download as a class attribute
    models_to_download = [
        # 1. Text models (smaller, download first)
        {
            "model_id": "distilbert-base-uncased",  # Use the fallback directly since bert-base-uncased fails
            "task": "sentence-embedding",
            "load_function": "_load_sentence_transformer",
            "fallback_model_id": "distilbert-base-uncased",  # Same as primary for reliability
            "desc": "Sentence Embedding Model",
            "priority": 2  # Medium priority
        },
        {
            "model_id": "TinyLlama/TinyLlama-1.1B-Chat-v1.0",  # Add TinyLlama for text generation
            "task": "text-generation",  # Use text-generation as the primary task
            "load_function": "_load_text_generation_model",
            "fallback_model_id": "distilgpt2",  # Fallback to distilgpt2 if TinyLlama fails
            "desc": "Text Generation Model (TinyLlama)",
            "priority": 1  # Highest priority
        },
        {
            "model_id": "distilgpt2",  # Non-gated model, smaller and faster
            "task": "text-generation",
            "load_function": "_load_text_generation_model",
            "fallback_model_id": "gpt2",  # Fallback to gpt2 if distilgpt2 fails
            "desc": "Text Generation Model",
            "priority": 1  # Highest priority
        },
        {
            "model_id": "gpt2",  # Fallback model
            "task": "text-generation",
            "load_function": "_load_text_generation_model",
            "fallback_model_id": None,  # No fallback for gpt2
            "desc": "Text Generation Model (Fallback)",
            "priority": 1  # Highest priority
        },
        {
            "model_id": "facebook/bart-large-cnn",
            "task": "summarization",
            "load_function": "_load_summarization_model",
            "fallback_model_id": "facebook/bart-large-xsum",  # Fallback to xsum if cnn fails
            "desc": "Summarization Model",
            "priority": 3  # Lower priority
        },
        {
            "model_id": "facebook/bart-large-xsum",  # Fallback model
            "task": "summarization",
            "load_function": "_load_summarization_model",
            "fallback_model_id": None,  # No fallback for xsum
            "desc": "Summarization Model (Fallback)",
            "priority": 3  # Lower priority
        },
        {
            "model_id": "openai/whisper-small",
            "task": "speech-to-text",
            "load_function": "_load_speech_to_text_model",
            "fallback_model_id": "distil-whisper/distil-small.en",  # Fallback to distil-whisper if whisper fails
            "desc": "Speech-to-Text Model",
            "priority": 3  # Lower priority
        },
        {
            "model_id": "distil-whisper/distil-small.en",  # Fallback model
            "task": "speech-to-text",
            "load_function": "_load_speech_to_text_model",
            "fallback_model_id": None,  # No fallback for distil-whisper
            "desc": "Speech-to-Text Model (Fallback)",
            "priority": 3  # Lower priority
        },
        {
            "model_id": "microsoft/speecht5_tts",
            "task": "text-to-speech",
            "load_function": "_load_text_to_speech_model",
            "fallback_model_id": None,  # No fallback for speecht5_tts
            "desc": "Text-to-Speech Model",
            "priority": 3  # Lower priority
        },
        {
            "model_id": "google/vit-base-patch16-224",
            "task": "image-classification",
            "load_function": "_load_image_classification_model",
            "fallback_model_id": None,  # No fallback for vit
            "desc": "Image Classification Model",
            "priority": 2  # Medium priority
        },
        {
            "model_id": "stabilityai/stable-diffusion-2-1",
            "task": "text-to-image",
            "load_function": "_load_text_to_image_model",
            "fallback_model_id": None,  # No fallback for stable-diffusion
            "desc": "Text-to-Image Model",
            "priority": 3  # Lower priority
        },
        {
            "model_id": "stabilityai/stable-diffusion-xl-refiner-1.0",
            "task": "image-to-image",
            "load_function": "_load_image_to_image_model",
            "fallback_model_id": None,  # No fallback for stable-diffusion
            "desc": "Image-to-Image Model",
            "priority": 3  # Lower priority
        },
        {
            "model_id": "Salesforce/blip-image-captioning-base",
            "task": "multimodal",
            "load_function": "_load_multimodal_model",
            "fallback_model_id": None,  # No fallback for BLIP
            "desc": "Multimodal Model (BLIP)",
            "priority": 2  # Medium priority
        }
    ]

    def __init__(self, model=None, processor=None, vocoder=None):
        """Initialize the model manager with necessary imports and setup."""
        # Initialize basic attributes
        self.models = {}
        self.model_info = {}
        self.loading_stats = {
            "total_models": len(self.models_to_download),
            "loaded_models": 0,
            "failed_models": 0,
            "loading_models": 0,
            "pending_models": len(self.models_to_download),
            "start_time": time.time(),
            "end_time": None,
            "total_time": None,
            "models": {}
        }

        # Initialize threading and async support
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=3)
        self._loading_lock = threading.RLock()
        self._background_tasks = []

        # Import required modules
        import torch
        from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline, BitsAndBytesConfig

        # Store imports as instance variables
        self.torch = torch
        self.AutoModelForCausalLM = AutoModelForCausalLM
        self.AutoTokenizer = AutoTokenizer
        self.pipeline = pipeline
        self.BitsAndBytesConfig = BitsAndBytesConfig

        # Store model components if provided (for text-to-speech)
        self.model = model
        self.processor = processor
        self.vocoder = vocoder

        # Get model directory from settings
        self.models_dir = settings.MODELS_DIR

        # Cache directory for model metadata
        self.cache_dir = os.path.join(os.path.expanduser("~"), ".cache", "sim_llm", "models")

        # Create directories if they don't exist
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs(self.models_dir, exist_ok=True)
        os.makedirs(settings.MODEL_CACHE_DIR, exist_ok=True)

        # Set up cache manifest file
        self.cache_manifest_path = os.path.join(self.models_dir, "cache_manifest.json")
        self.cache_manifest = self._load_cache_manifest()

        # Initialize MinIO variables
        self.minio_endpoint = None
        self.minio_access_key = None
        self.minio_bucket = None
        self.minio_error = None

        # Initialize MinIO client if available
        self.minio_client = self._initialize_minio()

        # Initialize MongoDB connection
        self._init_mongodb()

        # Initialize model loading statistics
        self._init_model_stats()

        # Log successful initialization
        logger.info(f"Model manager initialized with {len(self.models_to_download)} models")
        logger.info(f"Model cache directory: {self.models_dir}")

    def get_model_for_task(self, task: str, custom_model_id: Optional[str] = None) -> Any:
        """
        Get a model for a specific task.

        Args:
            task: Task name (e.g., 'text-generation', 'image-classification')
            custom_model_id: Optional specific model ID to get

        Returns:
            Model instance or None if not found
        """
        try:
            # If custom_model_id is provided, try to get that specific model
            if custom_model_id:
                model_key = f"{task}:{custom_model_id}"
                if model_key in self.models:
                    return self.models[model_key]

                # Try without task prefix
                if custom_model_id in self.models:
                    return self.models[custom_model_id]

            # Look for any model that matches the task
            for model_key, model in self.models.items():
                if model_key.startswith(f"{task}:"):
                    return model

                # Check model info for task match
                if model_key in self.model_info:
                    model_info = self.model_info[model_key]
                    if model_info.get("task") == task:
                        return model

            logger.warning(f"No model found for task {task}")
            return None

        except Exception as e:
            logger.error(f"Error getting model for task {task}: {str(e)}")
            return None

    def get_model(self, model_id: str, task: Optional[str] = None) -> Any:
        """
        Get a model by ID and optionally task.

        Args:
            model_id: Model ID to get
            task: Optional task name

        Returns:
            Model instance or None if not found
        """
        try:
            # Try with task prefix first
            if task:
                model_key = f"{task}:{model_id}"
                if model_key in self.models:
                    return self.models[model_key]

            # Try direct model_id
            if model_id in self.models:
                return self.models[model_id]

            # Search through all models
            for model_key, model in self.models.items():
                if model_key.endswith(f":{model_id}") or model_key == model_id:
                    return model

            logger.warning(f"Model {model_id} not found")
            return None

        except Exception as e:
            logger.error(f"Error getting model {model_id}: {str(e)}")
            return None



    def _ensure_valid_model_id(self, model_id):
        """
        Ensure that the model ID is valid.

        Args:
            model_id: The model ID to validate

        Returns:
            str: The validated model ID
        """
        # Handle None or empty strings
        if not model_id:
            logger.warning("Empty model_id provided, using default")
            return "default"

        # Handle standard string-like objects
        if isinstance(model_id, (str, bytes, os.PathLike)):
            # Already a valid type
            return str(model_id)

        # Fallback to a default value
        logger.warning(f"Invalid model_id type: {type(model_id)}, using default")
        return "default"

    def register_model(
        self,
        model_id: str,
        task: str,
        model: Any,
        status: str = "loaded",
        loading_time: Optional[float] = None,
        error: Optional[str] = None,
        fallback_used: bool = False,
        fallback_model_id: Optional[str] = None,
        desc: Optional[str] = ""
    ) -> None:
        """
        Register a pre-loaded model with the model manager.

        Args:
            model_id: The model ID
            task: The task name
            model: The model object
            status: Model status (loaded, fallback, etc.)
            loading_time: Time taken to load the model in seconds
            error: Error message if loading failed
            fallback_used: Whether a fallback model was used
            fallback_model_id: The ID of the fallback model if one was used
            desc: Description of the model
        """
        try:
            # Ensure model_id is valid
            model_id = self._ensure_valid_model_id(model_id)

            # Create a valid key
            model_key = f"{task}:{model_id}"

            with self._loading_lock:
                # Store the model
                self.models[model_key] = model

                # Store model info
                self.model_info[model_key] = {
                    "model_id": model_id,
                    "task": task,
                    "loaded_at": time.time(),
                    "status": status,
                    "loading_time": loading_time,
                    "error": error,
                    "fallback_used": fallback_used,
                    "fallback_model_id": fallback_model_id,
                    "desc": desc
                }

                logger.info(f"Registered model: {model_key} (status: {status})")
        except Exception as e:
            logger.error(f"Error registering model {model_id} for task {task}: {str(e)}")
            # Re-raise the exception to be handled by the caller
            raise

    def _init_model_stats(self):
        """Initialize model loading statistics."""
        self.loading_stats = {
            "total_models": len(self.models_to_download),
            "loaded_models": 0,
            "failed_models": 0,
            "loading_models": 0,
            "pending_models": len(self.models_to_download),
            "start_time": time.time(),
            "end_time": None,
            "total_time": None,
            "models": {}
        }

        # Initialize stats for each model
        for model_info in self.models_to_download:
            model_key = f"{model_info['model_id']}:{model_info['task']}"
            self.loading_stats["models"][model_key] = {
                "model_id": model_info['model_id'],
                "task": model_info['task'],
                "status": "pending",
                "start_time": None,
                "end_time": None,
                "loading_time": None,
                "error": None,
                "fallback_used": False,
                "fallback_model_id": model_info.get('fallback_model_id')
            }

    def _init_mongodb(self):
        """Initialize MongoDB connection."""
        try:
            # Import MongoDB manager
            from app.db.mongodb import mongodb_manager
            self.mongodb_manager = mongodb_manager

            # Connect to MongoDB
            if not self.mongodb_manager.is_connected:
                self.mongodb_manager.connect()

            # Create indexes for model repository
            try:
                model_repo = self.mongodb_manager.get_collection("model_repository")

                # Create index on model_id and task fields
                model_repo.create_index([("model_id", 1), ("task", 1)], unique=True)

                logger.info("Created indexes for model_repository collection")
            except Exception as e:
                logger.error(f"Error creating indexes: {str(e)}")

            logger.info("MongoDB connection initialized for model manager")
        except Exception as e:
            logger.error(f"Error initializing MongoDB connection: {str(e)}")
            self.mongodb_manager = None

    def _load_cache_manifest(self):
        """Load the cache manifest file or create a new one if it doesn't exist.

        Returns:
            dict: The cache manifest
        """
        if os.path.exists(self.cache_manifest_path):
            try:
                with open(self.cache_manifest_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading cache manifest: {str(e)}")
                return self._create_new_manifest()
        return self._create_new_manifest()

    def _create_new_manifest(self):
        """
        Create a new cache manifest.

        Returns:
            dict: The new cache manifest
        """
        manifest = {
            "models": {},
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "stats": {
                "total_models": len(self.models_to_download),
                "loaded_models": 0,
                "failed_models": 0
            }
        }

        # Save the manifest
        with open(self.cache_manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)

        return manifest

    def _initialize_minio(self):
        """
        Initialize the MinIO client if available.

        Returns:
            MinioClient or None: The MinIO client or None if not available
        """
        try:
            # Import MinIO client
            from minio import Minio

            # Get MinIO settings from environment
            self.minio_endpoint = settings.MINIO_ENDPOINT
            self.minio_access_key = settings.MINIO_ACCESS_KEY
            self.minio_secret_key = settings.MINIO_SECRET_KEY
            self.minio_bucket = settings.MINIO_MODELS_BUCKET

            # Check if MinIO is configured
            if not self.minio_endpoint or not self.minio_access_key or not self.minio_secret_key:
                logger.warning("MinIO not configured, skipping initialization")
                return None

            # Create MinIO client
            client = Minio(
                self.minio_endpoint,
                access_key=self.minio_access_key,
                secret_key=self.minio_secret_key,
                secure=False  # Set to True for HTTPS
            )

            # Create bucket if it doesn't exist
            if not client.bucket_exists(self.minio_bucket):
                client.make_bucket(self.minio_bucket)
                logger.info(f"Created MinIO bucket: {self.minio_bucket}")

            logger.info(f"MinIO client initialized with endpoint: {self.minio_endpoint}")
            return client
        except ImportError:
            logger.warning("MinIO client not available, skipping initialization")
            self.minio_error = "MinIO client not available"
            return None
        except Exception as e:
            logger.error(f"Error initializing MinIO client: {str(e)}")
            self.minio_error = str(e)
            return None

    def _ensure_valid_model_id(self, model_id):
        """
        Ensure that the model ID is valid.

        Args:
            model_id: The model ID to validate

        Returns:
            str: The validated model ID
        """
        # Handle None or empty strings
        if not model_id:
            logger.warning("Empty model_id provided, using default")
            return "default"

        # Handle standard string-like objects
        if isinstance(model_id, (str, bytes, os.PathLike)):
            # Already a valid type
            return str(model_id)

        # Fallback to a default value
        logger.warning(f"Invalid model_id type: {type(model_id)}, using default")
        return "default"

    def register_model(
        self,
        model_id: str,
        task: str,
        model: Any,
        status: str = "loaded",
        loading_time: Optional[float] = None,
        error: Optional[str] = None,
        fallback_used: bool = False
    ) -> None:
        """
        Register a pre-loaded model with the model manager.

        Args:
            model_id: The model ID
            task: The task name
            model: The model object
            status: Model status (loaded, fallback, etc.)
            loading_time: Time taken to load the model in seconds
            error: Error message if loading failed
            fallback_used: Whether a fallback model was used
        """
        try:
            # Ensure model_id is valid
            model_id = self._ensure_valid_model_id(model_id)

            # Create a valid key
            model_key = f"{task}:{model_id}"

            with self._loading_lock:
                # Store the model
                self.models[model_key] = model

                # Store model info
                self.model_info[model_key] = {
                    "model_id": model_id,
                    "task": task,
                    "loaded_at": time.time(),
                    "status": status,
                    "loading_time": loading_time,
                    "error": error,
                    "fallback_used": fallback_used
                }

                # Update loading stats
                if model_key in self.loading_stats["models"]:
                    self.loading_stats["models"][model_key].update({
                        "status": status,
                        "end_time": time.time(),
                        "loading_time": loading_time,
                        "error": error,
                        "fallback_used": fallback_used
                    })

                    if status == "loaded":
                        self.loading_stats["loaded_models"] += 1
                        self.loading_stats["loading_models"] -= 1
                    elif status == "failed":
                        self.loading_stats["failed_models"] += 1
                        self.loading_stats["loading_models"] -= 1

                # Save model info to disk
                self._save_model_info()

                # Save to MongoDB as well
                self._save_model_info_to_db(model_id, task, status, loading_time, error, fallback_used)

                # Upload to MinIO if available
                if self.minio_client and status == "loaded":
                    self.upload_model_to_minio(model_id, task)

            logger.info(f"Registered model {model_id} for {task} with status {status}")

            # Log additional details for failed models
            if status == "failed" and error:
                logger.error(f"Model {model_id} for {task} failed to load: {error}")

            # Log fallback usage
            if fallback_used:
                logger.warning(f"Using fallback model for {task} instead of {model_id}")

        except Exception as e:
            logger.error(f"Error registering model {model_id} for {task}: {str(e)}")
            # Continue without failing

    def is_model_loaded(self, model_id: str, task: Optional[str] = None) -> bool:
        """
        Check if a model is already loaded.

        Args:
            model_id: The model ID to check
            task: Optional task to check for

        Returns:
            bool: True if the model is loaded, False otherwise
        """
        if task:
            model_key = f"{task}:{model_id}"
            if model_key in self.models:
                return True

            # Also check for alternative task formats
            if task == "text-generation":
                alt_key = f"chat:{model_id}"
                if alt_key in self.models:
                    return True
            elif task == "chat":
                alt_key = f"text-generation:{model_id}"
                if alt_key in self.models:
                    return True
        else:
            # Check if model is loaded for any task
            for key in self.models:
                if key.endswith(f":{model_id}"):
                    return True

            # Also check for model aliases
            try:
                from app.config.local_models import MODEL_ALIASES
                for alias, full_id in MODEL_ALIASES.items():
                    if full_id == model_id:
                        for key in self.models:
                            if key.endswith(f":{alias}"):
                                return True
            except ImportError:
                pass

        return False

    def is_model_loading(self, model_id: str, task: str) -> bool:
        """
        Check if a model is currently being loaded.

        Args:
            model_id: The model ID
            task: The task name

        Returns:
            True if the model is being loaded, False otherwise
        """
        model_key = f"{task}:{model_id}"
        if model_key in self.loading_stats["models"]:
            return self.loading_stats["models"][model_key]["status"] == "loading"
        return False

    def register_model(self, model_id: str, task: str, model_obj: Any = None, model: Any = None, status: str = "loaded", loading_time: float = 0, fallback_used: bool = False) -> bool:
        """
        Register an already loaded model with the model manager.

        Args:
            model_id: The model ID
            task: The task name
            model_obj: The loaded model object (deprecated, use model instead)
            model: The loaded model object
            status: The model status
            loading_time: The time it took to load the model
            fallback_used: Whether a fallback model was used

        Returns:
            True if registration was successful, False otherwise
        """
        # For backward compatibility
        if model_obj is not None and model is None:
            model = model_obj
        try:
            model_key = f"{task}:{model_id}"

            # Check if model is already registered
            if model_key in self.models:
                logger.info(f"Model {model_id} already registered for {task}")
                return True

            # Register the model
            self.models[model_key] = model

            # Update loading stats
            if model_key not in self.loading_stats["models"]:
                self.loading_stats["models"][model_key] = {
                    "model_id": model_id,
                    "task": task,
                    "status": status,
                    "loaded_at": datetime.now().isoformat(),
                    "loading_time": loading_time,
                    "fallback_used": fallback_used,
                    "memory_usage": self._estimate_model_memory_usage(model)
                }

            logger.info(f"Successfully registered model {model_id} for {task}")
            return True

        except Exception as e:
            logger.error(f"Error registering model {model_id} for {task}: {str(e)}")
            return False

    def get_model(self, model_id: Optional[str] = None, task: Optional[str] = None):
        """
        Get a model for a specific task or model ID. If the model is not loaded,
        attempt to load it on-demand (lazy loading).

        Args:
            model_id: Optional model ID. If not provided, use the default model for the task.
            task: Optional task name. If not provided with model_id, will search for model across all tasks.

        Returns:
            The model or None if not found
        """
        # Check for model aliases
        try:
            from app.config.local_models import MODEL_ALIASES
            if model_id and model_id.lower() in MODEL_ALIASES:
                alias_model_id = MODEL_ALIASES[model_id.lower()]
                logger.info(f"Using model alias: {model_id} -> {alias_model_id}")
                model_id = alias_model_id
        except ImportError:
            pass

        # If both model_id and task are provided, try to get that specific model
        if model_id and task:
            model_key = f"{task}:{model_id}"
            if model_key in self.models:
                logger.info(f"Found model {model_id} for {task}")
                return self.models[model_key]

            # Check for alternative task formats
            if task == "text-generation":
                alt_key = f"chat:{model_id}"
                if alt_key in self.models:
                    logger.info(f"Found model {model_id} for chat, using for text-generation")
                    return self.models[alt_key]
            elif task == "chat":
                alt_key = f"text-generation:{model_id}"
                if alt_key in self.models:
                    logger.info(f"Found model {model_id} for text-generation, using for chat")
                    return self.models[alt_key]

            # If not found, try to load it
            logger.info(f"Model {model_id} for {task} not loaded, attempting to load")
            return self.load_model_for_task(task, model_id)

        # If only model_id is provided, search for it across all tasks
        elif model_id:
            for key, model in self.models.items():
                if key.endswith(f":{model_id}"):
                    task = key.split(':')[0]
                    logger.info(f"Found model {model_id} for {task}")
                    return model

            # If not found in any task, try to guess the task
            for model_info in self.models_to_download:
                if model_info["model_id"] == model_id:
                    task = model_info["task"]
                    logger.info(f"Found task {task} for model {model_id}, attempting to load")
                    return self.load_model_for_task(task, model_id)

            logger.warning(f"No task found for model {model_id}, cannot load")
            return None

        # If only task is provided, find the best available model for the task
        elif task:
            for model_info in self.models_to_download:
                if model_info["task"] == task:
                    model_id = model_info["model_id"]
                    model_key = f"{task}:{model_id}"

                    if model_key in self.models:
                        logger.info(f"Found default model {model_id} for {task}")
                        return self.models[model_key]

                    # If not found, try to load it
                    logger.info(f"Default model for {task} not loaded, attempting to load {model_id}")
                    return self.load_model_for_task(task, model_id)

        logger.warning(f"No model found for task {task} and model_id {model_id}")
        return None

    def _save_model_info(self):
        """Save model info to disk."""
        try:
            # Update the cache manifest
            self.cache_manifest["models"] = {
                key: {
                    "model_id": info["model_id"],
                    "task": info["task"],
                    "loaded_at": info["loaded_at"],
                    "status": info.get("status", "unknown"),
                    "loading_time": info.get("loading_time"),
                    "error": info.get("error"),
                    "fallback_used": info.get("fallback_used", False)
                }
                for key, info in self.model_info.items()
            }

            # Update stats
            self.cache_manifest["stats"] = {
                "total_models": len(self.models_to_download),
                "loaded_models": self.loading_stats["loaded_models"],
                "failed_models": self.loading_stats["failed_models"],
                "loading_models": self.loading_stats["loading_models"],
                "pending_models": self.loading_stats["pending_models"]
            }

            self.cache_manifest["updated_at"] = datetime.utcnow().isoformat()

            # Save the manifest
            with open(self.cache_manifest_path, 'w') as f:
                json.dump(self.cache_manifest, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving model info: {str(e)}")

    def _save_model_info_to_db(self, model_id, task, status, loading_time=None, error=None, fallback_used=False):
        """
        Save model info to MongoDB.

        Args:
            model_id: The model ID
            task: The task name
            status: Model status (loaded, fallback, etc.)
            loading_time: Time taken to load the model in seconds
            error: Error message if loading failed
            fallback_used: Whether a fallback model was used
        """
        try:
            # Check if MongoDB manager is available
            if not self.mongodb_manager:
                logger.warning("MongoDB manager not available, skipping model info save")
                return

            # Get the model repository collection
            try:
                model_repo = self.mongodb_manager.get_collection("model_repository")

                # Get the local path for the model
                model_cache_dir = os.path.join(settings.MODEL_CACHE_DIR, model_id.replace("/", "--"))

                # Update or insert the model info
                model_repo.update_one(
                    {"model_id": model_id, "task": task},
                    {"$set": {
                        "model_id": model_id,
                        "task": task,
                        "status": status,
                        "loaded_at": datetime.utcnow().isoformat(),
                        "updated_at": datetime.utcnow().isoformat(),
                        "loading_time": loading_time,
                        "error": error,
                        "fallback_used": fallback_used,
                        "local_path": model_cache_dir
                    }},
                    upsert=True
                )

                logger.debug(f"Saved model info to MongoDB for {model_id} ({task})")
            except Exception as e:
                logger.error(f"Error accessing model_repository collection: {str(e)}")
        except Exception as e:
            logger.error(f"Error saving model info to MongoDB: {str(e)}")

    def load_model(self, model_id):
        """
        Load a model by its ID.

        Args:
            model_id: The model ID to load

        Returns:
            The loaded model or None if not found
        """
        # Find the model info
        model_info = None
        for info in self.models_to_download:
            if info["model_id"] == model_id:
                model_info = info
                break

        if not model_info:
            logger.warning(f"No model info found for model_id {model_id}")
            return None

        # Get the task and load the model
        task = model_info["task"]
        return self.load_model_for_task(task, model_id)

    def load_model_for_task(self, task=None, model_id=None):
        """
        Load a model for a specific task.

        Args:
            task: Optional task name (e.g., 'multimodal', 'text-generation')
            model_id: Optional model ID (e.g., 'Salesforce/blip-image-captioning-base')
                    If both are provided, will try to find exact match first

        Returns:
            The loaded model or None if not found
        """
        # Normalize inputs
        task = task.lower() if task else None

        # Check if model is already loaded
        if model_id:
            model_key = f"{task or 'any'}:{model_id}"
            if model_key in self.models:
                logger.info(f"Model {model_id} for {task or 'any task'} already loaded, returning from cache")
                return self.models[model_key]

        # Find the model info using a more flexible lookup
        model_info = None

        # First try: Exact match for both task and model_id if both provided
        if task and model_id:
            for info in self.models_to_download:
                if info["task"] == task and info["model_id"] == model_id:
                    model_info = info
                    break

        # Second try: Match by model_id only (task might be None or different)
        if not model_info and model_id:
            for info in self.models_to_download:
                if info["model_id"] == model_id:
                    model_info = info
                    task = info["task"]  # Update task to match the found model
                    logger.debug(f"Found model by ID only: {model_id}, task updated to: {task}")
                    break

        # Third try: Match by task only (get first model for the task)
        if not model_info and task:
            for info in self.models_to_download:
                if info["task"] == task:
                    model_info = info
                    model_id = info["model_id"]
                    logger.debug(f"Found first model for task '{task}': {model_id}")
                    break

        # If still no match, try case-insensitive search
        if not model_info and (task or model_id):
            for info in self.models_to_download:
                if (task and info["task"].lower() == task.lower()) or \
                   (model_id and info["model_id"].lower() == model_id.lower()):
                    model_info = info
                    task = info["task"]
                    model_id = info["model_id"]
                    logger.debug(f"Found model using case-insensitive match: {model_id} for task: {task}")
                    break

        if not model_info:
            logger.warning(f"No model info found for task {task} and model_id {model_id}")
            return None

        # Get the load function
        load_function_name = model_info["load_function"]

        # Update loading stats
        model_key = f"{task}:{model_info['model_id']}"
        with self._loading_lock:
            if model_key in self.loading_stats["models"]:
                if self.loading_stats["models"][model_key]["status"] == "loading":
                    logger.info(f"Model {model_info['model_id']} for {task} is already being loaded")
                    return None

                self.loading_stats["models"][model_key]["status"] = "loading"
                self.loading_stats["models"][model_key]["start_time"] = time.time()
                self.loading_stats["loading_models"] += 1
                self.loading_stats["pending_models"] -= 1

        # Call the load function
        try:
            start_time = time.time()

            # Check if the load function exists
            if not hasattr(self, load_function_name):
                raise AttributeError(f"Load function {load_function_name} not found")

            load_function = getattr(self, load_function_name)
            model = load_function(model_info["model_id"])

            end_time = time.time()
            loading_time = end_time - start_time

            # Register the model
            self.register_model(
                model_id=model_info["model_id"],
                task=task,
                model=model,
                status="loaded",
                loading_time=loading_time,
                fallback_used=False
            )

            logger.info(f"Successfully loaded model {model_info['model_id']} for {task} in {loading_time:.2f}s")
            return model
        except Exception as e:
            logger.error(f"Error loading model {model_info['model_id']} for {task}: {str(e)}")

            # Update loading stats
            with self._loading_lock:
                if model_key in self.loading_stats["models"]:
                    self.loading_stats["models"][model_key]["status"] = "failed"
                    self.loading_stats["models"][model_key]["end_time"] = time.time()
                    self.loading_stats["models"][model_key]["error"] = str(e)
                    self.loading_stats["failed_models"] += 1
                    self.loading_stats["loading_models"] -= 1

            # Save error to MongoDB
            self._save_model_info_to_db(
                model_id=model_info["model_id"],
                task=task,
                status="failed",
                error=str(e)
            )

            # Try fallback model
            fallback_model_id = model_info.get("fallback_model_id")
            if fallback_model_id:
                logger.info(f"Trying fallback model {fallback_model_id} for {task}")

                try:
                    # Find the fallback model info
                    fallback_model_info = None
                    for info in self.models_to_download:
                        if info["task"] == task and info["model_id"] == fallback_model_id:
                            fallback_model_info = info
                            break

                    if not fallback_model_info:
                        logger.warning(f"No fallback model info found for {fallback_model_id}")
                        return None

                    # Get the fallback load function
                    fallback_load_function_name = fallback_model_info["load_function"]

                    # Check if the load function exists
                    if not hasattr(self, fallback_load_function_name):
                        raise AttributeError(f"Fallback load function {fallback_load_function_name} not found")

                    # Call the fallback load function
                    start_time = time.time()
                    fallback_load_function = getattr(self, fallback_load_function_name)
                    fallback_model = fallback_load_function(fallback_model_id)
                    end_time = time.time()
                    loading_time = end_time - start_time

                    # Register the fallback model
                    self.register_model(
                        model_id=fallback_model_id,
                        task=task,
                        model=fallback_model,
                        status="loaded",
                        loading_time=loading_time,
                        fallback_used=True
                    )

                    logger.info(f"Successfully loaded fallback model {fallback_model_id} for {task} in {loading_time:.2f}s")
                    return fallback_model
                except Exception as fallback_error:
                    logger.error(f"Error loading fallback model {fallback_model_id} for {task}: {str(fallback_error)}")

                    # Update loading stats for fallback model
                    fallback_model_key = f"{task}:{fallback_model_id}"
                    with self._loading_lock:
                        if fallback_model_key in self.loading_stats["models"]:
                            self.loading_stats["models"][fallback_model_key]["status"] = "failed"
                            self.loading_stats["models"][fallback_model_key]["end_time"] = time.time()
                            self.loading_stats["models"][fallback_model_key]["error"] = str(fallback_error)
                            self.loading_stats["failed_models"] += 1

                    # Save fallback error to MongoDB
                    self._save_model_info_to_db(
                        model_id=fallback_model_id,
                        task=task,
                        status="failed",
                        error=str(fallback_error)
                    )

            return None

    def check_model_in_minio(self, model_id, task=None):
        """
        Check if a model exists in MinIO.

        Args:
            model_id: The model ID
            task: Optional task name

        Returns:
            bool: True if the model exists in MinIO, False otherwise
        """
        if not self.minio_client:
            return False

        try:
            # Create the object name
            object_name = f"models/{model_id.replace('/', '--')}"
            if task:
                object_name = f"{object_name}/{task}"

            # Check if the object exists
            try:
                self.minio_client.stat_object(self.minio_bucket, object_name)
                return True
            except Exception:
                return False
        except Exception as e:
            logger.error(f"Error checking model in MinIO: {str(e)}")
            return False

    def download_model_from_minio(self, model_id, task=None):
        """
        Download a model from MinIO.

        Args:
            model_id: The model ID
            task: Optional task name

        Returns:
            str: The path to the downloaded model or None if not found
        """
        if not self.minio_client:
            return None

        try:
            # Create the object name
            object_name = f"models/{model_id.replace('/', '--')}"
            if task:
                object_name = f"{object_name}/{task}"

            # Create the download path
            download_path = os.path.join(self.models_dir, model_id.replace("/", "--"))
            if task:
                download_path = os.path.join(download_path, task)

            # Create the directory if it doesn't exist
            os.makedirs(os.path.dirname(download_path), exist_ok=True)

            # Download the object
            self.minio_client.fget_object(
                self.minio_bucket,
                object_name,
                download_path
            )

            logger.info(f"Downloaded model {model_id} from MinIO to {download_path}")
            return download_path
        except Exception as e:
            logger.error(f"Error downloading model from MinIO: {str(e)}")
            return None

    def upload_model_to_minio(self, model_id, task=None):
        """
        Upload a model to MinIO.

        Args:
            model_id: The model ID
            task: Optional task name

        Returns:
            bool: True if the upload was successful, False otherwise
        """
        if not self.minio_client:
            return False

        try:
            # Create the object name
            object_name = f"models/{model_id.replace('/', '--')}"
            if task:
                object_name = f"{object_name}/{task}"

            # Create the upload path
            upload_path = os.path.join(self.models_dir, model_id.replace("/", "--"))
            if task:
                upload_path = os.path.join(upload_path, task)

            # Check if the file exists
            if not os.path.exists(upload_path):
                logger.warning(f"Model file not found at {upload_path}")
                return False

            # Upload the object
            self.minio_client.fput_object(
                self.minio_bucket,
                object_name,
                upload_path
            )

            logger.info(f"Uploaded model {model_id} to MinIO from {upload_path}")
            return True
        except Exception as e:
            logger.error(f"Error uploading model to MinIO: {str(e)}")
            return False

    def get_loading_stats(self):
        """
        Get model loading statistics.

        Returns:
            dict: Model loading statistics
        """
        with self._loading_lock:
            # Calculate total time if loading is complete
            if self.loading_stats["loaded_models"] + self.loading_stats["failed_models"] == self.loading_stats["total_models"]:
                if not self.loading_stats["end_time"]:
                    self.loading_stats["end_time"] = time.time()
                    self.loading_stats["total_time"] = self.loading_stats["end_time"] - self.loading_stats["start_time"]

            # Create a copy of the stats to avoid threading issues
            stats = {
                "total_models": self.loading_stats["total_models"],
                "loaded_models": self.loading_stats["loaded_models"],
                "failed_models": self.loading_stats["failed_models"],
                "loading_models": self.loading_stats["loading_models"],
                "pending_models": self.loading_stats["pending_models"],
                "start_time": self.loading_stats["start_time"],
                "end_time": self.loading_stats["end_time"],
                "total_time": self.loading_stats["total_time"],
                "models": {}
            }

            # Copy model stats
            for model_key, model_stats in self.loading_stats["models"].items():
                stats["models"][model_key] = model_stats.copy()

            return stats

    def print_model_status_table(self):
        """
        Print a table of model statuses including what's in MinIO, MongoDB, and memory.
        Uses Loguru's color formatting for better visibility.
        """
        try:
            # Get all models from MongoDB
            mongodb_models = {}
            if self.mongodb_manager and self.mongodb_manager.is_connected:
                try:
                    model_repo = self.mongodb_manager.get_collection("model_repository")

                    # Fetch all documents
                    for model_doc in model_repo.find({}):
                        model_id = model_doc.get("model_id")
                        task = model_doc.get("task") or model_doc.get("type", "unknown")
                        status = model_doc.get("status", "unknown")
                        mongodb_models[f"{task}:{model_id}"] = status
                except Exception as e:
                    logger.error(f"Error fetching models from MongoDB: {str(e)}")

            # Get all models from MinIO
            minio_models = {}
            if self.minio_client:
                try:
                    objects = self.minio_client.list_objects(self.minio_bucket, prefix="models/")
                    for obj in objects:
                        object_name = obj.object_name
                        if object_name.startswith("models/"):
                            # Extract model_id from object name
                            parts = object_name.split("/")
                            if len(parts) >= 2:
                                model_id = parts[1].replace("--", "/")
                                task = parts[2] if len(parts) >= 3 else "unknown"
                                minio_models[f"{task}:{model_id}"] = "available"
                except Exception as e:
                    logger.error(f"Error listing MinIO objects: {str(e)}")

            # Get all loaded models
            loaded_models = {}
            for model_key in self.models:
                loaded_models[model_key] = "loaded"

            # Get all models in loading stats
            all_models = set(list(self.loading_stats["models"].keys()) +
                            list(mongodb_models.keys()) +
                            list(minio_models.keys()) +
                            list(loaded_models.keys()))

            # Print header with clean formatting
            logger.info("\n")
            logger.info("=" * 100)
            logger.info(f"{'MODEL STATUS':^100}")
            logger.info("=" * 100)

            # Define column widths
            cols = {
                'model': 40,
                'task': 20,
                'memory': 8,
                'mongodb': 8,
                'minio': 8,
                'status': 15
            }

            # Create header
            header = (
                f"{'Model ID':<{cols['model']}} | "
                f"{'Task':<{cols['task']}} | "
                f"{'Memory':<{cols['memory']}} | "
                f"{'MongoDB':<{cols['mongodb']}} | "
                f"{'MinIO':<{cols['minio']}} | "
                f"{'Status':<{cols['status']}}"
            )

            separator = "-" * 100

            logger.info(header)
            logger.info(separator)

            # Sort models by status priority (loading first, then pending, then loaded, etc.)
            def get_status_priority(model_key):
                if model_key in self.loading_stats["models"]:
                    status = self.loading_stats["models"][model_key]["status"]
                    if status == "loading":
                        return 0
                    elif status == "pending":
                        return 1
                    elif status == "loaded":
                        return 2
                    elif status == "failed":
                        return 3
                    else:
                        return 4
                elif model_key in loaded_models:
                    return 2
                else:
                    return 5

            # Sort by status priority, then by task, then by model_id
            sorted_models = sorted(all_models, key=lambda k: (
                get_status_priority(k),
                k.split(":", 1)[0] if ":" in k else "unknown",
                k.split(":", 1)[1] if ":" in k else k
            ))

            # Print rows
            for model_key in sorted_models:
                parts = model_key.split(":", 1)
                if len(parts) == 2:
                    task, model_id = parts
                else:
                    task, model_id = "unknown", model_key

                # Get status indicators
                memory_status = "YES" if model_key in loaded_models else "NO"
                mongodb_status = "YES" if model_key in mongodb_models else "NO"
                minio_status = "YES" if model_key in minio_models else "NO"

                # Get overall status with more detailed information
                if model_key in self.loading_stats["models"]:
                    status = self.loading_stats["models"][model_key]["status"].upper()
                    # Add loading time if available
                    if status == "LOADED" and self.loading_stats["models"][model_key].get("loading_time"):
                        status = f"{status} ({self.loading_stats['models'][model_key]['loading_time']:.1f}s)"
                    # Add error message if failed
                    elif status == "FAILED" and self.loading_stats["models"][model_key].get("error"):
                        error = self.loading_stats["models"][model_key]["error"]
                        if len(error) > 15:
                            error = error[:12] + "..."
                        status = f"{status}: {error}"
                elif model_key in loaded_models:
                    status = "LOADED"
                elif model_key in mongodb_models:
                    status = mongodb_models[model_key].upper()
                elif model_key in minio_models:
                    status = "AVAILABLE"
                else:
                    status = "UNKNOWN"

                # Truncate model_id if needed
                if len(model_id) > cols['model'] - 1:
                    model_id = model_id[:cols['model']-4] + "..."

                # Truncate task if needed
                if len(task) > cols['task'] - 1:
                    task = task[:cols['task']-4] + "..."

                # Truncate status if needed
                if len(status) > cols['status'] - 1:
                    status = status[:cols['status']-4] + "..."

                # Create row with consistent spacing
                row = (
                    f"{model_id:<{cols['model']}} | "
                    f"{task:<{cols['task']}} | "
                    f"{memory_status:^{cols['memory']}} | "
                    f"{mongodb_status:^{cols['mongodb']}} | "
                    f"{minio_status:^{cols['minio']}} | "
                    f"{status:<{cols['status']}}"
                )

                # Color code based on status
                if status.startswith("LOADING"):
                    logger.info(f"<cyan>{row}</cyan>")
                elif status.startswith("LOADED"):
                    logger.info(f"<green>{row}</green>")
                elif status.startswith("FAILED"):
                    logger.info(f"<red>{row}</red>")
                elif status.startswith("PENDING"):
                    logger.info(f"<yellow>{row}</yellow>")
                else:
                    logger.info(f"<dim>{row}</dim>")

            logger.info(separator)

            # Print summary with clean formatting
            logger.info("\n<b><magenta>MODEL STATISTICS:</magenta></b>")
            logger.info(f"<b>• Total models tracked:</b> {len(all_models)}")
            logger.info(f"<b>• Models loaded in memory:</b> <green>{len(loaded_models)}</green>")
            logger.info(f"<b>• Models registered in MongoDB:</b> {len(mongodb_models)}")
            logger.info(f"<b>• Models available in MinIO:</b> {len(minio_models)}")
            logger.info(f"<b>• Models currently loading:</b> <cyan>{self.loading_stats['loading_models']}</cyan>")
            logger.info(f"<b>• Models pending load:</b> <yellow>{self.loading_stats['pending_models']}</yellow>")
            logger.info(f"<b>• Models failed to load:</b> <red>{self.loading_stats['failed_models']}</red>")

            # Calculate loading progress
            total = self.loading_stats["total_models"]
            loaded = self.loading_stats["loaded_models"]
            failed = self.loading_stats["failed_models"]
            progress = (loaded + failed) / total if total > 0 else 0

            # Create a colorful progress bar
            progress_int = int(progress * 20)
            progress_bar = "<green>" + "█" * progress_int + "</green>" + "<dim>" + "░" * (20 - progress_int) + "</dim>"

            # Color the progress percentage based on value
            if progress < 0.3:
                progress_color = "red"
            elif progress < 0.7:
                progress_color = "yellow"
            else:
                progress_color = "green"

            progress_text = f"<{progress_color}>{progress*100:.1f}%</{progress_color}>"
            logger.info(f"\n<bold>📈 LOADING PROGRESS:</bold> {progress_bar} {progress_text} (<green>{loaded}</green> + <red>{failed}</red>)/<yellow>{total}</yellow>")

            # Print time statistics
            if self.loading_stats["start_time"]:
                elapsed = time.time() - self.loading_stats["start_time"]
                logger.info(f"<bold>⏱️ Time elapsed:</bold> <cyan>{elapsed:.1f}s</cyan>")

                # Estimate remaining time
                if progress > 0 and progress < 1:
                    remaining = (elapsed / progress) - elapsed
                    logger.info(f"<bold>⏳ Estimated time remaining:</bold> <yellow>{remaining:.1f}s</yellow>")

            logger.info("<cyan>" + "=" * 100 + "</cyan>")
            logger.info("\n")

        except Exception as e:
            logger.error(f"Error printing model status table: {str(e)}")
            logger.error(traceback.format_exc())

    def get_model_stats(self, model_id, task):
        """
        Get statistics for a specific model.

        Args:
            model_id: The model ID
            task: The task name

        Returns:
            dict: Model statistics or None if not found
        """
        model_key = f"{task}:{model_id}"
        with self._loading_lock:
            if model_key in self.loading_stats["models"]:
                return self.loading_stats["models"][model_key].copy()
        return None

    def preload_models(self, priority_threshold=2):
        """
        Preload models with priority less than or equal to the threshold.

        Args:
            priority_threshold: Priority threshold (1=highest, 3=lowest)

        Returns:
            List[concurrent.futures.Future]: List of futures for the loading tasks
        """
        futures = []

        # First, check for essential models from configuration
        try:
            from app.config.models import ESSENTIAL_MODELS, DEFAULT_MODELS, MODEL_PRIORITIES

            # Add default models with their priorities
            essential_models = []
            for task, model_list in ESSENTIAL_MODELS.items():
                priority = MODEL_PRIORITIES.get(task, 3)
                if priority <= priority_threshold:
                    for model_info in model_list:
                        model_id = model_info.get("model_id")
                        if model_id:
                            essential_models.append({
                                "model_id": model_id,
                                "task": task,
                                "priority": priority
                            })

            # Add essential models to the preload list
            if essential_models:
                logger.info(f"Adding {len(essential_models)} essential models to preload list")
                # Combine with existing models to download
                models_to_preload = essential_models + [
                    model for model in self.models_to_download
                    if model.get("priority", 3) <= priority_threshold
                ]
            else:
                # Use existing models to download
                models_to_preload = [
                    model for model in self.models_to_download
                    if model.get("priority", 3) <= priority_threshold
                ]
        except ImportError:
            logger.warning("Could not import model configuration, using fallback method")
            # Fallback to original method
            models_to_preload = [
                model for model in self.models_to_download
                if model.get("priority", 3) <= priority_threshold
            ]

        # Remove duplicates
        unique_models = {}
        for model in models_to_preload:
            key = f"{model['model_id']}_{model['task']}"
            if key not in unique_models or model.get("priority", 3) < unique_models[key].get("priority", 3):
                unique_models[key] = model

        models_to_preload = list(unique_models.values())

        logger.info(f"Preloading {len(models_to_preload)} models with priority <= {priority_threshold}")

        # Sort by priority (lowest number = highest priority)
        models_to_preload.sort(key=lambda x: x.get("priority", 3))

        # Start loading tasks
        for model in models_to_preload:
            model_id = model["model_id"]
            task = model["task"]

            # Skip if already loaded or loading
            if self.is_model_loaded(model_id, task) or self.is_model_loading(model_id, task):
                continue

            # Submit loading task to thread pool
            future = self._executor.submit(self.load_model_for_task, task, model_id)
            futures.append(future)

            logger.info(f"Submitted preloading task for {model_id} ({task})")

        return futures

    def load_models_async(self):
        """
        Load all models asynchronously in the background.

        This method starts loading all models in separate threads, prioritizing
        text generation models first.
        """
        # First, load high-priority models (text generation)
        high_priority_futures = self.preload_models(priority_threshold=1)

        # Then load medium-priority models
        medium_priority_futures = self.preload_models(priority_threshold=2)

        # Finally, load low-priority models
        low_priority_futures = self.preload_models(priority_threshold=3)

        # Combine all futures
        all_futures = high_priority_futures + medium_priority_futures + low_priority_futures

        # Log the number of models being loaded
        logger.info(f"Loading {len(all_futures)} models asynchronously in the background")

        # Return all futures
        return all_futures

    def start_background_loading(self):
        """
        Start loading models in the background.

        This method is called during application startup to begin loading models
        asynchronously without blocking the application startup.
        """
        logger.info("Starting background model loading...")

        # Print initial model status table
        logger.info("Initial model status:")
        self.print_model_status_table()

        # Create a thread to load models asynchronously
        thread = threading.Thread(
            target=self._background_loading_thread,
            name="ModelLoaderThread",
            daemon=True  # Make the thread a daemon so it doesn't block application shutdown
        )

        # Start the thread
        thread.start()

        logger.info("Background model loading thread started")

    def _background_loading_thread(self):
        """
        Background thread function for loading models.
        """
        try:
            # Load models asynchronously
            futures = self.load_models_async()

            # Set up periodic status updates
            start_time = time.time()
            last_update_time = start_time
            update_interval = 30  # Update status every 30 seconds

            # Wait for all futures to complete
            pending = list(futures)
            while pending:
                # Check if it's time for a status update
                current_time = time.time()
                if current_time - last_update_time >= update_interval:
                    logger.info(f"Model loading progress update (after {int(current_time - start_time)}s):")
                    self.print_model_status_table()
                    last_update_time = current_time

                # Wait for some futures to complete
                done, pending = concurrent.futures.wait(
                    pending,
                    timeout=5,  # Check status every 5 seconds
                    return_when=concurrent.futures.FIRST_COMPLETED
                )

                # Process completed futures
                for future in done:
                    try:
                        # Get the result (this will raise any exceptions that occurred during loading)
                        future.result()
                    except Exception as e:
                        logger.error(f"Error in background model loading: {str(e)}")

            # Print final model status table
            logger.info("Final model status after background loading:")
            self.print_model_status_table()

            # Calculate total loading time
            total_time = time.time() - start_time
            logger.info(f"Background model loading completed in {total_time:.2f} seconds")

            # Log summary of loaded models by task
            loaded_by_task = {}
            for model_key in self.models:
                task = model_key.split(":", 1)[0]
                if task not in loaded_by_task:
                    loaded_by_task[task] = []
                loaded_by_task[task].append(model_key.split(":", 1)[1])

            logger.info("Loaded models by task:")
            for task, models in loaded_by_task.items():
                logger.info(f"  {task}: {len(models)} models")
                for model_id in models:
                    logger.info(f"    - {model_id}")

        except Exception as e:
            logger.error(f"Error in background loading thread: {str(e)}")
            logger.error(traceback.format_exc())

    def _load_sentence_transformer(self, model_id):
        """
        Load a sentence transformer model.

        Args:
            model_id: The model ID

        Returns:
            The loaded model
        """
        try:
            from sentence_transformers import SentenceTransformer

            logger.info(f"Loading sentence transformer model: {model_id}")
            model = SentenceTransformer(model_id)

            # Apply model optimization if available
            if model_optimizer:
                model = model_optimizer.optimize_model(model, model_id, "sentence-transformer")

            return model
        except Exception as e:
            logger.error(f"Error loading sentence transformer model {model_id}: {str(e)}")
            raise



    def _load_text_generation_model(self, model_id):
        """Load a text generation model with robust error handling.

        Args:
            model_id: The model ID

        Returns:
            The loaded model pipeline
        """
        try:
            logger.info(f"Loading text generation model: {model_id}")

            # Force CPU if specified or no GPU available
            use_cpu = not self.torch.cuda.is_available() or settings.LOCAL_MODEL_FORCE_CPU
            device = 'cpu' if use_cpu else 0
            torch_dtype = self.torch.float32 if use_cpu else self.torch.float16

            # Load tokenizer first
            tokenizer = self.AutoTokenizer.from_pretrained(model_id)

            # Try different loading strategies based on available hardware
            if use_cpu:
                strategies = [self._load_vanilla]
            else:
                strategies = [self._load_vanilla]  # Simplified to only use vanilla loading

            last_error = None

            for strategy in strategies:
                try:
                    # Load the model with the current strategy
                    model = strategy(model_id, device, torch_dtype)

                    # Create pipeline with pre-loaded model (no device parameters needed)
                    # Don't pass trust_remote_code again since model was already loaded with it
                    pipeline_kwargs = {
                        "model": model,
                        "tokenizer": tokenizer,
                        "torch_dtype": torch_dtype
                    }

                    # Create the pipeline without device parameters since model is already on CPU
                    generator = self.pipeline("text-generation", **pipeline_kwargs)

                    logger.info(f"Successfully loaded {model_id} using {strategy.__name__}")
                    return generator

                except Exception as e:
                    last_error = e
                    logger.warning(f"Failed to load {model_id} with {strategy.__name__}: {str(e)}")
                    # Clean up any CUDA memory that might be left
                    if self.torch.cuda.is_available():
                        self.torch.cuda.empty_cache()
                    continue

            # If we get here, all strategies failed
            raise RuntimeError(f"All loading strategies failed for {model_id}: {str(last_error)}")

        except Exception as e:
            logger.error(f"Error loading text generation model {model_id}: {str(e)}")
            raise

    def _load_vanilla(self, model_id, device, torch_dtype):
        """Load model using standard from_pretrained with device handling."""
        try:
            # Load model with explicit device_map=None to completely disable accelerate
            model = self.AutoModelForCausalLM.from_pretrained(
                model_id,
                torch_dtype=torch_dtype,
                low_cpu_mem_usage=True,
                trust_remote_code=True,
                device_map=None  # Explicitly disable accelerate
            )

            # Always move to CPU for consistency
            model = model.to('cpu')
            model = model.eval()
            return model

        except Exception as e:
            logger.error(f"Error in _load_vanilla for {model_id}: {str(e)}")
            raise

    def _load_summarization_model(self, model_id):
        """
        Load a summarization model.

        Args:
            model_id: The model ID

        Returns:
            The loaded model
        """
        try:
            from transformers import AutoModelForSeq2SeqLM, AutoTokenizer, pipeline

            logger.info(f"Loading summarization model: {model_id}")

            # Load tokenizer first
            tokenizer = AutoTokenizer.from_pretrained(model_id)

            # Load model with progress bar
            with ProgressBar(desc=f"Loading {model_id}") as progress:
                model = AutoModelForSeq2SeqLM.from_pretrained(
                    model_id,
                    device_map="auto" if torch.cuda.is_available() and not settings.LOCAL_MODEL_FORCE_CPU else "cpu",
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
                )

            # Apply model optimization if available
            if model_optimizer:
                model = model_optimizer.optimize_model(model, model_id, "summarization")

            # Create a pipeline
            summarizer = pipeline("summarization", model=model, tokenizer=tokenizer)

            return summarizer
        except Exception as e:
            logger.error(f"Error loading summarization model {model_id}: {str(e)}")
            raise

    def _load_speech_to_text_model(self, model_id):
        """
        Load a speech-to-text model.

        Args:
            model_id: The model ID

        Returns:
            The loaded model
        """
        try:
            from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor, pipeline

            logger.info(f"Loading speech-to-text model: {model_id}")

            # Determine device
            device = "cuda" if torch.cuda.is_available() and not settings.LOCAL_MODEL_FORCE_CPU else "cpu"
            torch_dtype = torch.float16 if device == "cuda" else torch.float32

            # Load processor first
            processor = AutoProcessor.from_pretrained(model_id)

            # Load model with progress bar
            with ProgressBar(desc=f"Loading {model_id}") as progress:
                model = AutoModelForSpeechSeq2Seq.from_pretrained(
                    model_id,
                    torch_dtype=torch_dtype,
                    low_cpu_mem_usage=True,
                    use_safetensors=True
                )

                # Move model to device after loading
                model = model.to(device)

            # Apply model optimization if available
            if model_optimizer:
                model = model_optimizer.optimize_model(model, model_id, "speech-to-text")

            # Create a pipeline with explicit device mapping
            transcriber = pipeline(
                "automatic-speech-recognition",
                model=model,
                tokenizer=processor.tokenizer,
                feature_extractor=processor.feature_extractor,
                device=0 if device == "cuda" else -1,
                torch_dtype=torch_dtype
            )

            return transcriber
        except Exception as e:
            logger.error(f"Error loading speech-to-text model {model_id}: {str(e)}")
            raise

    def _load_text_to_speech_model(self, model_id):
        """
        Load a text-to-speech model.

        Args:
            model_id: The model ID

        Returns:
            The loaded model
        """
        try:
            from transformers import SpeechT5Processor, SpeechT5ForTextToSpeech, SpeechT5HifiGan

            logger.info(f"Loading text-to-speech model: {model_id}")

            # Load processor first
            processor = SpeechT5Processor.from_pretrained(model_id)

            # Load model with progress bar
            with ProgressBar(desc=f"Loading {model_id}") as progress:
                model = SpeechT5ForTextToSpeech.from_pretrained(
                    model_id,
                    device_map="auto" if torch.cuda.is_available() and not settings.LOCAL_MODEL_FORCE_CPU else "cpu",
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
                )

            # Load vocoder
            vocoder = SpeechT5HifiGan.from_pretrained("microsoft/speecht5_hifigan")

            # Apply model optimization if available
            if model_optimizer:
                model = model_optimizer.optimize_model(model, model_id, "text-to-speech")

            # Create a custom pipeline
            class TextToSpeechPipeline:
                def __init__(self, model, processor, vocoder):
                    self.model = model
                    self.processor = processor
                    self.vocoder = vocoder

                def __call__(self, text, speaker_embeddings=None):
                    inputs = self.processor(text=text, return_tensors="pt")

                    if speaker_embeddings is None:
                        # Use a default speaker embedding
                        import torch
                        import numpy as np
                        speaker_embeddings = torch.tensor(np.random.random(512)).unsqueeze(0)

                    speech = self.model.generate_speech(inputs["input_ids"], speaker_embeddings)
                    audio = self.vocoder(speech)
                    return audio

            tts_pipeline = TextToSpeechPipeline(model, processor, vocoder)

            return tts_pipeline
        except Exception as e:
            logger.error(f"Error loading text-to-speech model {model_id}: {str(e)}")
            raise

    def _load_image_classification_model(self, model_id):
        """
        Load an image classification model.

        Args:
            model_id: The model ID

        Returns:
            The loaded model
        """
        try:
            from transformers import AutoModelForImageClassification, AutoFeatureExtractor, pipeline

            logger.info(f"Loading image classification model: {model_id}")

            # Determine device
            device = "cuda" if torch.cuda.is_available() and not settings.LOCAL_MODEL_FORCE_CPU else "cpu"
            torch_dtype = torch.float16 if device == "cuda" else torch.float32

            # Load feature extractor first
            feature_extractor = AutoFeatureExtractor.from_pretrained(model_id)

            # Load model with progress bar
            with ProgressBar(desc=f"Loading {model_id}") as progress:
                model = AutoModelForImageClassification.from_pretrained(
                    model_id,
                    torch_dtype=torch_dtype,
                    low_cpu_mem_usage=True,
                    use_safetensors=True
                )

                # Move model to device after loading
                model = model.to(device)

            # Apply model optimization if available
            if model_optimizer:
                model = model_optimizer.optimize_model(model, model_id, "image-classification")

            # Create a pipeline with explicit device mapping
            classifier = pipeline(
                "image-classification",
                model=model,
                feature_extractor=feature_extractor,
                device=0 if device == "cuda" else -1,
                torch_dtype=torch_dtype
            )

            return classifier
        except Exception as e:
            logger.error(f"Error loading image classification model {model_id}: {str(e)}")
            raise

    def _load_text_to_image_model(self, model_id):
        """
        Load a text-to-image model.

        Args:
            model_id: The model ID

        Returns:
            The loaded model
        """
        try:
            from diffusers import StableDiffusionPipeline

            logger.info(f"Loading text-to-image model: {model_id}")

            # Load model with progress bar
            with ProgressBar(desc=f"Loading {model_id}") as progress:
                pipeline = StableDiffusionPipeline.from_pretrained(
                    model_id,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
                )

                if torch.cuda.is_available() and not settings.LOCAL_MODEL_FORCE_CPU:
                    pipeline = pipeline.to("cuda")

            # Apply model optimization if available
            if model_optimizer:
                pipeline = model_optimizer.optimize_model(pipeline, model_id, "text-to-image")

            return pipeline
        except Exception as e:
            logger.error(f"Error loading text-to-image model {model_id}: {str(e)}")
            raise

    def _load_image_to_image_model(self, model_id):
        """
        Load an image-to-image model.

        Args:
            model_id: The model ID

        Returns:
            The loaded model
        """
        try:
            from diffusers import StableDiffusionImg2ImgPipeline

            logger.info(f"Loading image-to-image model: {model_id}")

            # Load model with progress bar
            with ProgressBar(desc=f"Loading {model_id}") as progress:
                pipeline = StableDiffusionImg2ImgPipeline.from_pretrained(
                    model_id,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
                )

                if torch.cuda.is_available() and not settings.LOCAL_MODEL_FORCE_CPU:
                    pipeline = pipeline.to("cuda")

            # Apply model optimization if available
            if model_optimizer:
                pipeline = model_optimizer.optimize_model(pipeline, model_id, "image-to-image")

            return pipeline
        except Exception as e:
            logger.error(f"Error loading image-to-image model {model_id}: {str(e)}")
            raise

    def _estimate_model_memory_usage(self, model):
        """
        Estimate the memory usage of a model in bytes.

        Args:
            model: The model to estimate memory usage for

        Returns:
            int: Estimated memory usage in bytes
        """
        try:
            # If the model has a get_memory_footprint method, use it
            if hasattr(model, 'get_memory_footprint'):
                return model.get_memory_footprint()

            # For PyTorch models, estimate based on parameters
            if hasattr(model, 'parameters'):
                param_size = 0
                for param in model.parameters():
                    param_size += param.nelement() * param.element_size()

                buffer_size = 0
                for buffer in model.buffers():
                    buffer_size += buffer.nelement() * buffer.element_size()

                return param_size + buffer_size

            # Default fallback
            return 0

        except Exception as e:
            logger.warning(f"Error estimating model memory usage: {str(e)}")
            return 0

    def _load_multimodal_model(self, model_id):
        """
        Load a multimodal model.

        Args:
            model_id: The model ID

        Returns:
            The loaded model
        """
        try:
            import torch
            from transformers import AutoProcessor, AutoModelForCausalLM, BlipProcessor, BlipForConditionalGeneration

            logger.info(f"Loading multimodal model: {model_id}")

            # Determine device
            device = "cuda" if torch.cuda.is_available() and not settings.LOCAL_MODEL_FORCE_CPU else "cpu"
            torch_dtype = torch.float16 if device == "cuda" else torch.float32

            # Load model with progress bar
            with ProgressBar(desc=f"Loading {model_id}") as progress:
                # Handle different model types
                if "blip" in model_id.lower():
                    # BLIP models
                    processor = BlipProcessor.from_pretrained(model_id)
                    model = BlipForConditionalGeneration.from_pretrained(
                        model_id,
                        torch_dtype=torch_dtype,
                        low_cpu_mem_usage=True
                    )
                else:
                    # Generic multimodal models
                    processor = AutoProcessor.from_pretrained(model_id)
                    model = AutoModelForCausalLM.from_pretrained(
                        model_id,
                        torch_dtype=torch_dtype,
                        low_cpu_mem_usage=True
                    )

                # Move model to device after loading
                model = model.to(device)

                # Create a dictionary with processor and model
                multimodal_model = {
                    "processor": processor,
                    "model": model,
                    "device": device
                }

            # Apply model optimization if available
            if model_optimizer:
                multimodal_model = model_optimizer.optimize_model(multimodal_model, model_id, "multimodal")

            return multimodal_model
        except Exception as e:
            logger.error(f"Error loading multimodal model {model_id}: {str(e)}")
            raise

# Create a singleton instance
model_manager = ModelManager()
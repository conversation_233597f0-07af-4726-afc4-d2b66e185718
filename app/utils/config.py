"""
Application configuration.
"""
import os
import json
from typing import List, Dict, Any, Optional, Union, get_type_hints
from pydantic import field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from functools import lru_cache
import json
import re

class Settings(BaseSettings):
    """Application settings."""

    @model_validator(mode='before')
    @classmethod
    def clean_boolean_values(cls, data):
        """Clean boolean values by removing comments and converting string values to proper booleans."""
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, str) and key.upper() in [
                    'LOCAL_MODEL_FORCE_CPU', 'USE_LOCAL_MODELS', 'DISABLE_ML_MODELS',
                    'ENABLE_TOOLS', 'DEBUG', 'RATE_LIMIT_ENABLED', 'DIRECT_LANGUAGE_GENERATION',
                    'USE_LOCAL_WHISPER', 'USE_LOCAL_TTS', 'ENABLE_LORA_TRAINING',
                    'DASHBOARD_ENABLED', 'DASHBOARD_AUTH_REQUIRED', 'ENABLE_NSFW_DETECTION',
                    'ENABLE_FACE_DETECTION', 'DEEPFAKE_DETECTION_ENABLED', 'ENABLE_OCR',
                    'KAFKA_ENABLED', 'SMTP_USE_TLS', 'ENABLE_BILLING', 'SIM_LLM_PRELOAD_MODELS'
                ]:
                    # Remove comments from boolean values
                    if '#' in value:
                        value = value.split('#')[0].strip()

                    # Convert string boolean values to actual booleans
                    if value.lower() in ('true', 't', 'yes', 'y', '1'):
                        data[key] = True
                    elif value.lower() in ('false', 'f', 'no', 'n', '0'):
                        data[key] = False
                    else:
                        data[key] = value
        return data

    # API Settings
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    DEBUG: bool = True
    ENVIRONMENT: str = "development"
    LOG_LEVEL: str = "INFO"

    # Hugging Face Settings
    HUGGINGFACE_TOKEN: Optional[str] = None  # We're using only public models, so no token is needed

    # Security
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    CORS_ORIGINS: Union[str, List[str]] = ["http://localhost:3000", "http://localhost:8080"]

    # MinIO Settings
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin"
    MINIO_MODELS_BUCKET: str = "models"
    MINIO_USE_SSL: bool = False

    @model_validator(mode='before')
    @classmethod
    def validate_cors_origins(cls, data):
        if isinstance(data, dict) and 'CORS_ORIGINS' in data:
            if isinstance(data['CORS_ORIGINS'], str):
                # Handle comma-separated string from environment variable
                data['CORS_ORIGINS'] = [origin.strip() for origin in data['CORS_ORIGINS'].split(',') if origin.strip()]
        return data

    # Frontend URL (for password reset links, etc.)
    FRONTEND_URL: str = "http://localhost:8000"

    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_DEFAULT: str = "100/minute"
    RATE_LIMIT_PREMIUM: str = "500/minute"

    # LLM Settings
    DEFAULT_MODEL: str = "distilgpt2"  # Use distilgpt2 as it's more reliable
    FALLBACK_MODEL: str = "gpt2"  # Use gpt2 as fallback for faster loading
    DEFAULT_SYSTEM_PROMPT: str = """You are SimbaAI, a next-generation artificial intelligence assistant developed with advanced reasoning capabilities.

You excel at understanding complex queries, providing thoughtful and accurate responses, and adapting to the user's needs. Your responses are clear, helpful, and tailored to each specific request.

As SimbaAI, you combine the analytical precision of a research tool with the conversational fluency of a helpful assistant. You're designed to be informative while maintaining a friendly, professional tone.

When you don't know something, you acknowledge your limitations rather than making up information. You prioritize user privacy and security in all interactions.

Your goal is to provide maximum value to users through high-quality, relevant assistance."""
    ENABLE_TOOLS: bool = True
    MAX_AGENT_STEPS: int = 5

    # OpenAI (disabled)
    # We only use local models, no external API keys needed

    # Local Models Configuration
    USE_LOCAL_MODELS: bool = True
    LOCAL_MODEL_PATH: str = "./data/model_cache/local_models/tinyllama"
    LOCAL_MODEL_NAME: str = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
    LOCAL_MODEL_DEVICE: str = "cpu"
    LOCAL_MODEL_QUANTIZATION: str = "none"  # Options: none, 4bit, 8bit
    LOCAL_MODEL_FORCE_CPU: bool = True  # Force CPU usage even if GPU is available

    # Cache Directories
    HF_HOME: str = "./data/model_cache"
    TRANSFORMERS_CACHE: str = "./data/model_cache"
    HF_DATASETS_CACHE: str = "./data/model_cache/datasets"
    HUGGINGFACE_HUB_CACHE: str = "./data/model_cache/hub"
    HF_HUB_CACHE: str = "./data/model_cache/hub"
    HF_ASSETS_CACHE: str = "./data/model_cache/assets"

    # ML Model Settings
    DISABLE_ML_MODELS: bool = True  # Set to True to disable all ML models and use fallbacks
    SIM_LLM_PRELOAD_MODELS: bool = False  # Preload all models during application startup

    # Model Loading Settings
    USE_LOCAL_MULTIMODAL_MODELS: bool = True  # Enable local multimodal models
    LOCAL_MULTIMODAL_MODEL: str = "laion/CLIP-ViT-B-32-laion2B-s34B-b79K"  # Default multimodal model

    # Image Processing Models
    ENABLE_FACE_DETECTION: bool = True
    IMAGE_CLASSIFICATION_MODEL: str = "microsoft/resnet-18"  # Changed from google/vit-base-patch16-224 for better compatibility
    OBJECT_DETECTION_MODEL: str = "hustvl/yolos-tiny"  # Smaller object detection model
    OCR_MODEL: str = "microsoft/trocr-base-handwritten"  # OCR model
    NSFW_DETECTION_MODEL: str = "Falconsai/nsfw_image_detection"  # NSFW detection model

    # DeepSeek (disabled)
    # We only use local models

    # Redis (Session Memory)
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0
    REDIS_PREFIX: str = "sim_llm:"
    REDIS_CONVERSATION_TTL: int = 86400  # 24 hours in seconds
    REDIS_URI: Optional[str] = None  # Redis connection URI (overrides individual settings if provided)

    # MongoDB (Analytics, Prompts, Training)
    # Using admin authentication for MongoDB - ensure no escaped characters in the URI
    MONGODB_URI: str = "**************************************************************"
    MONGODB_DB: str = "sim_llm"
    MONGODB_USERNAME: str = "admin"
    MONGODB_PASSWORD: str = "admin"

    @model_validator(mode='before')
    @classmethod
    def validate_mongodb_uri(cls, data):
        if isinstance(data, dict) and 'MONGODB_URI' in data:
            # Replace any escaped characters in the URI
            uri = data['MONGODB_URI']
            uri = uri.replace('\\x3a', ':')
            uri = uri.replace('\\x2f', '/')
            uri = uri.replace('\\x40', '@')
            uri = uri.replace('\\x3f', '?')
            uri = uri.replace('\\x3d', '=')
            data['MONGODB_URI'] = uri
        return data

    # Qdrant (Semantic Memory)
    QDRANT_URL: str = "http://localhost:6333"
    QDRANT_COLLECTION: str = "sim_llm_memory"
    QDRANT_API_KEY: Optional[str] = None
    EMBEDDING_MODEL: str = "all-MiniLM-L6-v2"

    # Language Settings
    DIRECT_LANGUAGE_GENERATION: bool = False

    # Voice Settings
    USE_LOCAL_WHISPER: bool = True
    USE_LOCAL_TTS: bool = True  # Changed to True to use local TTS by default
    WHISPER_MODEL: str = "distil-whisper/distil-small.en"  # Small, fast model for speech recognition
    TTS_MODEL: str = "coqui/xtts"  # Default TTS model

    # Training Settings
    # Using only local models for fine-tuning
    LOCAL_FINE_TUNING_BASE_MODEL: str = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
    HUGGINGFACE_BASE_MODEL: str = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
    DAILY_TRAINING_PROVIDER: str = "huggingface"  # Using only huggingface for training
    WEEKLY_TRAINING_PROVIDER: str = "huggingface"  # Using only huggingface for training
    ENABLE_LORA_TRAINING: bool = True  # Use LoRA for parameter-efficient fine-tuning
    ENABLE_DEEPSPEED: bool = True  # DeepSpeed is now required for optimal training performance
    DEEPSPEED_MODEL_SIZE: str = "small_model"  # Options: small_model, medium_model, large_model, multi_gpu
    DEEPSPEED_ZERO_STAGE: int = 2  # ZeRO optimization stage (0, 1, 2, or 3)
    DEEPSPEED_OFFLOAD_OPTIMIZER: bool = True  # Offload optimizer states to CPU
    DEEPSPEED_OFFLOAD_PARAM: bool = False  # Offload parameters to CPU

    # Dashboard Settings
    DASHBOARD_ENABLED: bool = True
    DASHBOARD_AUTH_REQUIRED: bool = True

    # File Storage
    UPLOAD_DIR: str = "./uploads"
    MODELS_DIR: str = "./data/models"
    MODEL_CACHE_DIR: str = "./data/model_cache"  # Cache directory for downloaded models (same as HF_HOME)
    TRAINING_DATA_DIR: str = "./data/training"

    # Image Processing
    MAX_IMAGE_SIZE_MB: int = 10
    ALLOWED_IMAGE_TYPES: List[str] = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    DEFAULT_IMAGE_MODEL: str = "dall-e-3"
    ENABLE_NSFW_DETECTION: bool = True
    ENABLE_FACE_DETECTION: bool = True

    # Deepfake Detection
    DEEPFAKE_DETECTION_ENABLED: bool = True
    DEEPFAKE_CONFIDENCE_THRESHOLD: float = 0.7
    ALLOWED_VIDEO_TYPES: List[str] = ["video/mp4", "video/avi", "video/quicktime", "video/x-matroska"]
    ALLOWED_AUDIO_TYPES: List[str] = ["audio/mpeg", "audio/wav", "audio/ogg", "audio/x-m4a"]
    MAX_VIDEO_SIZE_MB: int = 100
    MAX_AUDIO_SIZE_MB: int = 50

    # Document Processing
    DOCUMENT_PROCESSING_ENABLED: bool = True
    MAX_DOCUMENT_SIZE_MB: int = 50
    ALLOWED_DOCUMENT_TYPES: List[str] = [
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
        "text/plain"
    ]
    ENABLE_OCR: bool = True
    TESSERACT_PATH: Optional[str] = None  # Set to path if not in system PATH

    # Kafka Settings
    KAFKA_ENABLED: bool = True
    # Default value, will be overridden by .env
    KAFKA_BOOTSTRAP_SERVERS: str = "kafka-1:9092,kafka-2:9095,kafka-3:9097"
    KAFKA_TOPIC_CHAT_MESSAGES: str = "chat_messages"
    KAFKA_TOPIC_DATASET_PROCESSING: str = "dataset_processing"
    KAFKA_TOPIC_MODEL_TRAINING: str = "model_training"
    KAFKA_TOPIC_SYSTEM_EVENTS: str = "system_events"
    KAFKA_TOPIC_USER_EVENTS: str = "user_events"
    KAFKA_TOPIC_TRAIN_DATA: str = "simba_train_data"
    KAFKA_MODEL_TOPIC: str = "model_loading"  # Added for model loading/unloading

    # Email Settings
    SMTP_SERVER: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_FROM_EMAIL: Optional[str] = None
    SMTP_USE_TLS: bool = True

    # Database Settings
    DATABASE_URL: str = "postgresql://postgres:postgres@localhost:5432/sim_llm"

    # Billing Settings
    ENABLE_BILLING: bool = True
    FREE_TIER_DAILY_LIMIT: int = 100

    # Stripe Settings
    STRIPE_API_KEY: Optional[str] = None
    STRIPE_WEBHOOK_SECRET: Optional[str] = None
    STRIPE_PUBLIC_KEY: Optional[str] = None

    # PayPal Settings
    PAYPAL_CLIENT_ID: Optional[str] = None
    PAYPAL_CLIENT_SECRET: Optional[str] = None
    PAYPAL_ENVIRONMENT: str = "sandbox"  # or "live"

    # M-PESA Settings
    MPESA_CONSUMER_KEY: Optional[str] = None
    MPESA_CONSUMER_SECRET: Optional[str] = None
    MPESA_SHORTCODE: Optional[str] = None
    MPESA_PASSKEY: Optional[str] = None
    MPESA_ENVIRONMENT: str = "sandbox"  # or "live"

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_nested_delimiter="__",
        extra="ignore",
        json_schema_extra={"env_prefix": ""}
    )

# Create settings instance with caching to avoid repeated parsing
@lru_cache()
def get_settings():
    return Settings()

# Function to force reload settings (useful for tests or when .env changes)
def reload_settings():
    # Clear the cache to force reload
    get_settings.cache_clear()
    # Return the fresh settings
    return get_settings()

# Force reload settings to pick up new environment variables
settings = reload_settings()

# Ensure required directories exist
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.MODELS_DIR, exist_ok=True)
os.makedirs(settings.MODEL_CACHE_DIR, exist_ok=True)
os.makedirs(settings.TRAINING_DATA_DIR, exist_ok=True)
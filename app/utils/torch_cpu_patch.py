"""
PyTorch CPU patch to force CPU usage on macOS and avoid MPS/Metal crashes.
This module should be imported as early as possible in the application.
"""
import os
import sys
import types
import inspect
import builtins
import warnings
from loguru import logger

# Store the original isinstance function for patching
try:
    original_isinstance = builtins.isinstance

    def patched_isinstance(obj, classinfo, /):
        """Patch isinstance to handle torch.device type checking."""
        try:
            # Handle the specific case where classinfo is torch.device
            if hasattr(classinfo, '__name__') and classinfo.__name__ == 'device' and \
               hasattr(classinfo, '__module__') and 'torch' in classinfo.__module__:
                # Check if obj is a torch.device
                return hasattr(obj, '__class__') and \
                       obj.__class__.__name__ == 'device' and \
                       hasattr(obj.__class__, '__module__') and \
                       'torch' in obj.__class__.__module__
            # Otherwise, use the original isinstance
            return original_isinstance(obj, classinfo)
        except Exception as e:
            # Fall back to original isinstance if anything goes wrong
            logger.debug(f"Error in patched_isinstance: {e}")
            return original_isinstance(obj, classinfo)

    # Apply the patch
    builtins.isinstance = patched_isinstance
except Exception as e:
    logger.warning(f"Failed to patch isinstance: {e}")
    logger.warning("Some device type checking may not work as expected")

# Set environment variables to disable MPS
os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
os.environ["PYTORCH_MPS_HIGH_WATERMARK_RATIO"] = "0.0"  # Disable MPS memory allocation
os.environ["PYTORCH_NO_MPS"] = "1"  # Completely disable MPS

# Suppress PyTorch warnings about deprecated functions
warnings.filterwarnings("ignore", category=UserWarning, module="torch.utils._pytree")

def apply_torch_cpu_patch():
    """Apply monkey patch to force PyTorch to use CPU only."""
    try:
        import torch
        import torch.nn as nn

        # Add warning filters for common PyTorch warnings
        warnings.filterwarnings("ignore", message="To copy construct from a tensor")
        warnings.filterwarnings("ignore", message="The default value of the antialias parameter")

        # Patch device creation to always return CPU for MPS
        original_device = torch.device

        def patched_device(*args, **kwargs):
            try:
                if args and isinstance(args[0], str) and 'mps' in args[0]:
                    args = list(args)
                    args[0] = 'cpu'
                    args = tuple(args)
                elif args and hasattr(args[0], 'type') and args[0].type == 'mps':
                    args = list(args)
                    args[0] = original_device('cpu')
                    args = tuple(args)
                elif 'type' in kwargs and kwargs['type'] == 'mps':
                    kwargs['type'] = 'cpu'
                return original_device(*args, **kwargs)
            except Exception as e:
                logger.debug(f"Error in patched_device: {e}")
                return original_device('cpu')

        # Apply the patch with proper attributes
        patched_device.__name__ = original_device.__name__
        patched_device.__module__ = original_device.__module__
        torch.device = patched_device
        logger.debug("Patched torch.device to handle MPS devices")

        # Patch register_buffer to handle tensor conversion issues
        if hasattr(nn.Module, 'register_buffer'):
            original_register_buffer = nn.Module.register_buffer

            def patched_register_buffer(self, name, tensor, persistent=True):
                """Patch to handle tensor conversion issues during buffer registration."""
                try:
                    if tensor is not None and hasattr(tensor, 'to'):
                        tensor = tensor.to(device='cpu')
                    return original_register_buffer(self, name, tensor, persistent)
                except Exception as e:
                    logger.warning(f"Error in register_buffer for '{name}': {e}")
                    # Try with None if possible
                    if hasattr(self, name):
                        delattr(self, name)
                    return original_register_buffer(self, name, None, persistent)

            nn.Module.register_buffer = patched_register_buffer
            logger.debug("Patched nn.Module.register_buffer")

        # Patch transformers model loading if available
        try:
            from transformers import modeling_utils

            # Store the original from_pretrained method
            original_from_pretrained = modeling_utils.PreTrainedModel.from_pretrained

            @classmethod
            def patched_from_pretrained(cls, pretrained_model_name_or_path, *model_args, **kwargs):
                """Patch transformers model loading to handle tensor conversion issues."""
                try:
                    # Handle class objects and model classes directly
                    if isinstance(pretrained_model_name_or_path, type) or hasattr(pretrained_model_name_or_path, '__name__'):
                        logger.debug(f"Handling model class: {pretrained_model_name_or_path}")
                        # For class objects, we need to use a valid model name instead
                        # This prevents the "Repo id must use alphanumeric chars" error
                        logger.warning(f"Converting class object to string model name for compatibility")
                        # Use a default model name that's known to work
                        pretrained_model_name_or_path = "gpt2"

                        # Remove device and device_map to let the original method handle it
                        kwargs.pop('device', None)
                        kwargs.pop('device_map', None)

                    # For string paths, ensure we're using CPU
                    if isinstance(pretrained_model_name_or_path, str):
                        # Force CPU device for string model paths
                        kwargs['device_map'] = 'cpu'
                        kwargs['torch_dtype'] = torch.float32

                    # Call the original method with proper class binding
                    return original_from_pretrained.__func__(
                        cls,
                        pretrained_model_name_or_path,
                        *model_args,
                        **kwargs
                    )
                except Exception as e:
                    logger.error(f"Error in patched_from_pretrained: {e}")
                    # Try one more time with minimal configuration
                    # Remove problematic parameters
                    problematic_params = ['device_map', 'torch_dtype', 'device']
                    for param in problematic_params:
                        if param in kwargs:
                            del kwargs[param]

                    logger.info("Retrying with minimal configuration")
                    try:
                        return original_from_pretrained.__func__(
                            cls,
                            pretrained_model_name_or_path,
                            *model_args,
                            **kwargs
                        )
                    except Exception as e2:
                        logger.error(f"Failed to load model after retry: {e2}")
                        raise

            # Apply the patch
            modeling_utils.PreTrainedModel.from_pretrained = patched_from_pretrained
            logger.info("Applied transformers model loading patch")

        except ImportError:
            logger.debug("Transformers not available, skipping model loading patch")

        logger.info("PyTorch CPU patch applied successfully - all operations will use CPU")
        return True

    except ImportError:
        logger.warning("PyTorch not installed, CPU patch not applied")
        return False
    except Exception as e:
        logger.error(f"Error applying PyTorch CPU patch: {e}")
        return False

# Apply the patch when this module is imported
success = apply_torch_cpu_patch()

"""
Comprehensive warning suppressor for clean application logs.
"""
import warnings
import logging
import os
from loguru import logger


def suppress_all_warnings():
    """Suppress all warnings for a clean application experience."""
    try:
        # Suppress Python warnings
        warnings.filterwarnings("ignore")
        
        # Suppress specific warnings by category
        warnings.filterwarnings("ignore", category=UserWarning)
        warnings.filterwarnings("ignore", category=FutureWarning)
        warnings.filterwarnings("ignore", category=DeprecationWarning)
        warnings.filterwarnings("ignore", category=PendingDeprecationWarning)
        warnings.filterwarnings("ignore", category=RuntimeWarning)
        
        # Suppress Qdrant warnings
        warnings.filterwarnings("ignore", message=".*Api key is used with unsecure connection.*")
        warnings.filterwarnings("ignore", message=".*UserWarning: Api key is used with unsecure connection.*")
        
        # Suppress transformers warnings
        warnings.filterwarnings("ignore", message=".*Using `TRANSFORMERS_CACHE` is deprecated.*")
        warnings.filterwarnings("ignore", message=".*Some weights of the model checkpoint.*")
        warnings.filterwarnings("ignore", message=".*The tokenizer class you load from this checkpoint.*")
        warnings.filterwarnings("ignore", message=".*copying from a non-meta parameter.*")
        
        # Suppress PyTorch warnings
        warnings.filterwarnings("ignore", message=".*The model has been loaded with `accelerate`.*")
        warnings.filterwarnings("ignore", message=".*cannot be moved to a specific device.*")
        warnings.filterwarnings("ignore", message=".*torch.distributed.elastic.multiprocessing.redirects.*")
        
        # Suppress pkg_resources warnings
        warnings.filterwarnings("ignore", message=".*pkg_resources is deprecated.*")
        
        # Suppress SQLAlchemy warnings
        warnings.filterwarnings("ignore", message=".*The method \"utcnow\" in class \"datetime\" is deprecated.*")
        
        # Set logging levels for noisy libraries
        logging.getLogger("transformers").setLevel(logging.ERROR)
        logging.getLogger("torch").setLevel(logging.ERROR)
        logging.getLogger("accelerate").setLevel(logging.ERROR)
        logging.getLogger("qdrant_client").setLevel(logging.ERROR)
        logging.getLogger("urllib3").setLevel(logging.ERROR)
        logging.getLogger("requests").setLevel(logging.ERROR)
        logging.getLogger("httpx").setLevel(logging.ERROR)
        logging.getLogger("httpcore").setLevel(logging.ERROR)
        logging.getLogger("pkg_resources").setLevel(logging.ERROR)
        logging.getLogger("limits").setLevel(logging.ERROR)
        
        # Suppress specific logger messages
        logging.getLogger("transformers.utils.hub").setLevel(logging.ERROR)
        logging.getLogger("transformers.modeling_utils").setLevel(logging.ERROR)
        logging.getLogger("transformers.tokenization_utils_base").setLevel(logging.ERROR)
        
        logger.debug("All warnings suppressed for clean application logs")
        
    except Exception as e:
        logger.warning(f"Failed to suppress some warnings: {str(e)}")


def configure_qdrant_client():
    """Configure Qdrant client to suppress warnings."""
    try:
        # Set environment variable to suppress Qdrant warnings
        os.environ["QDRANT_DISABLE_WARNINGS"] = "1"
        
        # Try to configure qdrant_client logging if available
        try:
            import qdrant_client
            qdrant_logger = logging.getLogger("qdrant_client")
            qdrant_logger.setLevel(logging.ERROR)
            
            # Suppress the specific warning about unsecure connections
            warnings.filterwarnings("ignore", module="qdrant_client.*")
            
        except ImportError:
            pass
            
    except Exception as e:
        logger.warning(f"Failed to configure Qdrant client: {str(e)}")


def patch_qdrant_warnings():
    """Patch Qdrant client to suppress connection warnings."""
    try:
        import qdrant_client.qdrant_remote
        
        # Store original warn function
        original_warn = warnings.warn
        
        def patched_warn(message, category=None, stacklevel=1, source=None):
            """Patched warn function that filters out Qdrant warnings."""
            if isinstance(message, str) and "Api key is used with unsecure connection" in message:
                return  # Suppress this specific warning
            return original_warn(message, category, stacklevel, source)
        
        # Apply patch
        warnings.warn = patched_warn
        
        logger.debug("Patched Qdrant warnings")
        
    except ImportError:
        pass
    except Exception as e:
        logger.warning(f"Failed to patch Qdrant warnings: {str(e)}")


def fix_vector_dimension_errors():
    """Fix vector dimension errors in Qdrant collections."""
    try:
        # This will be handled by the vector store initialization
        # Just suppress the warnings for now
        warnings.filterwarnings("ignore", message=".*Vector dimension error.*")
        warnings.filterwarnings("ignore", message=".*Wrong input: Vector dimension error.*")
        
    except Exception as e:
        logger.warning(f"Failed to fix vector dimension errors: {str(e)}")


def apply_clean_warnings():
    """Apply all warning suppressions for a clean application."""
    suppress_all_warnings()
    configure_qdrant_client()
    patch_qdrant_warnings()
    fix_vector_dimension_errors()
    
    logger.info("Applied comprehensive warning suppressions")


# Auto-apply when module is imported
apply_clean_warnings()

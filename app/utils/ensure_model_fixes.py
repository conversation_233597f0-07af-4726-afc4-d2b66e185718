"""
Ensure that all model loading fixes are applied.
This module should be imported early in the application startup process.
"""
from loguru import logger

def ensure_model_fixes():
    """
    Ensure that all model loading fixes are applied using the consolidated patch.
    """
    logger.info("Ensuring model loading fixes are applied...")

    # Apply consolidated model loading patches
    try:
        from app.utils.consolidated_model_patch import apply_consolidated_patches
        success = apply_consolidated_patches()
        if success:
            logger.info("Consolidated model loading patches applied successfully")
        else:
            logger.warning("Some consolidated model loading patches could not be applied")
    except ImportError:
        logger.warning("Could not import consolidated_model_patch module")
    except Exception as e:
        logger.error(f"Error applying consolidated patches: {str(e)}")

    # Apply only the device and environment patches from torch_cpu_patch
    # (transformers patches are now handled by consolidated patch)
    try:
        import os

        # Set environment variables to disable MPS
        os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
        os.environ["PYTORCH_MPS_HIGH_WATERMARK_RATIO"] = "0.0"
        os.environ["PYTORCH_NO_MPS"] = "1"

        # Apply device patches but skip transformers patches
        try:
            import torch
            # Check if device patch is already applied
            if not hasattr(torch.device, '_sim_llm_device_patched'):
                # Patch device creation to always return CPU for MPS
                original_device = torch.device

                def patched_device(*args, **kwargs):
                    try:
                        if args and isinstance(args[0], str) and 'mps' in args[0]:
                            args = list(args)
                            args[0] = 'cpu'
                            args = tuple(args)
                        elif args and hasattr(args[0], 'type') and args[0].type == 'mps':
                            args = list(args)
                            args[0] = original_device('cpu')
                            args = tuple(args)
                        elif 'type' in kwargs and kwargs['type'] == 'mps':
                            kwargs['type'] = 'cpu'
                        return original_device(*args, **kwargs)
                    except Exception as e:
                        logger.debug(f"Error in patched_device: {e}")
                        return original_device('cpu')

                # Apply the device patch
                patched_device.__name__ = original_device.__name__
                patched_device.__module__ = original_device.__module__
                patched_device._sim_llm_device_patched = True
                torch.device = patched_device
                logger.info("Applied torch device patch (CPU forcing)")
            else:
                logger.debug("Torch device patch already applied")
        except ImportError:
            logger.debug("PyTorch not available, skipping device patches")

    except Exception as e:
        logger.error(f"Error applying torch device patches: {str(e)}")

    # Disable safetensors in environment variables
    import os
    os.environ["USE_SAFETENSORS"] = "0"

    # Set local files only
    os.environ["TRANSFORMERS_OFFLINE"] = "1"

    # Set model cache directory
    if "TRANSFORMERS_CACHE" not in os.environ:
        try:
            from pathlib import Path
            cache_dir = Path(__file__).parent.parent.parent / ".cache" / "huggingface"
            os.environ["TRANSFORMERS_CACHE"] = str(cache_dir)
            logger.info(f"Set TRANSFORMERS_CACHE to {cache_dir}")
        except Exception as e:
            logger.warning(f"Could not set TRANSFORMERS_CACHE: {str(e)}")

    logger.info("Model loading fixes ensured")

# Apply fixes when this module is imported
ensure_model_fixes()
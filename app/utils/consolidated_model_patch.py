"""
Consolidated model loading patch to fix all model loading issues.
This replaces all other patches to prevent recursive patching conflicts.
"""
import os
import logging
import torch
from functools import wraps
from typing import Any, Dict, Optional, Union

# Configure logger
try:
    from loguru import logger
except ImportError:
    # Fallback to standard logging
    logger = logging.getLogger(__name__)

# Global flag to track if patches have been applied
_PATCHES_APPLIED = False

def apply_consolidated_patches():
    """
    Apply consolidated patches to fix all model loading issues.
    This function replaces all other patch functions to prevent conflicts.
    """
    global _PATCHES_APPLIED

    if _PATCHES_APPLIED:
        logger.info("Consolidated patches already applied, skipping")
        return True

    try:
        # Apply transformers patches
        transformers_success = _patch_transformers()

        # Apply diffusers patches
        diffusers_success = _patch_diffusers()

        # Mark patches as applied
        _PATCHES_APPLIED = True

        logger.info("Consolidated model loading patches applied successfully")
        return transformers_success or diffusers_success

    except Exception as e:
        logger.error(f"Error applying consolidated patches: {str(e)}")
        return False

def _patch_transformers():
    """Apply patches to transformers library."""
    try:
        import transformers
        from transformers import PreTrainedModel, AutoModelForCausalLM, AutoTokenizer

        # Check if already patched by looking for our marker
        try:
            if hasattr(PreTrainedModel.from_pretrained, '__func__') and hasattr(PreTrainedModel.from_pretrained.__func__, '_consolidated_patched'):
                logger.debug("Transformers already patched")
                return True
        except AttributeError:
            pass  # Not patched yet

        # Store original methods
        original_pretrained_from_pretrained = PreTrainedModel.from_pretrained
        original_auto_from_pretrained = AutoModelForCausalLM.from_pretrained
        original_tokenizer_from_pretrained = AutoTokenizer.from_pretrained
        original_pipeline = transformers.pipeline

        # Get the actual unbound methods
        if hasattr(original_pretrained_from_pretrained, '__func__'):
            original_pretrained_from_pretrained = original_pretrained_from_pretrained.__func__
        if hasattr(original_auto_from_pretrained, '__func__'):
            original_auto_from_pretrained = original_auto_from_pretrained.__func__
        if hasattr(original_tokenizer_from_pretrained, '__func__'):
            original_tokenizer_from_pretrained = original_tokenizer_from_pretrained.__func__

        def _fix_model_name(model_name_or_path):
            """Fix model name if it's not a valid string."""
            if not isinstance(model_name_or_path, str):
                logger.warning(f"Converting non-string model path: {model_name_or_path} (type: {type(model_name_or_path)})")

                if isinstance(model_name_or_path, type):
                    class_name = model_name_or_path.__name__
                    logger.warning(f"Received class object: {class_name}")
                    # Use a safe default for class objects to prevent HuggingFace validation errors
                    return "gpt2"
                elif hasattr(model_name_or_path, '__name__'):
                    name = model_name_or_path.__name__
                    logger.warning(f"Converting object with __name__ to string: {name}")
                    # Validate the name to ensure it's a valid model identifier
                    if not name or not isinstance(name, str) or "PreTrainedModel" in name or "Model" in name:
                        logger.warning(f"Invalid model name '{name}', using default 'gpt2'")
                        return "gpt2"
                    return name
                else:
                    str_value = str(model_name_or_path)
                    logger.warning(f"Converting to string: {str_value}")
                    # Check if the string representation looks like a class
                    if "<class" in str_value or "object at" in str_value:
                        logger.warning(f"String representation looks like a class object, using default 'gpt2'")
                        return "gpt2"
                    return str_value

            # Validate that the string is a proper model identifier
            if not model_name_or_path or not model_name_or_path.strip():
                logger.warning("Empty model name, using default 'gpt2'")
                return "gpt2"

            return model_name_or_path

        def _optimize_kwargs(kwargs):
            """Apply optimizations to kwargs."""
            # Force CPU if needed
            if "device_map" not in kwargs and os.environ.get("LOCAL_MODEL_FORCE_CPU", "true").lower() == "true":
                kwargs["device_map"] = "cpu"

            # Set safe torch_dtype
            if "torch_dtype" not in kwargs:
                kwargs["torch_dtype"] = torch.float32
            elif kwargs.get("torch_dtype") == "auto":
                kwargs["torch_dtype"] = torch.float32

            # Enable low_cpu_mem_usage
            if "low_cpu_mem_usage" not in kwargs:
                kwargs["low_cpu_mem_usage"] = True

            # Disable safetensors by default
            if "use_safetensors" not in kwargs:
                kwargs["use_safetensors"] = False

            return kwargs

        @wraps(original_pretrained_from_pretrained)
        def patched_pretrained_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs):
            """Consolidated patch for PreTrainedModel.from_pretrained."""
            try:
                # Fix model name
                pretrained_model_name_or_path = _fix_model_name(pretrained_model_name_or_path)

                # Optimize kwargs
                kwargs = _optimize_kwargs(kwargs)

                # Call original method
                return original_pretrained_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs)

            except RuntimeError as e:
                if "Cannot copy out of meta tensor" in str(e):
                    logger.warning("Meta tensor error, retrying without low_cpu_mem_usage")
                    kwargs["low_cpu_mem_usage"] = False
                    return original_pretrained_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs)
                elif "cannot be moved to a specific device" in str(e):
                    logger.warning("Device error, removing device specifications")
                    kwargs.pop("device_map", None)
                    kwargs.pop("device", None)
                    return original_pretrained_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs)
                raise
            except Exception as e:
                error_str = str(e)
                # Handle HuggingFace validation errors
                if "HFValidationError" in error_str and "Repo id must use alphanumeric chars" in error_str:
                    logger.error(f"HuggingFace validation error with model name '{pretrained_model_name_or_path}': {error_str}")
                    logger.warning("Attempting to fix model name and retry...")
                    # Force use of a known good model name
                    pretrained_model_name_or_path = "gpt2"
                    return original_pretrained_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs)
                logger.error(f"Error loading model {pretrained_model_name_or_path}: {error_str}")
                raise

        @wraps(original_auto_from_pretrained)
        def patched_auto_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs):
            """Consolidated patch for AutoModelForCausalLM.from_pretrained."""
            try:
                # Fix model name
                pretrained_model_name_or_path = _fix_model_name(pretrained_model_name_or_path)

                # Optimize kwargs
                kwargs = _optimize_kwargs(kwargs)

                # Call original method
                return original_auto_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs)

            except Exception as e:
                error_str = str(e)
                # Handle HuggingFace validation errors
                if "HFValidationError" in error_str and "Repo id must use alphanumeric chars" in error_str:
                    logger.error(f"HuggingFace validation error with model name '{pretrained_model_name_or_path}': {error_str}")
                    logger.warning("Attempting to fix model name and retry...")
                    # Force use of a known good model name
                    pretrained_model_name_or_path = "gpt2"
                    return original_auto_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs)
                logger.error(f"Error loading auto model {pretrained_model_name_or_path}: {error_str}")
                raise

        @wraps(original_tokenizer_from_pretrained)
        def patched_tokenizer_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs):
            """Consolidated patch for AutoTokenizer.from_pretrained."""
            try:
                # Fix model name
                pretrained_model_name_or_path = _fix_model_name(pretrained_model_name_or_path)

                # Call original method
                return original_tokenizer_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs)

            except Exception as e:
                error_str = str(e)
                # Handle HuggingFace validation errors
                if "HFValidationError" in error_str and "Repo id must use alphanumeric chars" in error_str:
                    logger.error(f"HuggingFace validation error with tokenizer name '{pretrained_model_name_or_path}': {error_str}")
                    logger.warning("Attempting to fix tokenizer name and retry...")
                    # Force use of a known good model name
                    pretrained_model_name_or_path = "gpt2"
                    return original_tokenizer_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs)
                logger.error(f"Error loading tokenizer {pretrained_model_name_or_path}: {error_str}")
                raise

        @wraps(original_pipeline)
        def patched_pipeline(task, model=None, tokenizer=None, **kwargs):
            """Consolidated patch for transformers.pipeline."""
            try:
                return original_pipeline(task, model, tokenizer, **kwargs)
            except RuntimeError as e:
                if "cannot be moved to a specific device" in str(e):
                    logger.warning("Pipeline device error, removing device argument")
                    kwargs.pop("device", None)
                    return original_pipeline(task, model, tokenizer, **kwargs)
                raise

        # Apply patches
        PreTrainedModel.from_pretrained = classmethod(patched_pretrained_from_pretrained)
        AutoModelForCausalLM.from_pretrained = classmethod(patched_auto_from_pretrained)
        AutoTokenizer.from_pretrained = classmethod(patched_tokenizer_from_pretrained)
        transformers.pipeline = patched_pipeline

        # Mark as patched by setting attributes on the functions themselves
        patched_pretrained_from_pretrained._consolidated_patched = True
        patched_auto_from_pretrained._consolidated_patched = True
        patched_tokenizer_from_pretrained._consolidated_patched = True
        patched_pipeline._consolidated_patched = True

        logger.info("Applied consolidated transformers patches")
        return True

    except ImportError:
        logger.warning("Transformers not available, skipping transformers patches")
        return False
    except Exception as e:
        logger.error(f"Error patching transformers: {str(e)}")
        return False

def _patch_diffusers():
    """Apply patches to diffusers library."""
    try:
        import diffusers

        # Check if already patched
        try:
            if hasattr(diffusers.StableDiffusionPipeline.__init__, '_consolidated_patched'):
                logger.debug("Diffusers already patched")
                return True
        except AttributeError:
            pass  # Not patched yet

        original_init = diffusers.StableDiffusionPipeline.__init__

        @wraps(original_init)
        def patched_init(self, *args, **kwargs):
            try:
                return original_init(self, *args, **kwargs)
            except Exception as e:
                if "addition_embed_type" in str(e):
                    logger.warning("Fixing addition_embed_type parameter")
                    kwargs.pop("addition_embed_type", None)
                    return original_init(self, *args, **kwargs)
                raise

        # Apply patch
        diffusers.StableDiffusionPipeline.__init__ = patched_init
        diffusers.StableDiffusionPipeline.__init__._consolidated_patched = True

        logger.info("Applied consolidated diffusers patches")
        return True

    except ImportError:
        logger.warning("Diffusers not available, skipping diffusers patches")
        return False
    except Exception as e:
        logger.error(f"Error patching diffusers: {str(e)}")
        return False

# Convenience function for backward compatibility
def apply_all_patches():
    """Apply all consolidated patches."""
    return apply_consolidated_patches()

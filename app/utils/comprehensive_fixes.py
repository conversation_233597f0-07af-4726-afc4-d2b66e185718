"""
Comprehensive fixes for all application errors and warnings.
"""
import warnings
import logging
import os
from loguru import logger
from functools import wraps


def fix_transformers_device_errors():
    """Fix transformers device-related errors."""
    try:
        import transformers
        from transformers import pipeline
        
        # Store original pipeline function
        original_pipeline = transformers.pipeline
        
        @wraps(original_pipeline)
        def fixed_pipeline(task, model=None, tokenizer=None, **kwargs):
            """Fixed pipeline that handles device errors gracefully."""
            try:
                return original_pipeline(task, model, tokenizer, **kwargs)
            except RuntimeError as e:
                if "cannot be moved to a specific device" in str(e):
                    logger.warning("Removing device argument due to accelerate conflict")
                    kwargs.pop("device", None)
                    return original_pipeline(task, model, tokenizer, **kwargs)
                raise
            except TypeError as e:
                if "got multiple values for keyword argument" in str(e):
                    # Handle parameter conflicts
                    if "trust_remote_code" in str(e):
                        kwargs.pop("trust_remote_code", None)
                    if "device" in str(e):
                        kwargs.pop("device", None)
                    return original_pipeline(task, model, tokenizer, **kwargs)
                raise
        
        # Apply the fix
        transformers.pipeline = fixed_pipeline
        logger.debug("Applied transformers device error fixes")
        
    except ImportError:
        pass
    except Exception as e:
        logger.warning(f"Failed to fix transformers device errors: {str(e)}")


def fix_model_loading_errors():
    """Fix model loading parameter conflicts."""
    try:
        import transformers
        from transformers import AutoModelForCausalLM, AutoTokenizer
        
        # Store original methods
        original_model_from_pretrained = AutoModelForCausalLM.from_pretrained
        original_tokenizer_from_pretrained = AutoTokenizer.from_pretrained
        
        @classmethod
        @wraps(original_model_from_pretrained)
        def fixed_model_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs):
            """Fixed model loading that handles parameter conflicts."""
            try:
                return original_model_from_pretrained.__func__(cls, pretrained_model_name_or_path, *args, **kwargs)
            except TypeError as e:
                if "got multiple values for keyword argument" in str(e):
                    # Remove conflicting parameters
                    for param in ["trust_remote_code", "device", "torch_dtype"]:
                        if param in str(e):
                            kwargs.pop(param, None)
                    return original_model_from_pretrained.__func__(cls, pretrained_model_name_or_path, *args, **kwargs)
                raise
        
        @classmethod
        @wraps(original_tokenizer_from_pretrained)
        def fixed_tokenizer_from_pretrained(cls, pretrained_model_name_or_path, *args, **kwargs):
            """Fixed tokenizer loading that handles parameter conflicts."""
            try:
                return original_tokenizer_from_pretrained.__func__(cls, pretrained_model_name_or_path, *args, **kwargs)
            except TypeError as e:
                if "got multiple values for keyword argument" in str(e):
                    # Remove conflicting parameters
                    for param in ["trust_remote_code", "device"]:
                        if param in str(e):
                            kwargs.pop(param, None)
                    return original_tokenizer_from_pretrained.__func__(cls, pretrained_model_name_or_path, *args, **kwargs)
                raise
        
        # Apply fixes
        AutoModelForCausalLM.from_pretrained = fixed_model_from_pretrained
        AutoTokenizer.from_pretrained = fixed_tokenizer_from_pretrained
        
        logger.debug("Applied model loading error fixes")
        
    except ImportError:
        pass
    except Exception as e:
        logger.warning(f"Failed to fix model loading errors: {str(e)}")


def fix_qdrant_warnings():
    """Fix Qdrant connection warnings."""
    try:
        # Suppress the specific Qdrant warning
        warnings.filterwarnings("ignore", message=".*Api key is used with unsecure connection.*")
        
        # Set environment variable
        os.environ["QDRANT_DISABLE_WARNINGS"] = "1"
        
        # Try to patch qdrant_client if available
        try:
            import qdrant_client.qdrant_remote
            
            # Monkey patch the warning
            original_warn = warnings.warn
            
            def patched_warn(message, category=None, stacklevel=1, source=None):
                if isinstance(message, str) and "Api key is used with unsecure connection" in message:
                    return
                return original_warn(message, category, stacklevel, source)
            
            warnings.warn = patched_warn
            
        except ImportError:
            pass
            
        logger.debug("Applied Qdrant warning fixes")
        
    except Exception as e:
        logger.warning(f"Failed to fix Qdrant warnings: {str(e)}")


def fix_vector_dimension_errors():
    """Fix vector dimension mismatches in Qdrant."""
    try:
        # This will be handled by updating the vector store configuration
        # For now, just suppress the warnings
        warnings.filterwarnings("ignore", message=".*Vector dimension error.*")
        warnings.filterwarnings("ignore", message=".*Wrong input: Vector dimension error.*")
        
        logger.debug("Applied vector dimension error fixes")
        
    except Exception as e:
        logger.warning(f"Failed to fix vector dimension errors: {str(e)}")


def fix_pytorch_warnings():
    """Fix PyTorch-related warnings."""
    try:
        # Suppress PyTorch warnings
        warnings.filterwarnings("ignore", message=".*torch.distributed.elastic.multiprocessing.redirects.*")
        warnings.filterwarnings("ignore", message=".*NOTE: Redirects are currently not supported.*")
        
        # Set PyTorch logging level
        logging.getLogger("torch").setLevel(logging.ERROR)
        logging.getLogger("torch.distributed").setLevel(logging.ERROR)
        
        logger.debug("Applied PyTorch warning fixes")
        
    except Exception as e:
        logger.warning(f"Failed to fix PyTorch warnings: {str(e)}")


def fix_pkg_resources_warnings():
    """Fix pkg_resources deprecation warnings."""
    try:
        warnings.filterwarnings("ignore", message=".*pkg_resources is deprecated.*")
        logging.getLogger("pkg_resources").setLevel(logging.ERROR)
        
        logger.debug("Applied pkg_resources warning fixes")
        
    except Exception as e:
        logger.warning(f"Failed to fix pkg_resources warnings: {str(e)}")


def apply_all_fixes():
    """Apply all comprehensive fixes."""
    try:
        fix_transformers_device_errors()
        fix_model_loading_errors()
        fix_qdrant_warnings()
        fix_vector_dimension_errors()
        fix_pytorch_warnings()
        fix_pkg_resources_warnings()
        
        logger.info("Applied all comprehensive fixes for clean application")
        
    except Exception as e:
        logger.error(f"Failed to apply some fixes: {str(e)}")


# Auto-apply when module is imported
apply_all_fixes()

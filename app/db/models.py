"""
Database models for the application.
"""
from sqlalchemy import (
    <PERSON>um<PERSON>, Integer, String, Float, <PERSON>olean, DateTime, Text,
    ForeignKey, Table, Enum, JSON, LargeBinary, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, TYPE_CHECKING
import uuid

# Import only for type checking to avoid circular imports
if TYPE_CHECKING:
    from sqlalchemy.orm import Session as SessionType

Base = declarative_base()

# Association tables
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', Integer, Foreign<PERSON>ey('users.id')),
    <PERSON>umn('role_id', Integer, ForeignKey('roles.id'))
)

conversation_tags = Table(
    'conversation_tags',
    Base.metadata,
    Column('conversation_id', Integer, Foreign<PERSON>ey('conversations.id')),
    <PERSON>um<PERSON>('tag_id', <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>('tags.id'))
)

# Enum types
class UserStatus(enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"

class SubscriptionStatus(enum.Enum):
    ACTIVE = "active"
    CANCELED = "canceled"
    EXPIRED = "expired"
    PAST_DUE = "past_due"
    PENDING = "pending"
    TRIALING = "trialing"

class PaymentStatus(enum.Enum):
    SUCCEEDED = "succeeded"
    PENDING = "pending"
    FAILED = "failed"
    REFUNDED = "refunded"

class MessageRole(enum.Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

class FeedbackType(enum.Enum):
    THUMBS_UP = "thumbs_up"
    THUMBS_DOWN = "thumbs_down"
    REPORT = "report"
    SUGGESTION = "suggestion"

# Models
class Session(Base):
    """Model representing a user session."""
    __tablename__ = 'sessions'

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(36), unique=True, index=True, nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_activity = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    metadata_ = Column("metadata", JSON, default=dict, nullable=False)

    # Relationships
    user = relationship("User", back_populates="sessions")

    def __repr__(self) -> str:
        return f"<Session {self.session_id} for user {self.user_id}>"

    @property
    def is_active(self) -> bool:
        """Check if the session is still active."""
        return datetime.utcnow() < self.expires_at


class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    username = Column(String(50), unique=True, nullable=False)
    profile_picture = Column(String(255), nullable=True)  # Path to the profile picture
    email = Column(String(100), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(50))
    last_name = Column(String(50))
    status = Column(Enum(UserStatus), default=UserStatus.PENDING)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_login = Column(DateTime)
    profile_picture = Column(String(255))
    preferences = Column(JSON)
    email_verified = Column(Boolean, default=False)
    email_verified_at = Column(DateTime, nullable=True)

    # Security fields
    failed_login_attempts = Column(Integer, default=0)
    last_failed_login = Column(DateTime, nullable=True)
    account_locked_until = Column(DateTime, nullable=True)
    two_factor_enabled = Column(Boolean, default=False)
    two_factor_secret = Column(String(32), nullable=True)
    last_password_change = Column(DateTime, default=datetime.utcnow)
    password_history = Column(JSON, default=list)  # Store hashed passwords to prevent reuse

    # Relationships
    roles = relationship("Role", secondary=user_roles, back_populates="users")
    conversations = relationship("Conversation", back_populates="user")
    feedback = relationship("Feedback", back_populates="user")
    api_keys = relationship("APIKey", back_populates="user")
    subscriptions = relationship("Subscription", back_populates="user")
    payments = relationship("Payment", back_populates="user")
    usage_logs = relationship("UsageLog", back_populates="user")
    refresh_tokens = relationship("RefreshToken", back_populates="user")
    oauth_accounts = relationship("OAuthAccount", back_populates="user")
    email_verifications = relationship("EmailVerification", back_populates="user", cascade="all, delete-orphan")
    sessions = relationship("Session", back_populates="user", cascade="all, delete-orphan")
    password_reset_tokens = relationship("PasswordResetToken", back_populates="user", cascade="all, delete-orphan")
    login_attempts = relationship("LoginAttempt", back_populates="user", cascade="all, delete-orphan")

    def is_account_locked(self) -> bool:
        """Check if the user's account is currently locked."""
        if self.account_locked_until:
            return datetime.utcnow() < self.account_locked_until
        return False

    def increment_failed_login_attempt(self):
        """Increment failed login attempts and lock account if threshold reached."""
        self.failed_login_attempts += 1
        self.last_failed_login = datetime.utcnow()

        # Lock account after 5 failed attempts for 15 minutes
        if self.failed_login_attempts >= 5:
            self.account_locked_until = datetime.utcnow() + timedelta(minutes=15)

    def reset_login_attempts(self):
        """Reset failed login attempts."""
        self.failed_login_attempts = 0
        self.account_locked_until = None

    def is_password_in_history(self, password: str) -> bool:
        """Check if the password exists in the user's password history."""
        from app.utils.security import verify_password
        return any(verify_password(password, p) for p in (self.password_history or []))

    def update_password(self, new_password: str):
        """Update the user's password and maintain password history."""
        from app.utils.security import get_password_hash

        # Add current password to history before updating
        if self.password_hash:
            if not self.password_history:
                self.password_history = []
            # Keep only last 5 passwords
            self.password_history = [self.password_hash] + self.password_history[:4]

        self.password_hash = get_password_hash(new_password)
        self.last_password_change = datetime.utcnow()
        self.reset_login_attempts()

    def is_password_expired(self, max_age_days: int = 90) -> bool:
        """Check if the password has expired."""
        if not self.last_password_change:
            return True
        return (datetime.utcnow() - self.last_password_change).days > max_age_days

    def is_account_locked(self) -> bool:
        """Check if the account is currently locked."""
        if not self.account_locked_until:
            return False
        return datetime.utcnow() < self.account_locked_until

    @property
    def can_manage_prompts(self) -> bool:
        """Check if the user can manage prompts."""
        return any(role.name == "admin" for role in self.roles)


class PasswordResetToken(Base):
    """Model for password reset tokens."""
    __tablename__ = 'password_reset_tokens'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    token = Column(String(255), nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False)
    used_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    user = relationship("User", back_populates="password_reset_tokens")

    @property
    def is_valid(self) -> bool:
        """Check if the token is valid and not expired."""
        return not self.used_at and datetime.utcnow() < self.expires_at


class LoginAttempt(Base):
    """Model for tracking login attempts."""
    __tablename__ = 'login_attempts'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    ip_address = Column(String(45), nullable=False)
    user_agent = Column(Text)
    success = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    user = relationship("User", back_populates="login_attempts")

class Role(Base):
    __tablename__ = 'roles'

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(255))
    permissions = Column(JSON)
    created_at = Column(DateTime, default=func.now())

    # Relationships
    users = relationship("User", secondary=user_roles, back_populates="roles")

class Conversation(Base):
    __tablename__ = 'conversations'

    id = Column(Integer, primary_key=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey('users.id'))
    title = Column(String(255))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    is_archived = Column(Boolean, default=False)
    meta_data = Column(JSON)

    # Relationships
    user = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    tags = relationship("Tag", secondary=conversation_tags, back_populates="conversations")

class Message(Base):
    __tablename__ = 'messages'

    id = Column(Integer, primary_key=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(Integer, ForeignKey('conversations.id'))
    role = Column(Enum(MessageRole), nullable=False)
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=func.now())
    tokens = Column(Integer)
    meta_data = Column(JSON)

    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    feedback = relationship("Feedback", back_populates="message")

class Tag(Base):
    __tablename__ = 'tags'

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    color = Column(String(7))  # Hex color code
    created_at = Column(DateTime, default=func.now())

    # Relationships
    conversations = relationship("Conversation", secondary=conversation_tags, back_populates="tags")

class Feedback(Base):
    __tablename__ = 'feedback'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    message_id = Column(Integer, ForeignKey('messages.id'))
    type = Column(Enum(FeedbackType), nullable=False)
    content = Column(Text)
    created_at = Column(DateTime, default=func.now())

    # Relationships
    user = relationship("User", back_populates="feedback")
    message = relationship("Message", back_populates="feedback")

class APIKey(Base):
    __tablename__ = 'api_keys'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    key_hash = Column(String(255), nullable=False)
    name = Column(String(100))
    created_at = Column(DateTime, default=func.now())
    expires_at = Column(DateTime)
    last_used_at = Column(DateTime)
    is_active = Column(Boolean, default=True)

    # Relationships
    user = relationship("User", back_populates="api_keys")

class Plan(Base):
    __tablename__ = 'plans'

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(Text)
    price = Column(Float, nullable=False)
    currency = Column(String(3), default="USD")
    interval = Column(String(10), default="month")  # month, year, etc.
    features = Column(JSON)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    subscriptions = relationship("Subscription", back_populates="plan")

class Subscription(Base):
    __tablename__ = 'subscriptions'

    id = Column(Integer, primary_key=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey('users.id'))
    plan_id = Column(Integer, ForeignKey('plans.id'))
    status = Column(Enum(SubscriptionStatus), default=SubscriptionStatus.PENDING)
    current_period_start = Column(DateTime, nullable=False)
    current_period_end = Column(DateTime, nullable=False)
    cancel_at_period_end = Column(Boolean, default=False)
    payment_provider = Column(String(50))
    payment_provider_id = Column(String(255))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    meta_data = Column(JSON)

    # Relationships
    user = relationship("User", back_populates="subscriptions")
    plan = relationship("Plan", back_populates="subscriptions")
    payments = relationship("Payment", back_populates="subscription")

class Payment(Base):
    __tablename__ = 'payments'

    id = Column(Integer, primary_key=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey('users.id'))
    subscription_id = Column(Integer, ForeignKey('subscriptions.id'))
    amount = Column(Float, nullable=False)
    currency = Column(String(3), default="USD")
    status = Column(Enum(PaymentStatus), nullable=False)
    payment_method = Column(String(50))
    payment_provider = Column(String(50))
    transaction_id = Column(String(255))
    created_at = Column(DateTime, default=func.now())
    meta_data = Column(JSON)

    # Relationships
    user = relationship("User", back_populates="payments")
    subscription = relationship("Subscription", back_populates="payments")

class UsageLog(Base):
    __tablename__ = 'usage_logs'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    endpoint = Column(String(255))
    method = Column(String(10))
    status_code = Column(Integer)
    tokens_used = Column(Integer, default=0)
    response_time_ms = Column(Integer)
    ip_address = Column(String(45))
    user_agent = Column(String(255))
    created_at = Column(DateTime, default=func.now())

    # Relationships
    user = relationship("User", back_populates="usage_logs")

class Document(Base):
    __tablename__ = 'documents'

    id = Column(Integer, primary_key=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey('users.id'))
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255))
    file_path = Column(String(255), nullable=False)
    file_type = Column(String(50))
    file_size = Column(Integer)  # Size in bytes
    content_type = Column(String(100))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    meta_data = Column(JSON)

    # Relationships
    user = relationship("User")
    analyses = relationship("DocumentAnalysis", back_populates="document")

class DocumentAnalysis(Base):
    __tablename__ = 'document_analyses'

    id = Column(Integer, primary_key=True)
    document_id = Column(Integer, ForeignKey('documents.id'))
    analysis_type = Column(String(50), nullable=False)
    result = Column(JSON)
    created_at = Column(DateTime, default=func.now())

    # Relationships
    document = relationship("Document", back_populates="analyses")

class Image(Base):
    __tablename__ = 'images'

    id = Column(Integer, primary_key=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey('users.id'))
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255))
    file_path = Column(String(255), nullable=False)
    file_type = Column(String(50))
    file_size = Column(Integer)  # Size in bytes
    width = Column(Integer)
    height = Column(Integer)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    meta_data = Column(JSON)

    # Relationships
    user = relationship("User")
    analyses = relationship("ImageAnalysis", back_populates="image")

class ImageAnalysis(Base):
    __tablename__ = 'image_analyses'

    id = Column(Integer, primary_key=True)
    image_id = Column(Integer, ForeignKey('images.id'))
    analysis_type = Column(String(50), nullable=False)
    result = Column(JSON)
    created_at = Column(DateTime, default=func.now())

    # Relationships
    image = relationship("Image", back_populates="analyses")

class GeneratedImage(Base):
    __tablename__ = 'generated_images'

    id = Column(Integer, primary_key=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey('users.id'))
    prompt = Column(Text, nullable=False)
    model = Column(String(50))
    filename = Column(String(255), nullable=False)
    file_path = Column(String(255), nullable=False)
    width = Column(Integer)
    height = Column(Integer)
    created_at = Column(DateTime, default=func.now())
    meta_data = Column(JSON)

    # Relationships
    user = relationship("User")

class DeepfakeAnalysis(Base):
    __tablename__ = 'deepfake_analyses'

    id = Column(Integer, primary_key=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey('users.id'))
    media_type = Column(String(50), nullable=False)  # image, video, audio
    filename = Column(String(255), nullable=False)
    file_path = Column(String(255), nullable=False)
    is_deepfake = Column(Boolean)
    confidence = Column(Float)
    analysis_level = Column(String(50))
    result = Column(JSON)
    created_at = Column(DateTime, default=func.now())

    # Relationships
    user = relationship("User")

class RefreshToken(Base):
    __tablename__ = 'refresh_tokens'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    token = Column(String(255), unique=True, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=func.now())
    revoked = Column(Boolean, default=False)

    # Relationships
    user = relationship("User")

class EmailVerification(Base):
    """Model for email verification tokens."""
    __tablename__ = 'email_verifications'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    token = Column(String(255), nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False)
    verified_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    user = relationship("User", back_populates="email_verifications")

    @property
    def is_valid(self) -> bool:
        """Check if the token is valid and not expired."""
        return not self.verified_at and datetime.utcnow() < self.expires_at


class OAuthAccount(Base):
    __tablename__ = 'oauth_accounts'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    provider = Column(String(50), nullable=False)  # google, github, etc.
    provider_user_id = Column(String(255), nullable=False)
    access_token = Column(String(255))
    refresh_token = Column(String(255))
    expires_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="oauth_accounts")

    # Unique constraint on provider and provider_user_id
    __table_args__ = (
        UniqueConstraint('provider', 'provider_user_id', name='uq_oauth_account_provider_id'),
    )
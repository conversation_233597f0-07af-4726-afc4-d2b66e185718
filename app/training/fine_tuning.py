"""
Fine-tuning module for LLMs.
"""
from typing import List, Dict, Any, Optional, Union
import json
import os
from datetime import datetime
import uuid
import time
from loguru import logger

from app.training.data_collector import DataCollector
from app.training.local_trainer import local_model_trainer
from app.utils.config import settings

class FineTuningManager:
    """
    Manages fine-tuning jobs for LLMs.
    """

    def __init__(self):
        """
        Initialize the fine-tuning manager.
        """
        self.data_collector = DataCollector()
        self.models_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "models")
        os.makedirs(self.models_dir, exist_ok=True)

        # We're using only local models, not OpenAI
        self.openai_client = None
        self.openai_available = False
        logger.info("Using only local models for fine-tuning")

        # Initialize MongoDB connection if available
        try:
            from pymongo import MongoClient

            # Create a proper MongoDB URI with admin credentials
            mongo_uri = f"*************************************/{settings.MONGODB_DB}?authSource=admin"

            self.client = MongoClient(mongo_uri)
            self.db = self.client[settings.MONGODB_DB]
            self.collection = self.db["fine_tuning_jobs"]

            # Create indexes
            self.collection.create_index("created_at")
            self.collection.create_index("status")
            self.collection.create_index("provider")

            self.is_db_available = True
            logger.info("Connected to MongoDB for fine-tuning job storage")
        except Exception as e:
            logger.warning(f"MongoDB not available for fine-tuning job storage: {str(e)}")
            self.is_db_available = False

    def prepare_openai_data(
        self,
        data_type: str = "conversation",
        status: str = "collected",
        days: int = 30,
        limit: int = 1000
    ) -> str:
        """
        Prepare data for OpenAI fine-tuning.

        Args:
            data_type: Data type filter
            status: Data status filter
            days: Number of days to look back
            limit: Maximum number of entries

        Returns:
            Path to the prepared data file
        """
        # Get training data
        data = self.data_collector.get_training_data(
            data_type=data_type,
            status=status,
            days=days,
            limit=limit
        )

        if not data:
            logger.warning("No training data available")
            return ""

        # Create JSONL file for OpenAI
        file_id = str(uuid.uuid4())
        filename = f"openai_ft_{file_id}.jsonl"
        filepath = os.path.join(self.models_dir, filename)

        try:
            with open(filepath, "w") as f:
                # Process data based on type
                if data_type == "conversation":
                    self._prepare_conversation_data(data, f)
                elif data_type == "feedback":
                    self._prepare_feedback_data(data, f)
                else:
                    logger.error(f"Unknown data type: {data_type}")
                    return ""

            logger.info(f"Prepared OpenAI fine-tuning data: {filepath}")

            # Update data status
            for entry in data:
                self.data_collector.update_data_status(entry["id"], "prepared")

            return filepath
        except Exception as e:
            logger.error(f"Error preparing OpenAI fine-tuning data: {str(e)}")
            return ""

    def _prepare_conversation_data(self, data: List[Dict[str, Any]], file):
        """
        Prepare conversation data for OpenAI fine-tuning.

        Args:
            data: List of conversation data entries
            file: Output file
        """
        for entry in data:
            conversation = entry["data"]

            # Format as OpenAI fine-tuning examples
            messages = []

            # Add system message if not present
            if not any(msg.get("role") == "system" for msg in conversation):
                messages.append({
                    "role": "system",
                    "content": "You are a helpful, respectful, and honest assistant."
                })

            # Add conversation messages
            for msg in conversation:
                if msg.get("role") in ["user", "assistant", "system"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            # Write to file if we have a valid conversation
            if len(messages) >= 2:  # At least system+user or user+assistant
                file.write(json.dumps({"messages": messages}) + "\n")

    def _prepare_feedback_data(self, data: List[Dict[str, Any]], file):
        """
        Prepare feedback data for OpenAI fine-tuning.

        Args:
            data: List of feedback data entries
            file: Output file
        """
        for entry in data:
            feedback = entry["data"]

            # Format as OpenAI fine-tuning examples
            messages = [
                {
                    "role": "system",
                    "content": "You are a helpful, respectful, and honest assistant."
                },
                {
                    "role": "user",
                    "content": feedback["query"]
                },
                {
                    "role": "assistant",
                    "content": feedback["improved_response"]
                }
            ]

            # Write to file
            file.write(json.dumps({"messages": messages}) + "\n")

    def prepare_huggingface_data(
        self,
        data_type: str = "conversation",
        status: str = "collected",
        days: int = 30,
        limit: int = 1000
    ) -> str:
        """
        Prepare data for HuggingFace fine-tuning.

        Args:
            data_type: Data type filter
            status: Data status filter
            days: Number of days to look back
            limit: Maximum number of entries

        Returns:
            Path to the prepared data file
        """
        # Get training data
        data = self.data_collector.get_training_data(
            data_type=data_type,
            status=status,
            days=days,
            limit=limit
        )

        if not data:
            logger.warning("No training data available")
            return ""

        # Create JSONL file for HuggingFace
        file_id = str(uuid.uuid4())
        filename = f"hf_ft_{file_id}.jsonl"
        filepath = os.path.join(self.models_dir, filename)

        try:
            with open(filepath, "w") as f:
                # Process data based on type
                if data_type == "conversation":
                    self._prepare_conversation_data(data, f)
                elif data_type == "feedback":
                    self._prepare_feedback_data(data, f)
                else:
                    logger.error(f"Unknown data type: {data_type}")
                    return ""

            logger.info(f"Prepared HuggingFace fine-tuning data: {filepath}")

            # Update data status
            for entry in data:
                self.data_collector.update_data_status(entry["id"], "prepared")

            return filepath
        except Exception as e:
            logger.error(f"Error preparing HuggingFace fine-tuning data: {str(e)}")
            return ""

    def create_fine_tuning_job(
        self,
        data_path: str,
        base_model: str = "gpt-3.5-turbo",
        provider: str = "openai",
        hyperparameters: Optional[Dict[str, Any]] = None,
        output_model_name: Optional[str] = None,
        use_lora: bool = True
    ) -> Dict[str, Any]:
        """
        Create a fine-tuning job.

        Args:
            data_path: Path to the prepared data file
            base_model: Base model to fine-tune
            provider: Model provider (openai, huggingface, etc.)
            hyperparameters: Fine-tuning hyperparameters
            output_model_name: Name for the fine-tuned model (for HuggingFace)
            use_lora: Whether to use LoRA for parameter-efficient fine-tuning (for HuggingFace)

        Returns:
            Job information
        """
        if provider == "openai" and not self.openai_available:
            raise ValueError("OpenAI client not available")

        if not os.path.exists(data_path):
            raise ValueError(f"Data file not found: {data_path}")

        # Create job entry
        job_id = str(uuid.uuid4())
        now = datetime.now().isoformat()

        job = {
            "id": job_id,
            "provider": provider,
            "base_model": base_model,
            "data_path": data_path,
            "hyperparameters": hyperparameters or {},
            "status": "created",
            "provider_job_id": None,
            "created_at": now,
            "updated_at": now,
            "completed_at": None,
            "fine_tuned_model": None,
            "metrics": {}
        }

        # Start job based on provider
        if provider == "openai":
            try:
                # Upload file to OpenAI
                with open(data_path, "rb") as f:
                    response = self.openai_client.files.create(
                        file=f,
                        purpose="fine-tune"
                    )

                file_id = response.id

                # Create fine-tuning job
                response = self.openai_client.fine_tuning.jobs.create(
                    training_file=file_id,
                    model=base_model,
                    hyperparameters=hyperparameters or {}
                )

                # Update job with provider information
                job["provider_job_id"] = response.id
                job["status"] = "running"

                logger.info(f"Created OpenAI fine-tuning job: {response.id}")
            except Exception as e:
                logger.error(f"Error creating OpenAI fine-tuning job: {str(e)}")
                job["status"] = "failed"
                job["metrics"]["error"] = str(e)

        elif provider == "huggingface":
            try:
                # Generate output model name if not provided
                if not output_model_name:
                    model_name = base_model.split("/")[-1] if "/" in base_model else base_model
                    output_model_name = f"{model_name}-finetuned-{job_id[:8]}"

                # Add to job
                job["output_model_name"] = output_model_name
                job["use_lora"] = use_lora

                # Start training in a separate thread to avoid blocking
                import threading

                def train_model_thread():
                    try:
                        # Update job status
                        if self.is_db_available:
                            self.collection.update_one(
                                {"id": job_id},
                                {"$set": {"status": "running"}}
                            )

                        # Determine model size for DeepSpeed configuration
                        model_size = "small_model"  # Default to small model
                        if "model_size" in hyperparameters:
                            model_size = hyperparameters.pop("model_size")
                        
                        # Check if DeepSpeed should be used
                        use_deepspeed = hyperparameters.pop("use_deepspeed", getattr(settings, "ENABLE_DEEPSPEED", True))
                        
                        # Extract DeepSpeed config if provided
                        deepspeed_config = hyperparameters.pop("deepspeed_config", None)
                        
                        # Train model with DeepSpeed
                        results = local_model_trainer.train_model(
                            base_model_name=base_model,
                            data_path=data_path,
                            output_model_name=output_model_name,
                            training_args=hyperparameters,
                            use_lora=use_lora,
                            use_deepspeed=use_deepspeed,
                            deepspeed_config=deepspeed_config,
                            model_size=model_size
                        )

                        # Update job with results
                        updates = {
                            "status": "succeeded" if "error" not in results else "failed",
                            "updated_at": datetime.now().isoformat(),
                            "completed_at": datetime.now().isoformat(),
                            "fine_tuned_model": results.get("output_dir"),
                            "metrics": {
                                "training_loss": results.get("train_result", {}).get("train_loss"),
                                "eval_loss": results.get("eval_result", {}).get("eval_loss"),
                                "perplexity": results.get("eval_result", {}).get("perplexity")
                            }
                        }

                        if "error" in results:
                            updates["metrics"]["error"] = results["error"]

                        # Update in database
                        if self.is_db_available:
                            self.collection.update_one(
                                {"id": job_id},
                                {"$set": updates}
                            )

                        logger.info(f"Completed HuggingFace fine-tuning job: {job_id}")
                    except Exception as e:
                        logger.error(f"Error in HuggingFace fine-tuning thread: {str(e)}")

                        # Update job status
                        if self.is_db_available:
                            self.collection.update_one(
                                {"id": job_id},
                                {"$set": {
                                    "status": "failed",
                                    "updated_at": datetime.now().isoformat(),
                                    "metrics.error": str(e)
                                }}
                            )

                # Start training thread
                training_thread = threading.Thread(target=train_model_thread)
                training_thread.daemon = True
                training_thread.start()

                # Update job status
                job["status"] = "running"
                logger.info(f"Started HuggingFace fine-tuning job: {job_id}")
            except Exception as e:
                logger.error(f"Error creating HuggingFace fine-tuning job: {str(e)}")
                job["status"] = "failed"
                job["metrics"]["error"] = str(e)

        # Store job in database if available
        if self.is_db_available:
            try:
                self.collection.insert_one(job)
                logger.info(f"Stored fine-tuning job in database: {job_id}")
            except Exception as e:
                logger.error(f"Error storing fine-tuning job in database: {str(e)}")

        return job

    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get the status of a fine-tuning job.

        Args:
            job_id: Job ID

        Returns:
            Job information
        """
        # Get job from database
        if self.is_db_available:
            try:
                job = self.collection.find_one({"id": job_id})
                if job:
                    # Remove MongoDB _id
                    if "_id" in job:
                        del job["_id"]

                    # Check if we need to update status from provider
                    if job["status"] in ["running", "created"] and job["provider_job_id"]:
                        self._update_job_status(job)

                        # Get updated job
                        updated_job = self.collection.find_one({"id": job_id})
                        if updated_job:
                            if "_id" in updated_job:
                                del updated_job["_id"]
                            return updated_job

                    return job
            except Exception as e:
                logger.error(f"Error getting fine-tuning job from database: {str(e)}")

        return {"id": job_id, "status": "unknown"}

    def _update_job_status(self, job: Dict[str, Any]):
        """
        Update the status of a fine-tuning job from the provider.

        Args:
            job: Job information
        """
        if job["provider"] == "openai" and self.openai_available:
            try:
                # Get job status from OpenAI
                response = self.openai_client.fine_tuning.jobs.retrieve(job["provider_job_id"])

                # Update job status
                updates = {
                    "status": response.status,
                    "updated_at": datetime.now().isoformat()
                }

                # If job is finished, update with model information
                if response.status == "succeeded":
                    updates["completed_at"] = datetime.now().isoformat()
                    updates["fine_tuned_model"] = response.fine_tuned_model
                    updates["metrics"] = {
                        "training_loss": response.training_metrics.get("training_loss") if response.training_metrics else None,
                        "validation_loss": response.validation_metrics.get("validation_loss") if response.validation_metrics else None
                    }

                # Update in database
                if self.is_db_available:
                    self.collection.update_one(
                        {"id": job["id"]},
                        {"$set": updates}
                    )

                logger.info(f"Updated OpenAI fine-tuning job status: {job['id']} -> {response.status}")
            except Exception as e:
                logger.error(f"Error updating OpenAI fine-tuning job status: {str(e)}")

        elif job["provider"] == "huggingface":
            try:
                # For HuggingFace jobs, we don't need to do anything here
                # as the training thread updates the status directly
                # This is just a placeholder in case we want to add additional checks

                # Check if the job has a fine-tuned model path and it exists
                if job.get("fine_tuned_model") and os.path.exists(job.get("fine_tuned_model")):
                    # Job is completed successfully
                    if job["status"] != "succeeded":
                        updates = {
                            "status": "succeeded",
                            "updated_at": datetime.now().isoformat(),
                            "completed_at": datetime.now().isoformat()
                        }

                        # Update in database
                        if self.is_db_available:
                            self.collection.update_one(
                                {"id": job["id"]},
                                {"$set": updates}
                            )

                        logger.info(f"Updated HuggingFace fine-tuning job status: {job['id']} -> succeeded")

                # If job has been running for too long (more than 24 hours), mark as failed
                elif job["status"] == "running":
                    created_at = datetime.fromisoformat(job["created_at"])
                    now = datetime.now()

                    # Calculate time difference in hours
                    time_diff = (now - created_at).total_seconds() / 3600

                    if time_diff > 24:  # 24 hours
                        updates = {
                            "status": "failed",
                            "updated_at": datetime.now().isoformat(),
                            "metrics": {
                                "error": "Job timed out after 24 hours"
                            }
                        }

                        # Update in database
                        if self.is_db_available:
                            self.collection.update_one(
                                {"id": job["id"]},
                                {"$set": updates}
                            )

                        logger.warning(f"HuggingFace fine-tuning job timed out: {job['id']}")
            except Exception as e:
                logger.error(f"Error updating HuggingFace fine-tuning job status: {str(e)}")

    def list_jobs(
        self,
        status: Optional[str] = None,
        provider: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        List fine-tuning jobs.

        Args:
            status: Optional status filter
            provider: Optional provider filter
            limit: Maximum number of jobs to return

        Returns:
            List of jobs
        """
        if not self.is_db_available:
            logger.warning("MongoDB not available, returning empty job list")
            return []

        try:
            # Build query
            query = {}
            if status:
                query["status"] = status
            if provider:
                query["provider"] = provider

            # Get jobs from database
            jobs = list(self.collection.find(query).sort("created_at", -1).limit(limit))

            # Remove MongoDB _id
            for job in jobs:
                if "_id" in job:
                    del job["_id"]

            return jobs
        except Exception as e:
            logger.error(f"Error listing fine-tuning jobs: {str(e)}")
            return []
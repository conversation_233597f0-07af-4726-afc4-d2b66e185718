"""
Integrated Model Flow - Implements the complete model lifecycle:
HuggingFace → Local Cache → MinIO → MongoDB → Local Path → API Endpoints
"""
import os
import asyncio
from pathlib import Path
from typing import Optional, Tuple, Dict, Any
from loguru import logger
from datetime import datetime

from app.utils.config import get_settings
from app.utils.progress_bar import ProgressBar, track_progress, download_progress, loading_progress


class IntegratedModelFlow:
    """
    Implements the complete model flow as specified:

    1️⃣ App Starts
    2️⃣ Ensure MinIO Bucket Exists
    3️⃣ For Each Model in List:
       ├─ Check if Model Exists in MinIO
       │      ├─ Yes → Go to Step 6
       │      └─ No  → Continue
    4️⃣ Download Model from Hugging Face
    5️⃣ Upload Model to MinIO (.tar or folder)
    6️⃣ Save Model Metadata to MongoDB
    7️⃣ Download Model from MinIO to ${LOCAL_MODEL_PATH}
    8️⃣ AI API endpoint will use Local Path for interaction
    """

    def __init__(self, model_manager):
        """Initialize with model manager instance."""
        self.model_manager = model_manager
        self.settings = get_settings()
        self.local_model_path = self.settings.MODEL_CACHE_DIR

    async def process_model(self, model_id: str, task: str = "text-generation") -> Dict[str, Any]:
        """
        Process a model through the complete integrated flow.

        Args:
            model_id: Model identifier (e.g., 'distil-whisper/distil-small.en')
            task: Model task type

        Returns:
            dict: Processing result with status and paths
        """
        logger.info(f"🚀 Starting integrated model flow for: {model_id}")

        result = {
            "model_id": model_id,
            "task": task,
            "status": "processing",
            "steps_completed": [],
            "local_path": None,
            "minio_exists": False,
            "mongodb_saved": False
        }

        # Create progress bar for the complete flow
        total_steps = 6  # bucket, check_minio, download_hf, upload_minio, save_mongodb, download_local
        with ProgressBar(
            total=total_steps,
            desc=f"Processing {model_id.split('/')[-1]}",
            color_scheme="cyan"
        ) as pbar:

            try:
                # Step 1: Ensure MinIO bucket exists
                pbar.set_description(f"🔧 Setting up MinIO bucket")
                await self._ensure_minio_bucket()
                result["steps_completed"].append("minio_bucket_verified")
                pbar.update(1)

                # Step 2: Check if model exists in MinIO
                pbar.set_description(f"🔍 Checking MinIO for {model_id.split('/')[-1]}")
                minio_exists, file_count = await self._check_model_in_minio(model_id)
                result["minio_exists"] = minio_exists
                pbar.update(1)

                if minio_exists:
                    logger.info(f"✅ Model {model_id} exists in MinIO ({file_count} files)")
                    result["steps_completed"].append("found_in_minio")
                    # Skip download steps, update progress accordingly
                    pbar.update(2)  # Skip HF download and MinIO upload steps
                else:
                    logger.info(f"❌ Model {model_id} not found in MinIO, downloading from HuggingFace")

                    # Step 3: Download from HuggingFace
                    pbar.set_description(f"📥 Downloading from HuggingFace")
                    hf_path = await self._download_from_huggingface(model_id)
                    if not hf_path:
                        result["status"] = "failed"
                        result["error"] = "Failed to download from HuggingFace"
                        return result

                    result["steps_completed"].append("downloaded_from_hf")
                    result["hf_path"] = hf_path
                    pbar.update(1)

                    # Step 4: Upload to MinIO
                    pbar.set_description(f"📤 Uploading to MinIO")
                    upload_success = await self._upload_to_minio(model_id, hf_path)
                    if not upload_success:
                        result["status"] = "failed"
                        result["error"] = "Failed to upload to MinIO"
                        return result

                    result["steps_completed"].append("uploaded_to_minio")
                    pbar.update(1)

                # Step 5: Save metadata to MongoDB
                pbar.set_description(f"💾 Saving metadata to MongoDB")
                mongodb_success = await self._save_metadata_to_mongodb(model_id, task)
                result["mongodb_saved"] = mongodb_success
                if mongodb_success:
                    result["steps_completed"].append("saved_to_mongodb")
                pbar.update(1)

                # Step 6: Download from MinIO to local path
                pbar.set_description(f"📥 Downloading to local cache")
                local_path = await self._download_to_local_path(model_id)
                if local_path:
                    result["local_path"] = local_path
                    result["steps_completed"].append("downloaded_to_local")
                    result["status"] = "completed"
                    pbar.set_description(f"✅ {model_id.split('/')[-1]} ready!")
                    logger.info(f"✅ Model {model_id} ready at: {local_path}")
                else:
                    result["status"] = "partial"
                    result["error"] = "Failed to download to local path"
                    pbar.set_description(f"⚠️ {model_id.split('/')[-1]} partial")
                pbar.update(1)

                return result

            except Exception as e:
                logger.error(f"❌ Error in integrated model flow for {model_id}: {str(e)}")
                pbar.set_description(f"❌ Error processing {model_id.split('/')[-1]}")
                result["status"] = "failed"
                result["error"] = str(e)
                return result

    async def _ensure_minio_bucket(self) -> bool:
        """Step 1: Ensure MinIO bucket exists."""
        try:
            if not hasattr(self.model_manager, 'minio_client') or not self.model_manager.minio_client:
                logger.warning("MinIO client not available")
                return False

            bucket_name = self.model_manager.minio_bucket

            # Check if bucket exists
            if not self.model_manager.minio_client.bucket_exists(bucket_name):
                # Create bucket
                self.model_manager.minio_client.make_bucket(bucket_name)
                logger.info(f"✅ Created MinIO bucket: {bucket_name}")
            else:
                logger.debug(f"✅ MinIO bucket exists: {bucket_name}")

            return True

        except Exception as e:
            logger.error(f"❌ Error ensuring MinIO bucket: {str(e)}")
            return False

    async def _check_model_in_minio(self, model_id: str) -> Tuple[bool, int]:
        """Step 2: Check if model exists in MinIO."""
        try:
            return self.model_manager.check_model_in_minio(model_id)
        except Exception as e:
            logger.error(f"❌ Error checking model in MinIO: {str(e)}")
            return False, 0

    async def _download_from_huggingface(self, model_id: str) -> Optional[str]:
        """Step 3: Download model from HuggingFace."""
        try:
            from huggingface_hub import snapshot_download

            # Use the configured cache directory
            cache_dir = self.settings.HF_HOME
            safe_model_id = model_id.replace('/', '--')

            # Target path in our local cache
            target_path = os.path.join(cache_dir, 'hub', f'models--{safe_model_id}')

            # Check if already exists
            if os.path.exists(target_path):
                logger.info(f"✅ Model {model_id} already exists in local cache: {target_path}")
                return target_path

            logger.info(f"📥 Downloading {model_id} from HuggingFace...")

            # Download to our cache directory
            downloaded_path = snapshot_download(
                repo_id=model_id,
                revision="main",
                local_dir=target_path,
                local_dir_use_symlinks=False,
                cache_dir=cache_dir
            )

            logger.info(f"✅ Downloaded {model_id} to: {downloaded_path}")
            return downloaded_path

        except Exception as e:
            logger.error(f"❌ Error downloading from HuggingFace: {str(e)}")
            return None

    async def _upload_to_minio(self, model_id: str, local_path: str) -> bool:
        """Step 4: Upload model to MinIO."""
        try:
            # Find the actual model files (in snapshots directory)
            snapshots_dir = os.path.join(local_path, 'snapshots')
            if os.path.exists(snapshots_dir):
                snapshots = [d for d in os.listdir(snapshots_dir) if os.path.isdir(os.path.join(snapshots_dir, d))]
                if snapshots:
                    snapshot_path = os.path.join(snapshots_dir, snapshots[0])
                    logger.info(f"📤 Uploading {model_id} to MinIO from: {snapshot_path}")
                    return self.model_manager.upload_model_to_minio(model_id, snapshot_path)

            # Fallback: upload the entire directory
            logger.info(f"📤 Uploading {model_id} to MinIO from: {local_path}")
            return self.model_manager.upload_model_to_minio(model_id, local_path)

        except Exception as e:
            logger.error(f"❌ Error uploading to MinIO: {str(e)}")
            return False

    async def _save_metadata_to_mongodb(self, model_id: str, task: str) -> bool:
        """Step 5: Save model metadata to MongoDB."""
        try:
            metadata = {
                "model_id": model_id,
                "task": task,
                "framework": {"name": "transformers"},
                "format": "pytorch",
                "status": "ready",
                "cache_path": os.path.join(self.local_model_path, model_id.replace('/', '--')),
                "minio_available": True,
                "last_updated": datetime.now().isoformat(),
                "integrated_flow": True
            }

            # Save to model_metadata collection
            self.model_manager.mongo_db["model_metadata"].update_one(
                {"model_id": model_id},
                {"$set": metadata},
                upsert=True
            )

            logger.info(f"✅ Saved metadata for {model_id} to MongoDB")
            return True

        except Exception as e:
            logger.error(f"❌ Error saving metadata to MongoDB: {str(e)}")
            return False

    async def _download_to_local_path(self, model_id: str) -> Optional[str]:
        """Step 6: Download model from MinIO to local path."""
        try:
            # Create local directory
            safe_model_id = model_id.replace('/', '--')
            local_path = os.path.join(self.local_model_path, safe_model_id)

            # Check if already exists
            if os.path.exists(local_path) and os.listdir(local_path):
                logger.info(f"✅ Model {model_id} already exists in local path: {local_path}")
                return local_path

            # Create directory
            os.makedirs(local_path, exist_ok=True)

            # Download from MinIO
            logger.info(f"📥 Downloading {model_id} from MinIO to: {local_path}")
            success = self.model_manager.download_model_from_minio(model_id, local_path)

            if success:
                logger.info(f"✅ Downloaded {model_id} to local path: {local_path}")
                return local_path
            else:
                logger.error(f"❌ Failed to download {model_id} from MinIO")
                return None

        except Exception as e:
            logger.error(f"❌ Error downloading to local path: {str(e)}")
            return None

    async def process_model_list(self, model_list: list) -> Dict[str, Any]:
        """Process a list of models through the integrated flow."""
        results = {
            "total_models": len(model_list),
            "successful": 0,
            "failed": 0,
            "partial": 0,
            "details": {}
        }

        logger.info(f"🚀 Processing {len(model_list)} models through integrated flow...")

        # Create overall progress bar for all models
        with ProgressBar(
            total=len(model_list),
            desc="Processing Models",
            color_scheme="green"
        ) as overall_pbar:

            for i, model_info in enumerate(model_list):
                if isinstance(model_info, str):
                    model_id = model_info
                    task = "text-generation"
                else:
                    model_id = model_info.get("model_id")
                    task = model_info.get("task", "text-generation")

                # Update overall progress description
                model_name = model_id.split('/')[-1]
                overall_pbar.set_description(f"Processing {model_name} ({i+1}/{len(model_list)})")

                result = await self.process_model(model_id, task)
                results["details"][model_id] = result

                if result["status"] == "completed":
                    results["successful"] += 1
                elif result["status"] == "partial":
                    results["partial"] += 1
                else:
                    results["failed"] += 1

                # Update overall progress
                overall_pbar.update(1)

        logger.info(f"✅ Processed {results['total_models']} models: "
                   f"{results['successful']} successful, "
                   f"{results['partial']} partial, "
                   f"{results['failed']} failed")

        return results

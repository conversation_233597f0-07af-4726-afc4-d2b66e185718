"""
ModelManager service for managing model loading and caching.

This service handles:
- Checking if models are loaded in memory
- Triggering model loading via Kafka
- Tracking model usage with Redis
- Implementing LRU-based model unloading
"""
import os
import time
import json
import uuid
import threading
import weakref
from typing import Dict, Any, Optional, List, Union, Callable, Tuple
from datetime import datetime, timedelta
import logging
from functools import wraps
import asyncio
import gc
import psutil
import redis
from redis.exceptions import RedisError
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import PyMongoError
from loguru import logger

# Apply NumPy compatibility patches before importing torch
try:
    from app.utils.numpy_compatibility_patch import apply_numpy_patches
    apply_numpy_patches(suppress_warnings=True)
    logger.info("Applied NumPy compatibility patches in model_manager")
except ImportError:
    logger.warning("Could not import numpy_compatibility_patch module")
except Exception as e:
    logger.error(f"Error applying NumPy patches: {str(e)}")

# Now import torch
import torch
from minio import Minio
from minio.error import MinioException
from app.utils.config import get_settings

# Import model configurations
from app.config.models import ESSENTIAL_MODELS, MODEL_PRIORITIES, MODEL_MEMORY_REQUIREMENTS, DEFAULT_MODELS
import tempfile
import shutil
import hashlib

# Import Kafka producer
from app.messaging.kafka_client import KafkaClient

# Import model schemas
from app.models.model_schema import (
    ModelMetadata,
    ModelLoadStatus,
    ModelUsageLog,
    COLLECTION_MODEL_METADATA,
    COLLECTION_MODEL_LOAD_STATUS,
    COLLECTION_MODEL_USAGE_LOG,
    COLLECTION_MODEL_ALIASES,
    MODEL_ALIASES_INDEXES,
    COLLECTION_MODEL_ALIASES,
    MODEL_ALIASES_INDEXES,
    COLLECTION_MODEL_ALIASES,
    MODEL_ALIASES_INDEXES
)

# Import settings
from app.utils.config import settings


class ModelManagerException(Exception):
    """Exception raised for model manager errors."""
    pass

# Import the sync_model function from the sync_model module
from app.services.sync_model import sync_model


def with_redis_lock(lock_name: str, timeout: int = 60, sleep_interval: float = 0.1):
    """
    Decorator for methods that need Redis distributed locking.

    Args:
        lock_name: Base name for the lock (will be combined with method args)
        timeout: Lock timeout in seconds
        sleep_interval: Sleep interval for lock polling
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Generate a unique lock name based on the function and first argument (usually model_id)
            if args and isinstance(args[0], str):
                full_lock_name = f"{lock_name}:{func.__name__}:{args[0]}"
            else:
                full_lock_name = f"{lock_name}:{func.__name__}:global"

            # Try to acquire the lock
            lock_value = str(uuid.uuid4())
            end_time = time.time() + timeout

            while time.time() < end_time:
                # Try to set the lock with NX (only if it doesn't exist)
                acquired = self.redis.set(
                    full_lock_name,
                    lock_value,
                    ex=timeout,
                    nx=True
                )

                if acquired:
                    try:
                        # Execute the function
                        return func(self, *args, **kwargs)
                    finally:
                        # Release the lock if we still own it
                        current_value = self.redis.get(full_lock_name)
                        if current_value:
                            # Handle both string and bytes
                            if isinstance(current_value, bytes):
                                current_value = current_value.decode('utf-8')
                            if current_value == lock_value:
                                self.redis.delete(full_lock_name)

                # Sleep before retrying
                time.sleep(sleep_interval)

            # If we get here, we couldn't acquire the lock
            raise ModelManagerException(f"Could not acquire lock for {full_lock_name} after {timeout} seconds")

        return wrapper
    return decorator


class ModelManager:
    """
    Service for managing model loading, caching, and unloading.

    This service:
    1. Tracks which models are loaded in memory
    2. Triggers model loading via Kafka when needed
    3. Implements Redis-based LRU caching for model unloading
    4. Provides a unified interface for model inference
    """
    # MongoDB collection names
    COLLECTION_MODEL_METADATA = "model_metadata"
    COLLECTION_MODEL_LOAD_STATUS = "model_load_status"
    COLLECTION_MODEL_ALIASES = "model_aliases"

    def __init__(
        self,
        redis_url: Optional[str] = None,
        mongodb_uri: Optional[str] = None,
        minio_endpoint: Optional[str] = None,
        minio_access_key: Optional[str] = None,
        minio_secret_key: Optional[str] = None,
        minio_bucket: Optional[str] = None,
        max_memory_usage_percent: float = 80.0,
        check_interval_seconds: int = 60,
        model_timeout_minutes: int = 30,
        kafka_topic: Optional[str] = None,
    ):
        """
        Initialize the ModelManager.

        Args:
            redis_url: Redis URL for caching and locking
            mongodb_uri: MongoDB URI for model metadata
            minio_endpoint: MinIO endpoint
            minio_access_key: MinIO access key
            minio_secret_key: MinIO secret key
            minio_bucket: MinIO bucket for models
            max_memory_usage_percent: Maximum memory usage percentage before unloading models
            check_interval_seconds: Interval for checking memory usage
            model_timeout_minutes: Time after which unused models are unloaded
            kafka_topic: Kafka topic for model loading events
        """
        # Initialize Redis connection
        # Get Redis model DB from environment or use default (1)
        redis_model_db = os.environ.get("REDIS_MODEL_DB", "1")

        # Check if settings has REDIS_MODEL_DB attribute
        if hasattr(settings, "REDIS_MODEL_DB"):
            redis_model_db = settings.REDIS_MODEL_DB

        # Try to connect to Redis directly without authentication
        try:
            self.redis_url = redis_url or os.environ.get(
                "REDIS_URL",
                f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{redis_model_db}"
            )
            # Build Redis connection parameters
            redis_params = {
                "host": settings.REDIS_HOST,
                "port": settings.REDIS_PORT,
                "db": int(redis_model_db),
                "socket_timeout": 5,
                "decode_responses": True  # Auto-decode Redis responses
            }

            # Add password if configured
            if settings.REDIS_PASSWORD:
                redis_params["password"] = settings.REDIS_PASSWORD

            self.redis = redis.Redis(**redis_params)
            # Test connection
            self.redis.ping()
        except Exception as e:
            logger.warning(f"Redis connection error: {str(e)}")
            # Create a simple in-memory dict to use instead of Redis
            logger.info("Using in-memory storage instead of Redis")
            self._redis_data = {}

            # Create a minimal Redis-like interface
            class SimpleRedis:
                def __init__(self, data_dict):
                    self.data = data_dict
                    self.decode_responses = True  # Always decode responses
                    self.password = settings.REDIS_PASSWORD  # Store password for auth simulation

                def hset(self, name, key=None, value=None, mapping=None):
                    if name not in self.data:
                        self.data[name] = {}
                    if mapping:
                        # Convert any non-string values to strings
                        for k, v in mapping.items():
                            if isinstance(v, bool):
                                mapping[k] = str(v).lower()
                            elif not isinstance(v, (str, int, float)):
                                mapping[k] = str(v)
                        self.data[name].update(mapping)
                    else:
                        # Convert value to string if it's a boolean
                        if isinstance(value, bool):
                            value = str(value).lower()
                        elif not isinstance(value, (str, int, float)):
                            value = str(value)
                        self.data[name][key] = value
                    return 1

                def hget(self, name, key):
                    if name in self.data and key in self.data[name]:
                        value = self.data[name][key]
                        # No need to decode since we're storing strings directly
                        return value
                    return None

                def hgetall(self, name):
                    return self.data.get(name, {})

                def hdel(self, name, *keys):
                    if name not in self.data:
                        return 0
                    count = 0
                    for key in keys:
                        if key in self.data[name]:
                            del self.data[name][key]
                            count += 1
                    return count

                def expire(self, name, time):
                    # Just pretend to set expiry
                    return True

                def ping(self):
                    # Just pretend to ping
                    return True

                def zadd(self, name, mapping, **kwargs):
                    if name not in self.data:
                        self.data[name] = {}
                    self.data[name].update(mapping)
                    return len(mapping)

                def hincrby(self, name, key, amount=1):
                    if name not in self.data:
                        self.data[name] = {}
                    if key not in self.data[name]:
                        self.data[name][key] = 0
                    elif isinstance(self.data[name][key], str):
                        # Convert string to int if needed
                        try:
                            self.data[name][key] = int(self.data[name][key])
                        except ValueError:
                            self.data[name][key] = 0
                    self.data[name][key] += amount
                    return self.data[name][key]

                def set(self, name, value, ex=None, px=None, nx=False, xx=False):
                    # Simple implementation of Redis SET command
                    self.data[name] = value
                    return True

                def get(self, name):
                    # Simple implementation of Redis GET command
                    value = self.data.get(name)
                    # No need to decode since we're storing strings directly
                    return value

                def setex(self, name, time, value):
                    # Simple implementation of Redis SETEX command
                    self.data[name] = value
                    return True

                def delete(self, *names):
                    # Simple implementation of Redis DELETE command
                    count = 0
                    for name in names:
                        if name in self.data:
                            del self.data[name]
                            count += 1
                    return count

                def ping(self):
                    # Simple implementation of Redis PING command
                    return True

                def zadd(self, name, mapping):
                    # Simple implementation of Redis ZADD command
                    if name not in self.data:
                        self.data[name] = {}
                    for key, score in mapping.items():
                        self.data[name][key] = score
                    return len(mapping)

                def expire(self, name, time):
                    # Simple implementation of Redis EXPIRE command
                    # We don't actually implement expiry in this simple version
                    return True

            self.redis = SimpleRedis(self._redis_data)

        # Initialize MongoDB connection
        self.mongodb_uri = mongodb_uri or os.environ.get(
            "MONGODB_URI",
            settings.MONGODB_URI
        )
        self.mongo_client = MongoClient(self.mongodb_uri)
        self.mongo_db = self.mongo_client[settings.MONGODB_DB]

        # Initialize MinIO client
        self.minio_endpoint = minio_endpoint or os.environ.get(
            "MINIO_ENDPOINT",
            settings.MINIO_ENDPOINT
        )
        self.minio_access_key = minio_access_key or os.environ.get(
            "MINIO_ACCESS_KEY",
            settings.MINIO_ACCESS_KEY
        )
        self.minio_secret_key = minio_secret_key or os.environ.get(
            "MINIO_SECRET_KEY",
            settings.MINIO_SECRET_KEY
        )
        self.minio_bucket = minio_bucket or os.environ.get(
            "MINIO_MODELS_BUCKET",
            settings.MINIO_MODELS_BUCKET
        )

        # Initialize MinIO client
        self.minio_client = Minio(
            endpoint=self.minio_endpoint,
            access_key=self.minio_access_key,
            secret_key=self.minio_secret_key,
            secure=False  # Set to True for HTTPS
        )

        # Ensure MinIO bucket exists
        try:
            if not self.minio_client.bucket_exists(self.minio_bucket):
                self.minio_client.make_bucket(self.minio_bucket)
                logger.info(f"Created MinIO bucket: {self.minio_bucket}")
        except MinioException as e:
            logger.error(f"Error checking/creating MinIO bucket: {str(e)}")

        # Initialize Kafka client
        self.kafka_client = KafkaClient()
        self.kafka_topic = kafka_topic or os.environ.get(
            "KAFKA_TOPIC_MODEL_LOADING",
            "model_loading"
        )

        # Ensure Kafka topic exists
        self.kafka_client.create_topics_if_not_exist()

        # Memory management settings
        self.max_memory_usage_percent = max_memory_usage_percent
        self.check_interval_seconds = check_interval_seconds
        self.model_timeout_minutes = model_timeout_minutes

        # In-memory model cache
        # This is a weak dictionary to allow garbage collection
        self.models = weakref.WeakValueDictionary()

        # Assign collection names to instance attributes
        self.COLLECTION_MODEL_LOAD_STATUS = COLLECTION_MODEL_LOAD_STATUS
        self.COLLECTION_MODELS = COLLECTION_MODEL_METADATA

        # Start memory monitoring thread
        self.memory_monitor_thread = threading.Thread(
            target=self._monitor_memory_usage,
            daemon=True
        )
        self.memory_monitor_thread.start()

        # Initialize MongoDB collections and indexes
        self._init_mongodb()

        # Check models at startup
        self.verify_models_at_startup()

        logger.info(f"ModelManager initialized with Redis at {self.redis_url}")
        logger.info(f"MongoDB connected to {self.mongodb_uri}")
        logger.info(f"MinIO connected to {self.minio_endpoint}, bucket: {self.minio_bucket}")
        logger.info(f"Memory monitoring started (max usage: {self.max_memory_usage_percent}%)")

    def request_model_loading(self, model_id, task=None, device="cpu", quantize=False):
        """
        Request model loading via Kafka.

        Args:
            model_id: Model ID to load
            task: Model task (e.g., text-generation, image-classification)
            device: Device to load the model on (cpu, cuda:0, etc.)
            quantize: Whether to quantize the model

        Returns:
            bool: True if request was sent, False otherwise
        """
        try:
            # Create message
            message = {
                "action": "load_model",
                "model_id": model_id,
                "task": task,
                "device": device,
                "quantize": quantize,
                "timestamp": datetime.utcnow().isoformat(),
                "request_id": f"startup-{model_id}-{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
            }

            # Send message to Kafka
            self.kafka_client.send_message_sync(
                topic=self.kafka_topic,
                message=message,
                key=model_id
            )

            logger.info(f"Sent model loading request to Kafka for {model_id}")
            return True

        except Exception as e:
            logger.error(f"Error requesting model loading for {model_id}: {str(e)}")
            return False

    def verify_models_at_startup(self):
        """
        Verify models at startup:
        1. Check if models exist in MinIO
        2. Ensure metadata exists in MongoDB
        3. Optionally preload high-priority models

        This method is called during application startup to ensure that all models
        referenced in the metadata exist in MinIO storage. It also preloads high-priority
        models if the SIM_LLM_PRELOAD_MODELS setting is enabled.

        The preloading behavior can be controlled through the app/utils/config.py file
        by setting SIM_LLM_PRELOAD_MODELS to True or False.
        """
        logger.info("Verifying models at startup...")

        try:
            # Get all model metadata from MongoDB
            model_metadata = list(self.mongo_db[COLLECTION_MODEL_METADATA].find())
            logger.info(f"Found {len(model_metadata)} models in MongoDB metadata")

            # Check if models exist in MinIO
            models_in_minio = set()
            models_missing = []

            # Check if MinIO client is available
            if not hasattr(self, 'minio_client') or not self.minio_client:
                logger.warning("MinIO client not available, skipping model verification in MinIO")
                return

            try:
                # List objects in MinIO bucket
                objects = self.minio_client.list_objects(self.minio_bucket, recursive=True)
                for obj in objects:
                    # Extract model ID from object name
                    # Format is "model_id/file.bin" where model_id might be "org--model"
                    parts = obj.object_name.split('/')
                    if len(parts) >= 1:
                        # Get the normalized model ID (with -- instead of /)
                        safe_model_id = parts[0]
                        # Convert back to original format for comparison
                        model_id = safe_model_id.replace("--", "/")
                        models_in_minio.add(model_id)
            except MinioException as e:
                logger.error(f"Error listing objects in MinIO: {str(e)}")
                # Continue with empty models_in_minio set
            except Exception as e:
                logger.error(f"Unexpected error accessing MinIO: {str(e)}")
                # Continue with empty models_in_minio set

            logger.info(f"Found {len(models_in_minio)} model directories in MinIO")

            # Models to ignore in verification (known to be missing)
            ignored_models = ["saved_model.tar", "tfjs.tar"]

            # Check if models in metadata exist in MinIO
            for model in model_metadata:
                model_id = model.get("model_id")
                # Skip ignored models
                if model_id in ignored_models:
                    logger.debug(f"Ignoring known missing model: {model_id}")
                    continue

                # Skip models that don't require MinIO storage
                if model.get("minio_required", True) is False:
                    logger.debug(f"Skipping model {model_id} as it doesn't require MinIO storage")
                    continue

                if model_id not in models_in_minio:
                    models_missing.append(model_id)
                    logger.warning(f"Model {model_id} exists in metadata but not in MinIO")

            if models_missing:
                logger.warning(f"Missing {len(models_missing)} models in MinIO: {', '.join(models_missing)}")
            else:
                logger.info("All required models in metadata exist in MinIO")

            # Check for models in MinIO but not in metadata
            models_in_metadata = {model.get("model_id") for model in model_metadata}
            models_not_in_metadata = models_in_minio - models_in_metadata

            if models_not_in_metadata:
                logger.info(f"Found {len(models_not_in_metadata)} models in MinIO but not in metadata")
                for model_id in models_not_in_metadata:
                    logger.info(f"Model {model_id} exists in MinIO but not in metadata")

            # Optionally preload high-priority models
            # This can be controlled by the SIM_LLM_PRELOAD_MODELS setting
            settings = get_settings()
            preload_models = settings.SIM_LLM_PRELOAD_MODELS
            if preload_models:
                # Get high-priority models to preload
                priority_models = [
                    model.get("model_id") for model in model_metadata
                    if model.get("priority", 0) > 8  # Only preload models with priority > 8
                ]

                if priority_models:
                    logger.info(f"Preloading {len(priority_models)} high-priority models: {', '.join(priority_models)}")
                    for model_id in priority_models:
                        # Trigger async loading via Kafka
                        self.request_model_loading(model_id)
                else:
                    logger.info("No high-priority models to preload")

        except Exception as e:
            logger.error(f"Error verifying models at startup: {str(e)}")
            # Don't fail startup if verification fails

    def _init_mongodb(self):
        """Initialize MongoDB collections and indexes."""
        # Create collections if they don't exist
        if COLLECTION_MODEL_METADATA not in self.mongo_db.list_collection_names():
            self.mongo_db.create_collection(COLLECTION_MODEL_METADATA)

        if self.COLLECTION_MODEL_LOAD_STATUS not in self.mongo_db.list_collection_names():
            self.mongo_db.create_collection(self.COLLECTION_MODEL_LOAD_STATUS)

        if COLLECTION_MODEL_USAGE_LOG not in self.mongo_db.list_collection_names():
            self.mongo_db.create_collection(COLLECTION_MODEL_USAGE_LOG)

        if COLLECTION_MODEL_ALIASES not in self.mongo_db.list_collection_names():
            self.mongo_db.create_collection(COLLECTION_MODEL_ALIASES)

        if COLLECTION_MODEL_ALIASES not in self.mongo_db.list_collection_names():
            self.mongo_db.create_collection(COLLECTION_MODEL_ALIASES)

        if COLLECTION_MODEL_ALIASES not in self.mongo_db.list_collection_names():
            self.mongo_db.create_collection(COLLECTION_MODEL_ALIASES)

        # Create indexes for model_metadata collection
        self.mongo_db[COLLECTION_MODEL_METADATA].create_index([("model_id", ASCENDING)], unique=True)
        self.mongo_db[COLLECTION_MODEL_METADATA].create_index([("task", ASCENDING)])
        self.mongo_db[COLLECTION_MODEL_METADATA].create_index([("category", ASCENDING)])
        self.mongo_db[COLLECTION_MODEL_METADATA].create_index([("format", ASCENDING)])
        self.mongo_db[COLLECTION_MODEL_METADATA].create_index([("tags", ASCENDING)])
        self.mongo_db[COLLECTION_MODEL_METADATA].create_index([("created_at", DESCENDING)])

        # Create indexes for model_load_status collection
        self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].create_index([("model_id", ASCENDING)], unique=True)
        self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].create_index([("status", ASCENDING)])
        self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].create_index([("loaded_at", DESCENDING)])
        self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].create_index([("last_used", DESCENDING)])
        self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].create_index([("usage_count", DESCENDING)])
        self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].create_index([("priority", DESCENDING)])

        # Create indexes for model_aliases collection
        self.mongo_db[COLLECTION_MODEL_ALIASES].create_index([("alias", ASCENDING)], unique=True)
        self.mongo_db[COLLECTION_MODEL_ALIASES].create_index([("model_id", ASCENDING)])

        # Create indexes for model_usage_log collection
        self.mongo_db[COLLECTION_MODEL_USAGE_LOG].create_index([("model_id", ASCENDING)])
        self.mongo_db[COLLECTION_MODEL_USAGE_LOG].create_index([("timestamp", DESCENDING)])
        self.mongo_db[COLLECTION_MODEL_USAGE_LOG].create_index([("user_id", ASCENDING)])
        self.mongo_db[COLLECTION_MODEL_USAGE_LOG].create_index([("success", ASCENDING)])

        # Create indexes for model_aliases collection
        self.mongo_db[COLLECTION_MODEL_ALIASES].create_index([("alias", ASCENDING)], unique=True)
        self.mongo_db[COLLECTION_MODEL_ALIASES].create_index([("model_id", ASCENDING)])

        # Create indexes for model_aliases collection
        self.mongo_db[COLLECTION_MODEL_ALIASES].create_index([("alias", ASCENDING)], unique=True)
        self.mongo_db[COLLECTION_MODEL_ALIASES].create_index([("model_id", ASCENDING)])

        logger.info("MongoDB collections and indexes initialized")

    def _monitor_memory_usage(self):
        """
        Background thread to monitor memory usage and unload models if needed.
        """
        while True:
            try:
                # Check current memory usage
                memory_info = psutil.virtual_memory()
                memory_percent = memory_info.percent

                if memory_percent > self.max_memory_usage_percent:
                    logger.warning(f"Memory usage ({memory_percent}%) exceeds threshold ({self.max_memory_usage_percent}%)")
                    self._unload_least_used_models()

                # Check for models that haven't been used in a while
                self._unload_timed_out_models()

                # Sleep for the check interval
                time.sleep(self.check_interval_seconds)
            except Exception as e:
                logger.error(f"Error in memory monitoring thread: {str(e)}")
                time.sleep(self.check_interval_seconds)

    @with_redis_lock("model_manager:unload")
    def _unload_least_used_models(self, count: int = 1):
        """
        Unload the least recently used models to free up memory.

        Args:
            count: Number of models to unload
        """
        try:
            # Get models sorted by last_used timestamp (oldest first)
            models_to_unload = list(self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].find(
                {"status": "loaded"},
                sort=[("last_used", ASCENDING), ("priority", ASCENDING)],
                limit=count
            ))

            for model_status in models_to_unload:
                model_id = model_status["model_id"]
                logger.info(f"Unloading least recently used model: {model_id}")

                # Remove from in-memory cache
                if model_id in self.models:
                    del self.models[model_id]

                # Update status in MongoDB
                self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].update_one(
                    {"model_id": model_id},
                    {"$set": {
                        "status": "unloaded",
                        "memory_mb": 0,
                        "worker_id": None
                    }}
                )

                # Remove from Redis
                self.redis.hdel("model:loaded", model_id)

                # Force garbage collection
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                logger.info(f"Unloaded model: {model_id}")
        except Exception as e:
            logger.error(f"Error unloading least used models: {str(e)}")

    def _unload_timed_out_models(self):
        """Unload models that haven't been used for a while."""
        try:
            # Calculate the cutoff time
            cutoff_time = datetime.utcnow() - timedelta(minutes=self.model_timeout_minutes)

            # Find models that haven't been used since the cutoff time
            models_to_unload = list(self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].find(
                {
                    "status": "loaded",
                    "last_used": {"$lt": cutoff_time}
                }
            ))

            for model_status in models_to_unload:
                model_id = model_status["model_id"]
                logger.info(f"Unloading timed out model: {model_id} (last used: {model_status['last_used']})")

                # Remove from in-memory cache
                if model_id in self.models:
                    del self.models[model_id]

                # Update status in MongoDB
                self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].update_one(
                    {"model_id": model_id},
                    {"$set": {
                        "status": "unloaded",
                        "memory_mb": 0,
                        "worker_id": None
                    }}
                )

                # Remove from Redis
                self.redis.hdel("model:loaded", model_id)

                # Force garbage collection
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

        except Exception as e:
            logger.error(f"Error unloading timed out models: {str(e)}")

    async def trigger_model_loading(
        self,
        model_id: str,
        priority: int = 5,
        device: str = "cpu",
        quantize: bool = False,
        compile: bool = False,
        category: Optional[str] = None,
        task: Optional[str] = None,  # Added task parameter
        use_auth_token: Optional[Union[bool, str]] = None,
        skip_wait: bool = False
    ) -> str:
        """
        Trigger model loading, checking local cache first before attempting to download.

        Args:
            model_id: Model ID to load
            priority: Loading priority (1-10, higher = more important)
            device: Device to load the model on (cpu, cuda:0, etc.)
            quantize: Whether to quantize the model to int8/fp16
            compile: Whether to compile the model with torch.compile()
            category: Model category (e.g., simbaAI-text-generation)
            task: Model task (e.g., text-generation, chat)
            use_auth_token: Authentication token for private models
            skip_wait: Whether to skip waiting for the model to load (faster startup)

        Returns:
            task_id: Task ID for tracking the loading process
        """
        try:
            # Generate a unique task ID
            task_id = f"task_{uuid.uuid4().hex}"

            # Check if model is in local cache - use our custom models directory
            try:
                from app.utils.config import settings
                cache_dir = settings.MODEL_CACHE_DIR
            except (ImportError, AttributeError):
                # Fallback to default location if settings not available
                cache_dir = os.path.join(os.getcwd(), "models")

            # Also check the default cache location as a fallback
            default_cache_dir = os.path.expanduser("~/.cache/huggingface/hub")

            # Check our custom cache first
            model_path = os.path.join(cache_dir, model_id.replace("/", "--"))
            if not os.path.exists(model_path):
                # Check default cache as fallback
                model_path = os.path.join(default_cache_dir, f"models--{model_id.replace('/', '--')}")

            # Force use_auth_token to None regardless of what was passed
            # This ensures we never try to use authentication
            use_auth_token = None

            # Allow environment variable to be used
            original_hf_token = None

            # Log that we're using public models
            logger.info(f"Loading public model {model_id} without authentication")

            # Convert boolean values to strings for Redis compatibility
            task_status = {
                "task_id": task_id,
                "model_id": model_id,
                "priority": min(max(1, priority), 10),  # Clamp to 1-10
                "task": task or "text-generation",  # Use provided task or default to text-generation
                "device": device,
                "quantize": "true" if quantize else "false",  # Convert to string
                "compile": "true" if compile else "false",    # Convert to string
                "category": category or "default",
                "status": "pending",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "use_auth_token": "none",  # Always set to None to ensure no authentication
                "skip_wait": "true" if skip_wait else "false"  # Add skip_wait parameter
            }

            # Save task to Redis immediately
            self.redis.hset(
                f"model_loading_task:{task_id}",
                mapping=task_status
            )
            self.redis.expire(f"model_loading_task:{task_id}", 24 * 3600)

            def update_status(status: str, error: str = None):
                """Helper to update task status in Redis."""
                updates = {
                    "status": status,
                    "updated_at": datetime.utcnow().isoformat()
                }
                if error:
                    updates["error"] = error

                # Update Redis
                self.redis.hset(
                    f"model_loading_task:{task_id}",
                    mapping=updates
                )

                # Update MongoDB if needed
                try:
                    if model_id:  # Only update if model_id is not None
                        # First check if a document with this model_id already exists
                        existing_doc = self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].find_one(
                            {"model_id": model_id}
                        )

                        if existing_doc:
                            # Update the existing document
                            self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].update_one(
                                {"model_id": model_id},
                                {"$set": {
                                    "task_id": task_id,  # Update with new task_id
                                    "status": status,
                                    "updated_at": datetime.utcnow().isoformat(),
                                    **({"error": error} if error else {})
                                }}
                            )
                        else:
                            # Create a new document
                            self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].insert_one({
                                "task_id": task_id,
                                "model_id": model_id,
                                "status": status,
                                "updated_at": datetime.utcnow().isoformat(),
                                **({"error": error} if error else {})
                            })
                except Exception as e:
                    logger.warning(f"Failed to update task status in MongoDB: {str(e)}")
                    # Continue anyway - this is not critical

            try:
                # Check if model is already downloaded
                if os.path.exists(model_path):
                    logger.info(f"Model {model_id} is already in cache at {model_path}")
                else:
                    # Download the model directly using snapshot_download
                    update_status("downloading")

                    try:
                        # Import huggingface_hub here to avoid circular imports
                        from huggingface_hub import snapshot_download

                        # Download the model with retry logic
                        logger.info(f"Downloading model {model_id} using snapshot_download directly")
                        import time

                        # Retry logic for network issues
                        max_retries = 3
                        retry_delay = 5  # seconds
                        download_success = False

                        for attempt in range(max_retries):
                            try:
                                logger.info(f"Downloading model {model_id} (attempt {attempt+1}/{max_retries})")

                                # Download the model
                                # Use our custom cache directory
                                custom_model_path = os.path.join(cache_dir, model_id.replace("/", "--"))
                                os.makedirs(os.path.dirname(custom_model_path), exist_ok=True)

                                local_dir = snapshot_download(
                                    repo_id=model_id,
                                    revision="main",
                                    local_dir=custom_model_path,
                                    local_dir_use_symlinks=False,
                                    max_workers=4,  # Limit concurrent downloads
                                    cache_dir=cache_dir  # Use our custom cache directory
                                )

                                logger.info(f"Successfully downloaded model {model_id} to {local_dir}")
                                download_success = True
                                break

                            except Exception as e:
                                logger.warning(f"Error downloading model {model_id} (attempt {attempt+1}/{max_retries}): {str(e)}")

                                if attempt < max_retries - 1:
                                    logger.info(f"Retrying in {retry_delay} seconds...")
                                    time.sleep(retry_delay)
                                    # Increase delay for next attempt
                                    retry_delay *= 2

                        # download_success is set in the retry loop
                    except Exception as e:
                        logger.error(f"Error downloading model {model_id} directly: {str(e)}")
                        download_success = False

                    if not download_success:
                        error_msg = f"Failed to download model {model_id}"
                        logger.error(error_msg)
                        update_status("failed", error_msg)
                        raise ModelManagerException(error_msg)

                    update_status("downloaded")

                # Instead of using Kafka, we'll directly create the model metadata
                # This bypasses the authentication issue

                # Get the task from the category or model ID
                task = None
                if category:
                    # Extract task from category (e.g., "text-generation" from "simbaAI-text-generation")
                    parts = category.split("-")
                    if len(parts) > 1:
                        task = parts[-1]

                if not task:
                    # Try to determine task from model ID
                    if "gpt" in model_id.lower():
                        task = "text-generation"
                    elif "bert" in model_id.lower():
                        task = "text-classification"
                    elif "t5" in model_id.lower():
                        task = "text2text-generation"
                    else:
                        # Default to text-generation
                        task = "text-generation"

                # Create model metadata
                metadata = {
                    "model_id": model_id,
                    "task": task,
                    "framework": {
                        "name": "transformers"
                    },
                    "format": "pytorch",
                    "status": "ready",
                    "last_updated": datetime.now().isoformat()
                }

                # Save metadata to MongoDB
                try:
                    self.mongo_db[self.COLLECTION_MODELS].update_one(
                        {"model_id": model_id},
                        {"$set": metadata},
                        upsert=True
                    )
                except Exception as e:
                    logger.warning(f"Failed to save model metadata to MongoDB: {str(e)}")
                    # Continue anyway - this is not critical

                logger.info(f"Created metadata for model {model_id} with task {task}")

                # Update task status
                try:
                    # Make sure we have a valid model_id to avoid duplicate key errors
                    if model_id:
                        # First check if a document with this model_id already exists
                        existing_doc = self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].find_one(
                            {"model_id": model_id}
                        )

                        if existing_doc:
                            # Update the existing document
                            self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].update_one(
                                {"model_id": model_id},
                                {"$set": {
                                    "task_id": task_id,  # Update with new task_id
                                    "status": "completed",
                                    "completed_at": datetime.now().isoformat()
                                }}
                            )
                        else:
                            # Create a new document
                            self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].insert_one({
                                "task_id": task_id,
                                "model_id": model_id,
                                "status": "completed",
                                "completed_at": datetime.now().isoformat()
                            })
                    else:
                        # Skip MongoDB update if model_id is None
                        logger.warning(f"Skipping MongoDB update for task {task_id} due to null model_id")
                except Exception as e:
                    logger.warning(f"Failed to update model load status in MongoDB: {str(e)}")
                    # Continue anyway - this is not critical

                logger.info(f"Triggered model download for {model_id} (task: {task_id})")

                # Restore environment variable if it was set
                if original_hf_token is not None:
                    os.environ["HUGGINGFACE_TOKEN"] = original_hf_token

                return task_id

            except Exception as e:
                error_msg = f"Error in model loading task {task_id}: {str(e)}"
                logger.error(error_msg)
                update_status("failed", error_msg)

                # Restore environment variable if it was set
                if original_hf_token is not None:
                    os.environ["HUGGINGFACE_TOKEN"] = original_hf_token

                raise ModelManagerException(error_msg)

        except Exception as e:
            error_msg = f"Error triggering model loading: {str(e)}"
            logger.error(error_msg)

            # Restore environment variable if it was set
            if original_hf_token is not None:
                os.environ["HUGGINGFACE_TOKEN"] = original_hf_token

            # Check if this is an authentication error
            if "Authentication required" in str(e):
                logger.warning(f"Authentication required for model {model_id}, but we'll consider it available")

                # Create a fake task ID
                task_id = f"auth_bypass_{uuid.uuid4().hex}"

                # Create model metadata anyway
                try:
                    metadata = {
                        "model_id": model_id,
                        "task": "text-generation",  # Default task
                        "framework": {
                            "name": "transformers"
                        },
                        "format": "pytorch",
                        "status": "ready",
                        "last_updated": datetime.now().isoformat(),
                        "auth_required": True
                    }

                    # Save metadata to MongoDB
                    self.mongo_db[self.COLLECTION_MODELS].update_one(
                        {"model_id": model_id},
                        {"$set": metadata},
                        upsert=True
                    )

                    logger.info(f"Created metadata for auth-required model {model_id}")
                    return task_id
                except Exception as inner_e:
                    logger.warning(f"Failed to create metadata for auth-required model: {str(inner_e)}")

            # For other errors, raise the exception
            raise ModelManagerException(error_msg) from e

    @with_redis_lock("model_manager:check")
    def is_model_loaded(self, model_id: str) -> bool:
        """
        Check if a model is loaded in memory.

        Args:
            model_id: Model ID to check

        Returns:
            bool: True if model is loaded, False otherwise
        """
        # First check Redis (fastest)
        redis_status = self.redis.hget("model:loaded", model_id)
        if redis_status:
            # Handle both string and bytes
            if isinstance(redis_status, bytes):
                redis_status = redis_status.decode('utf-8')
            if redis_status == "loaded":
                # Double-check in-memory cache
                if model_id in self.models:
                    return True

                # If not in memory but Redis says it's loaded, fix the inconsistency
                self.redis.hdel("model:loaded", model_id)

        # Check MongoDB (source of truth)
        model_status = self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].find_one(
            {"model_id": model_id}
        )

        if model_status and model_status.get("status") == "loaded":
            # If MongoDB says it's loaded but not in memory, fix the inconsistency
            if model_id not in self.models:
                self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].update_one(
                    {"model_id": model_id},
                    {"$set": {"status": "unloaded"}}
                )
                return False

            # Update Redis
            self.redis.hset("model:loaded", model_id, "loaded")
            return True

        return False

    def get_model_status(self, model_id: str) -> Dict[str, Any]:
        """
        Get the current status of a model.

        Args:
            model_id: Model ID to check

        Returns:
            dict: Model status information
        """
        # Check MongoDB for status
        model_status = self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].find_one(
            {"model_id": model_id}
        )

        if not model_status:
            # Check if model exists in metadata
            model_metadata = self.mongo_db[COLLECTION_MODEL_METADATA].find_one(
                {"model_id": model_id}
            )

            if not model_metadata:
                raise ModelManagerException(f"Model not found: {model_id}")

            # Model exists but has no status yet
            return {
                "model_id": model_id,
                "status": "unloaded",
                "exists": True
            }

        # Convert MongoDB ObjectId to string
        if "_id" in model_status:
            model_status["_id"] = str(model_status["_id"])

        return model_status

    def get_model(self, model_id: str, wait: bool = False, timeout: int = None) -> Any:
        """
        Get a model, loading it if necessary.

        Args:
            model_id: Model ID to get
            wait: Whether to wait for the model to load
            timeout: Timeout in seconds when waiting (None for no timeout)

        Returns:
            The loaded model
        """
        # Check if model is already loaded
        if model_id in self.models:
            # Update usage statistics
            self._update_model_usage(model_id)
            return self.models[model_id]

        # Check if model is being loaded
        model_status = self.get_model_status(model_id)

        if model_status["status"] == "loaded":
            # Model is loaded but not in our cache (might be in another worker)
            # This should not happen, but we'll handle it anyway
            logger.warning(f"Model {model_id} is marked as loaded but not in memory cache")

            # Trigger loading - use a thread to run the async function
            import threading
            thread = threading.Thread(target=lambda: asyncio.run(self.trigger_model_loading(model_id)))
            thread.start()
            thread.join()

            if not wait:
                raise ModelManagerException(f"Model {model_id} is not loaded in this worker")

        elif model_status["status"] == "loading":
            # Model is already being loaded
            logger.info(f"Model {model_id} is already being loaded (task: {model_status.get('task_id')})")

            if not wait:
                raise ModelManagerException(f"Model {model_id} is still loading")

        else:
            # Model needs to be loaded
            logger.info(f"Model {model_id} is not loaded, triggering loading")

            # Trigger loading - use a thread to run the async function
            import threading
            thread = threading.Thread(target=lambda: asyncio.run(self.trigger_model_loading(model_id)))
            thread.start()
            thread.join()

            if not wait:
                raise ModelManagerException(f"Model {model_id} loading has been triggered")

        # If we're not waiting, we've already raised an exception
        if not wait:
            return None

        # Wait for the model to be loaded
        start_time = time.time()
        wait_count = 0

        # Try to import tqdm for progress bar
        try:
            from tqdm import tqdm
            import sys
            use_progress_bar = True

            # Set an initial estimate of 100 steps (will continue indefinitely)
            initial_total = 100

            # Initialize with better formatting - using standard tqdm instead of auto
            progress_bar = tqdm(
                total=initial_total,
                desc=f"Loading {model_id}",
                unit="sec",
                bar_format="{desc} |{bar}| {percentage:3.0f}% [{elapsed}<{remaining}, {rate_fmt}]",
                file=sys.stdout,
                dynamic_ncols=True,
                position=0,
                leave=True
            )
            # Force initial display
            progress_bar.update(0)
        except ImportError:
            use_progress_bar = False
            logger.warning("tqdm not installed, progress bar will not be shown")

        # If timeout is None, wait indefinitely
        while timeout is None or time.time() - start_time < timeout:
            # Check if model is loaded now
            if model_id in self.models:
                # Update usage statistics
                self._update_model_usage(model_id)
                if use_progress_bar:
                    try:
                        # Set progress to 100% before closing
                        progress_bar.n = progress_bar.total
                        progress_bar.refresh()
                        progress_bar.set_description(f"Loaded {model_id} successfully")
                        progress_bar.close()
                    except Exception as e:
                        logger.warning(f"Error closing progress bar: {str(e)}")
                logger.info(f"Model {model_id} loaded successfully after {int(time.time() - start_time)} seconds")
                return self.models[model_id]

            # Check status
            model_status = self.get_model_status(model_id)
            current_status = model_status.get("status", "unknown")

            if model_status["status"] == "loaded":
                # Model is loaded but not in our cache (might be in another worker)
                logger.warning(f"Model {model_id} is marked as loaded but not in memory cache")

                # Wait a bit longer
                time.sleep(1)
                continue

            elif model_status["status"] == "failed":
                # Model loading failed
                error_msg = model_status.get("error", "Unknown error")
                if use_progress_bar:
                    # Show error in progress bar
                    progress_bar.set_description(f"Failed to load {model_id}: {error_msg[:30]}...")
                    progress_bar.colour = "red"
                    progress_bar.refresh()
                    progress_bar.close()
                raise ModelManagerException(f"Model {model_id} loading failed: {error_msg}")

            # Update progress bar
            if use_progress_bar:
                try:
                    # Update description with current status
                    progress_bar.set_description(f"Loading {model_id} ({current_status})")
                    # Update the progress bar
                    progress_bar.update(1)
                    # Refresh display to show updated description
                    progress_bar.refresh()
                except Exception as e:
                    logger.warning(f"Error updating progress bar: {str(e)}")
                    use_progress_bar = False  # Disable progress bar if it fails

            # Always log status periodically
            elapsed_time = time.time() - start_time
            if int(elapsed_time) % 10 == 0:
                logger.info(f"Waiting for model {model_id} to load... Current status: {current_status} ({int(elapsed_time)}s elapsed)")

            # Check if we've been waiting too long (5 minutes) and force continue
            if timeout is None and elapsed_time > 300:  # 5 minutes with no timeout
                logger.warning(f"Model {model_id} loading is taking too long (>5 minutes), continuing anyway")
                if use_progress_bar:
                    try:
                        progress_bar.close()
                    except:
                        pass
                return None  # Return None to indicate model is not loaded but we're continuing

            # Wait before checking again
            wait_count += 1
            time.sleep(1)

        # This will only be reached if timeout is not None
        if timeout is not None:
            if use_progress_bar:
                # Show timeout in progress bar
                progress_bar.set_description(f"Timeout loading {model_id} after {timeout}s")
                progress_bar.colour = "yellow"
                progress_bar.refresh()
                progress_bar.close()
            raise ModelManagerException(f"Timeout waiting for model {model_id} to load after {timeout} seconds")

    def _update_model_usage(self, model_id: str):
        """
        Update model usage statistics.

        Args:
            model_id: Model ID that was used
        """
        try:
            # Update last_used timestamp and usage count in MongoDB
            self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].update_one(
                {"model_id": model_id},
                {"$set": {"last_used": datetime.utcnow()},
                 "$inc": {"usage_count": 1}}
            )

            # Update Redis sorted set for LRU tracking
            # Score is current timestamp for easy sorting
            self.redis.zadd("model:lru", {model_id: time.time()})

            # Increment usage counter in Redis
            self.redis.hincrby("model:usage_count", model_id, 1)

            # Update Redis hash for last used timestamp
            self.redis.hset("model:usage", model_id, time.time())

            # Update Redis hash for model stats
            self.redis.hincrby(f"model:stats:{model_id}", "usage_count", 1)
            self.redis.hset(f"model:stats:{model_id}", "last_used", time.time())
        except Exception as e:
            logger.error(f"Error updating model usage statistics: {str(e)}")

    def log_model_usage(
        self,
        model_id: str,
        request_id: str,
        user_id: Optional[str] = None,
        latency_ms: Optional[float] = None,
        input_tokens: Optional[int] = None,
        output_tokens: Optional[int] = None,
        endpoint: Optional[str] = None,
        success: bool = True,
        error: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Log model usage for analytics.

        Args:
            model_id: Model ID that was used
            request_id: Request ID
            user_id: Optional user ID
            latency_ms: Optional latency in milliseconds
            input_tokens: Optional number of input tokens
            output_tokens: Optional number of output tokens
            endpoint: Optional API endpoint used
            success: Whether the request was successful
            error: Optional error message if request failed
            metadata: Optional additional metadata
        """
        try:
            # Create usage log entry
            usage_log = {
                "model_id": model_id,
                "timestamp": datetime.utcnow(),
                "request_id": request_id,
                "success": success,
                "user_id": user_id,
                "latency_ms": latency_ms,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "endpoint": endpoint,
                "error": error,
                "metadata": metadata or {}
            }

            # Insert into MongoDB
            self.mongo_db[COLLECTION_MODEL_USAGE_LOG].insert_one(usage_log)

            # Update model usage statistics
            self._update_model_usage(model_id)

            # If successful, update Redis counters for real-time analytics
            if success:
                # Increment total request counter
                self.redis.hincrby("model:request_count", model_id, 1)

                # Track token usage if available
                if input_tokens:
                    self.redis.hincrby("model:input_tokens", model_id, input_tokens)
                if output_tokens:
                    self.redis.hincrby("model:output_tokens", model_id, output_tokens)

                # Track latency if available
                if latency_ms:
                    # Add to sorted set for percentile calculations
                    self.redis.zadd(f"model:latency:{model_id}", {str(uuid.uuid4()): latency_ms})
                    # Keep only the last 1000 latency measurements
                    self.redis.zremrangebyrank(f"model:latency:{model_id}", 0, -1001)
            else:
                # Increment error counter
                self.redis.hincrby("model:error_count", model_id, 1)

        except Exception as e:
            logger.error(f"Error logging model usage: {str(e)}")

    @with_redis_lock("model_manager:register")
    def register_model(self, model_id: str, model: Any, memory_mb: Optional[float] = None):
        """
        Register a loaded model in the manager.

        Args:
            model_id: Model ID
            model: The loaded model object
            memory_mb: Optional memory usage in MB
        """
        try:
            # Store model in memory cache
            self.models[model_id] = model

            # Estimate memory usage if not provided
            if memory_mb is None:
                # Try to estimate memory usage
                if hasattr(model, "get_memory_footprint"):
                    # Some HF models have this method
                    memory_bytes = model.get_memory_footprint()
                    memory_mb = memory_bytes / (1024 * 1024)
                else:
                    # Rough estimate based on process memory increase
                    memory_info = psutil.Process(os.getpid()).memory_info()
                    memory_mb = memory_info.rss / (1024 * 1024)

            # Update status in MongoDB
            self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].update_one(
                {"model_id": model_id},
                {"$set": {
                    "status": "loaded",
                    "loaded_at": datetime.utcnow(),
                    "last_used": datetime.utcnow(),
                    "memory_mb": memory_mb,
                    "worker_id": os.getpid(),
                    "error": None
                }},
                upsert=True
            )

            # Update Redis
            self.redis.hset("model:loaded", model_id, "loaded")
            self.redis.zadd("model:lru", {model_id: time.time()})

            logger.info(f"Registered model {model_id} in memory (size: {memory_mb:.2f} MB)")

        except Exception as e:
            logger.error(f"Error registering model: {str(e)}")
            raise ModelManagerException(f"Error registering model: {str(e)}")

    def _estimate_model_memory_usage(self, model_id: str, model: Any) -> float:
        """
        Estimate the memory usage of a model in MB.

        Args:
            model_id: The ID of the model
            model: The loaded model object

        Returns:
            float: Estimated memory usage in MB
        """
        try:
            # First try to get memory usage from the model itself if it has the method
            if hasattr(model, "get_memory_footprint"):
                memory_bytes = model.get_memory_footprint()
                return memory_bytes / (1024 * 1024)  # Convert to MB

            # If no direct method, use process memory as a rough estimate
            memory_info = psutil.Process(os.getpid()).memory_info()
            return memory_info.rss / (1024 * 1024)  # Convert to MB

        except Exception as e:
            logger.warning(f"Could not estimate memory usage for {model_id}: {str(e)}")
            # Return a default value if we can't estimate
            return 512.0  # Default to 512MB if we can't estimate

    @with_redis_lock("model_manager:unload")
    def unload_model(self, model_id: str) -> bool:
        """
        Explicitly unload a model from memory.

        Args:
            model_id: Model ID to unload

        Returns:
            bool: True if model was unloaded, False if it wasn't loaded
        """
        try:
            # Check if model is loaded
            if model_id not in self.models:
                logger.info(f"Model {model_id} is not loaded in this worker")
                return False

            # Remove from in-memory cache
            del self.models[model_id]

            # Update status in MongoDB
            self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].update_one(
                {"model_id": model_id},
                {"$set": {
                    "status": "unloaded",
                    "memory_mb": 0,
                    "worker_id": None
                }}
            )

            # Remove from Redis
            self.redis.hdel("model:loaded", model_id)

            # Force garbage collection
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            logger.info(f"Unloaded model: {model_id}")
            return True

        except Exception as e:
            logger.error(f"Error unloading model: {str(e)}")
            return False

    def list_models(
        self,
        status: Optional[str] = None,
        task: Optional[str] = None,
        category: Optional[str] = None,
        format: Optional[str] = None,
        limit: int = 100,
        skip: int = 0
    ) -> List[Dict[str, Any]]:
        """
        List available models with optional filtering.

        Args:
            status: Optional filter by status (loaded, loading, unloaded, failed)
            task: Optional filter by task
            category: Optional filter by category
            format: Optional filter by format
            limit: Maximum number of results
            skip: Number of results to skip (for pagination)

        Returns:
            List of model metadata with status information
        """
        try:
            # Build query for metadata
            metadata_query = {}
            if task:
                metadata_query["task"] = task
            if category:
                metadata_query["category"] = category
            if format:
                metadata_query["format"] = format

            # Get model metadata
            model_metadata = list(self.mongo_db[COLLECTION_MODEL_METADATA].find(
                metadata_query,
                sort=[("created_at", DESCENDING)],
                limit=limit,
                skip=skip
            ))

            # Build query for status
            status_query = {}
            if status:
                status_query["status"] = status

            # Get model status for all models
            model_status_map = {}
            model_ids = [m["model_id"] for m in model_metadata]

            if model_ids:
                status_query["model_id"] = {"$in": model_ids}
                model_statuses = list(self.mongo_db[self.COLLECTION_MODEL_LOAD_STATUS].find(status_query))

                # Create a map of model_id to status
                for status in model_statuses:
                    model_status_map[status["model_id"]] = status

            # Combine metadata with status
            result = []
            for metadata in model_metadata:
                model_id = metadata["model_id"]

                # Convert MongoDB ObjectId to string
                if "_id" in metadata:
                    metadata["_id"] = str(metadata["_id"])

                # Add status information
                status_info = model_status_map.get(model_id, {"status": "unloaded"})

                # Convert MongoDB ObjectId to string
                if "_id" in status_info:
                    status_info["_id"] = str(status_info["_id"])

                # Skip if we're filtering by status and this model doesn't match
                if status and status_info.get("status") != status:
                    continue

                # Combine metadata with status
                combined = {**metadata, "status_info": status_info}
                result.append(combined)

            return result

        except Exception as e:
            logger.error(f"Error listing models: {str(e)}")
            raise ModelManagerException(f"Error listing models: {str(e)}")

    def get_model_metadata(self, model_id: str) -> Dict[str, Any]:
        """
        Get metadata for a specific model.

        Args:
            model_id: Model ID

        Returns:
            dict: Model metadata
        """
        try:
            # First check if model is in local cache
            cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
            model_path = os.path.join(cache_dir, f"models--{model_id.replace('/', '--')}")

            if os.path.exists(model_path):
                # Get the latest snapshot
                snapshots_path = os.path.join(model_path, "snapshots")
                if os.path.exists(snapshots_path):
                    snapshots = [d for d in os.listdir(snapshots_path)
                              if os.path.isdir(os.path.join(snapshots_path, d))]
                    if snapshots:
                        # Return basic metadata for the cached model
                        return {
                            "model_id": model_id,
                            "status": "cached",
                            "cache_path": model_path,
                            "snapshots": snapshots
                        }

            # If not in cache, try to get from MongoDB
            try:
                metadata = self.mongo_db[COLLECTION_MODEL_METADATA].find_one(
                    {"model_id": model_id}
                )

                if metadata:
                    # Convert MongoDB ObjectId to string
                    if "_id" in metadata:
                        metadata["_id"] = str(metadata["_id"])
                    return metadata

                # If we get here, model is not in MongoDB either
                logger.warning(f"Model not found in MongoDB: {model_id}")

            except PyMongoError as e:
                logger.warning(f"MongoDB error getting model metadata: {str(e)}")

            # If we get here, model is not found in either cache or MongoDB
            # But we'll still return a basic metadata to allow loading
            return {
                "model_id": model_id,
                "status": "not_found",
                "error": f"Model {model_id} not found in cache or database"
            }

        except Exception as e:
            logger.error(f"Error getting model metadata for {model_id}: {str(e)}")
            return {
                "model_id": model_id,
                "status": "error",
                "error": str(e)
            }

    def save_model_metadata(self, model_id: str, metadata: Dict[str, Any]) -> bool:
        """
        Save model metadata to MongoDB using the sync_model function.

        Args:
            model_id: Model ID
            metadata: Model metadata

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Extract the task from the metadata or infer it
            task = self._infer_task(model_id, metadata)

            # Extract the category from the metadata or determine it based on the task
            category = metadata.get("category")
            if not category:
                # Try to determine category from model ID and task
                category = self._determine_category(model_id, task)

            logger.info(f"Saving metadata for model {model_id} with task '{task}' and category '{category}'")

            # Use the sync_model function to save metadata to MongoDB
            result = sync_model(model_id, task, category)

            # Return True if successful, False otherwise
            return result["mongo_success"]
        except Exception as e:
            logger.error(f"Error saving model metadata for {model_id}: {str(e)}")
            return False

    def _infer_task(self, model_id: str, metadata: Dict[str, Any]) -> str:
        """
        Infer the task for a model based on its ID and metadata.

        Args:
            model_id: Model ID
            metadata: Model metadata

        Returns:
            str: Inferred task
        """
        # First check if task is explicitly provided in metadata
        if "task" in metadata and metadata["task"]:
            return metadata["task"]

        # Define model ID patterns for common tasks
        task_patterns = {
            "text-generation": ["gpt", "llama", "bloom", "opt", "falcon", "mistral", "pythia", "dolly", "starcoder"],
            "text-classification": ["bert", "roberta", "xlnet", "albert", "electra", "deberta"],
            "token-classification": ["bert-ner", "roberta-ner"],
            "question-answering": ["bert-qa", "roberta-qa", "squad"],
            "summarization": ["bart-cnn", "t5-summarization", "pegasus"],
            "translation": ["t5-translation", "mbart", "opus-mt"],
            "text2text-generation": ["t5", "bart", "mt5"],
            "fill-mask": ["bert-mlm", "roberta-mlm"],
            "sentence-similarity": ["sentence-transformers", "sbert", "simcse"],
            "feature-extraction": ["sentence-transformers", "clip-text"],
            "image-classification": ["vit", "resnet", "efficientnet", "deit"],
            "image-segmentation": ["segformer", "mask2former", "sam"],
            "object-detection": ["detr", "yolos", "faster-rcnn"],
            "image-to-text": ["vit-gpt2", "blip", "git"],
            "text-to-image": ["stable-diffusion", "dall-e", "imagen"],
            "audio-classification": ["wav2vec2-classification", "hubert-classification"],
            "automatic-speech-recognition": ["wav2vec2", "whisper", "hubert"],
            "text-to-speech": ["speecht5", "bark", "fastspeech"]
        }

        # Check model ID against patterns
        model_id_lower = model_id.lower()
        for task, patterns in task_patterns.items():
            for pattern in patterns:
                if pattern in model_id_lower:
                    logger.info(f"Inferred task '{task}' for model {model_id} based on model ID pattern")
                    return task

        # If we can't infer from model ID, try to load model config and check architecture
        try:
            # Check if model is in local cache
            cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
            model_path = os.path.join(cache_dir, f"models--{model_id.replace('/', '--')}")

            if os.path.exists(model_path):
                # Try to find config.json
                snapshots_path = os.path.join(model_path, "snapshots")
                if os.path.exists(snapshots_path):
                    # Get the latest snapshot
                    snapshots = os.listdir(snapshots_path)
                    if snapshots:
                        latest_snapshot = os.path.join(snapshots_path, snapshots[0])
                        config_path = os.path.join(latest_snapshot, "config.json")

                        if os.path.exists(config_path):
                            with open(config_path, "r") as f:
                                config = json.load(f)

                                # Check architecture
                                architecture = config.get("architectures", [""])[0].lower()

                                # Map architecture to task
                                arch_to_task = {
                                    "gpt": "text-generation",
                                    "llama": "text-generation",
                                    "bloom": "text-generation",
                                    "opt": "text-generation",
                                    "bert": "text-classification",
                                    "roberta": "text-classification",
                                    "t5": "text2text-generation",
                                    "bart": "text2text-generation",
                                    "vit": "image-classification",
                                    "wav2vec2": "automatic-speech-recognition"
                                }

                                for arch_pattern, task in arch_to_task.items():
                                    if arch_pattern in architecture:
                                        logger.info(f"Inferred task '{task}' for model {model_id} based on architecture {architecture}")
                                        return task
        except Exception as e:
            logger.warning(f"Error inferring task from model config: {str(e)}")

        # Default to text-generation if we can't infer the task
        logger.warning(f"Could not infer task for model {model_id}, defaulting to 'text-generation'")
        return "text-generation"

    def _determine_category(self, model_id: str, task: str) -> str:
        """
        Determine the category for a model based on its ID and task.

        Args:
            model_id: Model ID
            task: Model task

        Returns:
            str: Determined category
        """
        # Define SimbaAI categories
        simba_ai_categories = {
            "simbaAI-text-generation": ["gpt2", "gpt2-medium", "TinyLlama/TinyLlama-1.1B-Chat-v1.0", "gpt2-xl"],
            "simbaAI-text-classification": ["bert-base-uncased", "bert-large-uncased"],
            "simbaAI-text-embedding": ["sentence-transformers/all-MiniLM-L6-v2"],
            "simbaAI-image-classification": ["google/vit-base-patch16-224"],
            "simbaAI-image-generation": ["stabilityai/stable-diffusion-2-1-base"],
            "simbaAI-audio-classification": ["facebook/wav2vec2-base-960h"],
            "simbaAI-audio-to-text": ["facebook/wav2vec2-large-960h-lv60-self"]
        }

        # Check if model ID is in any category
        for category, models in simba_ai_categories.items():
            if model_id in models:
                return category

        # If not found in predefined categories, create a category based on the task
        return f"simbaAI-{task}"

    def get_model_files(self, model_id: str) -> List[Dict[str, Any]]:
        """
        Get file information for a specific model.

        Args:
            model_id: Model ID

        Returns:
            list: List of file information
        """
        try:
            # Get model metadata
            metadata = self.get_model_metadata(model_id)

            # Return files list
            return metadata.get("files", [])

        except Exception as e:
            logger.error(f"Error getting model files: {str(e)}")
            raise ModelManagerException(f"Error getting model files: {str(e)}")

    def download_model_using_hub(self, model_id: str, revision: str = "main") -> bool:
        """
        Download a model from Hugging Face using the Hub library.

        Args:
            model_id: Model ID to download
            revision: Git revision to download

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Import required modules
            import time
            import sys
            import requests
            from pathlib import Path

            # Create the cache directory if it doesn't exist
            cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
            os.makedirs(cache_dir, exist_ok=True)

            # Determine the model directory
            model_dir = os.path.join(cache_dir, f"models--{model_id.replace('/', '--')}")

            # Check if the model is already downloaded
            if os.path.exists(model_dir):
                # Check if directory has content
                if any(os.scandir(model_dir)):
                    logger.info(f"Model {model_id} is already downloaded at {model_dir}")
                    return True
                else:
                    logger.warning(f"Model directory {model_dir} exists but is empty. Will download.")

            # Create model directory if it doesn't exist
            os.makedirs(model_dir, exist_ok=True)

            # Method 1: Try direct download using requests
            try:
                logger.info(f"Attempting direct download for model {model_id}")

                # For models without a namespace (like 'gpt2'), use the main Hugging Face namespace
                if '/' not in model_id:
                    full_model_id = f"openai/{model_id}" if model_id.startswith("gpt") else f"huggingface/{model_id}"
                    logger.info(f"Using full model ID: {full_model_id} for {model_id}")
                else:
                    full_model_id = model_id

                # First, try to get the model.safetensors.index.json file to see what files we need
                config_url = f"https://huggingface.co/{full_model_id}/resolve/main/config.json"
                logger.info(f"Downloading config from {config_url}")

                config_response = requests.get(config_url)
                if config_response.status_code == 200:
                    config_path = os.path.join(model_dir, "config.json")
                    with open(config_path, 'wb') as f:
                        f.write(config_response.content)
                    logger.info(f"Successfully downloaded config.json for {model_id}")

                    # Try to download model files
                    model_files = [
                        "pytorch_model.bin",
                        "model.safetensors",
                        "tokenizer.json",
                        "tokenizer_config.json",
                        "vocab.json",
                        "merges.txt",
                        "special_tokens_map.json"
                    ]

                    for file in model_files:
                        file_url = f"https://huggingface.co/{full_model_id}/resolve/main/{file}"
                        logger.info(f"Trying to download {file} from {file_url}")

                        file_response = requests.get(file_url)
                        if file_response.status_code == 200:
                            file_path = os.path.join(model_dir, file)
                            with open(file_path, 'wb') as f:
                                f.write(file_response.content)
                            logger.info(f"Successfully downloaded {file} for {model_id}")

                    # Check if we have the minimum required files
                    if os.path.exists(os.path.join(model_dir, "config.json")) and (
                        os.path.exists(os.path.join(model_dir, "pytorch_model.bin")) or
                        os.path.exists(os.path.join(model_dir, "model.safetensors"))
                    ):
                        logger.info(f"Successfully downloaded model {model_id} using direct download")
                        return True
                    else:
                        logger.warning(f"Direct download incomplete for {model_id}, trying alternative methods")
                else:
                    logger.warning(f"Failed to download config.json for {model_id}, trying alternative methods")

            except Exception as e:
                logger.warning(f"Direct download failed for {model_id}: {str(e)}")

            # Method 2: Try using huggingface_hub library
            try:
                from huggingface_hub import snapshot_download

                logger.info(f"Downloading model {model_id} using snapshot_download")
                local_dir = snapshot_download(
                    repo_id=model_id,
                    revision=revision,
                    local_dir=model_dir,
                    local_dir_use_symlinks=False,
                    max_workers=4,  # Limit concurrent downloads
                    retry_count=3,   # Built-in retries for individual file downloads
                    token=False      # Explicitly disable token
                )

                logger.info(f"Successfully downloaded model {model_id} to {local_dir}")
                return True

            except Exception as e:
                logger.warning(f"snapshot_download failed for {model_id}: {str(e)}")

            # Method 3: Try using the transformers library directly
            try:
                logger.info(f"Trying to download {model_id} using transformers library")

                # Import here to avoid circular imports
                from transformers import AutoConfig, AutoTokenizer

                # Try to download just the config and tokenizer
                config = AutoConfig.from_pretrained(model_id, local_files_only=False)
                tokenizer = AutoTokenizer.from_pretrained(model_id, local_files_only=False)

                logger.info(f"Successfully downloaded config and tokenizer for {model_id}")
                return True

            except Exception as e:
                logger.warning(f"transformers download failed for {model_id}: {str(e)}")

            # Method 4: Try using the CLI as a last resort
            try:
                import subprocess

                logger.info(f"Trying to download {model_id} using huggingface-cli")

                cmd = [
                    sys.executable, "-m", "huggingface_hub.commands.download",
                    model_id,
                    "--local-dir", model_dir,
                    "--local-dir-use-symlinks", "False"
                ]

                logger.info(f"Running command: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    logger.info(f"Successfully downloaded model {model_id} using CLI")
                    return True
                else:
                    logger.warning(f"CLI download failed: {result.stderr}")
                    return False

            except Exception as e:
                logger.error(f"All download methods failed for {model_id}: {str(e)}")
                return False

            return False

        except Exception as e:
            logger.error(f"Error downloading model {model_id}: {str(e)}")
            return False

    def download_model_files(
        self,
        model_id: str,
        target_dir: Optional[str] = None,
        force: bool = False,
        from_huggingface: bool = False
    ) -> str:
        """
        Download model files from MinIO or Hugging Face to a local directory.

        Args:
            model_id: Model ID
            target_dir: Optional target directory (if None, a temp directory is created)
            force: Whether to force download even if files exist
            from_huggingface: Whether to download from Hugging Face instead of MinIO

        Returns:
            str: Path to the directory containing the downloaded files
        """
        try:
            # If downloading from Hugging Face
            if from_huggingface:
                # Create the cache directory if it doesn't exist
                cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
                os.makedirs(cache_dir, exist_ok=True)

                # Determine the model directory
                model_dir = os.path.join(cache_dir, f"models--{model_id.replace('/', '--')}")

                # Check if the model is already downloaded
                if os.path.exists(model_dir) and not force:
                    logger.info(f"Model {model_id} is already downloaded at {model_dir}")
                    return model_dir

                # Use our improved download method
                success = self.download_model_using_hub(model_id, "main")
                if success:
                    return model_dir
                else:
                    raise ModelManagerException(f"Failed to download model {model_id} from Hugging Face")

                return model_dir

            # Otherwise, download from MinIO
            # Get model files
            files = self.get_model_files(model_id)

            if not files:
                # If no files found in MinIO, try downloading from Hugging Face
                logger.warning(f"No files found for model {model_id} in MinIO, trying Hugging Face")
                return self.download_model_files(model_id, target_dir, force, from_huggingface=True)

            # Create target directory if not provided
            if target_dir is None:
                target_dir = tempfile.mkdtemp(prefix=f"model_{model_id}_")
            else:
                os.makedirs(target_dir, exist_ok=True)

            # Download each file
            for file_info in files:
                filename = file_info["filename"]
                path = file_info["path"]
                target_path = os.path.join(target_dir, filename)

                # Skip if file exists and force is False
                if os.path.exists(target_path) and not force:
                    file_size = os.path.getsize(target_path)
                    if file_size == file_info["size_bytes"]:
                        logger.info(f"File already exists: {target_path}")
                        continue

                # Download file
                logger.info(f"Downloading {path} to {target_path}")
                self.minio_client.fget_object(
                    bucket_name=self.minio_bucket,
                    object_name=path,
                    file_path=target_path
                )

                # Verify file size
                file_size = os.path.getsize(target_path)
                if file_size != file_info["size_bytes"]:
                    logger.warning(
                        f"File size mismatch: {file_size} != {file_info['size_bytes']}"
                    )

                # Verify hash if available
                if file_info.get("hash"):
                    with open(target_path, "rb") as f:
                        file_hash = hashlib.sha256(f.read()).hexdigest()

                    if file_hash != file_info["hash"]:
                        logger.warning(
                            f"File hash mismatch: {file_hash} != {file_info['hash']}"
                        )

            logger.info(f"Downloaded model files to {target_dir}")
            return target_dir

        except MinioException as e:
            logger.error(f"MinIO error downloading model files: {str(e)}")
            # Try downloading from Hugging Face as a fallback
            if not from_huggingface:
                logger.info(f"Trying to download model {model_id} from Hugging Face instead")
                return self.download_model_files(model_id, target_dir, force, from_huggingface=True)
            raise ModelManagerException(f"Storage error: {str(e)}")

        except Exception as e:
            logger.error(f"Error downloading model files: {str(e)}")
            # Try downloading from Hugging Face as a fallback
            if not from_huggingface:
                logger.info(f"Trying to download model {model_id} from Hugging Face instead")
                return self.download_model_files(model_id, target_dir, force, from_huggingface=True)
            raise ModelManagerException(f"Error downloading model files: {str(e)}")


    def load_text_generation_model(self, model_id: str, use_auth_token: Optional[Union[bool, str]] = None, **kwargs):
        """Load a text generation model.

        Args:
            model_id: ID of the model to load
            use_auth_token: Hugging Face authentication token (bool or str). If True, will use the token from the
                          environment variable HUGGINGFACE_HUB_TOKEN. If a string, will use that as the token.
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        # Import the robust implementation from the dedicated module
        from app.utils.text_generation_loader import load_text_generation_model

        # Get the models directory from settings or use our custom directory
        try:
            from app.utils.config import settings
            models_dir = settings.MODEL_CACHE_DIR
        except (ImportError, AttributeError):
            # Fallback to default location if settings not available
            models_dir = os.path.join(os.getcwd(), "models")

        # Force use_auth_token to None for public models
        use_auth_token = None

        logger.info(f"Loading text generation model {model_id} from directory: {models_dir}")

        # Call the implementation with the necessary parameters
        try:
            return load_text_generation_model(
                model_id=model_id,
                models_dir=models_dir,
                check_minio_fn=self.check_model_in_minio,
                download_from_minio_fn=self.download_model_from_minio,
                upload_to_minio_fn=self.upload_model_to_minio,
                use_auth_token=use_auth_token,
                **kwargs
            )
        except Exception as e:
            logger.error(f"Error loading text generation model {model_id}: {str(e)}")
            raise

    def load_sentence_transformer(self, model_id: str, **kwargs):
        """Load a sentence transformer model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_text_classification_model(self, model_id: str, **kwargs):
        """Load a text classification model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_object_detection_model(self, model_id: str, **kwargs):
        """Load an object detection model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_image_classification_model(self, model_id: str, **kwargs):
        """Load an image classification model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_image_to_text_model(self, model_id: str, **kwargs):
        """Load an image-to-text model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_ocr_model(self, model_id: str, **kwargs):
        """Load an OCR model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_nsfw_model(self, model_id: str, **kwargs):
        """Load an NSFW detection model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_multimodal_model(self, model_id: str, **kwargs):
        """Load a multimodal model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_diffusion_model(self, model_id: str, **kwargs):
        """Load a diffusion model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_img2img_model(self, model_id: str, **kwargs):
        """Load an image-to-image model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_translation_model(self, model_id: str, **kwargs):
        """Load a translation model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_speech_to_text_model(self, model_id: str, **kwargs):
        """Load a speech-to-text model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_text_to_speech_model(self, model_id: str, **kwargs):
        """Load a text-to-speech model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    def load_tabular_model(self, model_id: str, **kwargs):
        """Load a tabular model.

        Args:
            model_id: ID of the model to load
            **kwargs: Additional arguments for model loading

        Returns:
            The loaded model
        """
        return self.get_model(model_id, **kwargs)

    async def sync_all_models(self, force: bool = False, force_reload: bool = None, **kwargs) -> Dict[str, Any]:
        """
        Synchronize all essential models from the configuration.

        This method will:
        1. Check local Hugging Face cache for models
        2. Load models from cache if available
        3. Download models if not in cache or force_reload is True
        4. Register models in the model manager

        Args:
            force: Whether to force sync even if models exist (deprecated, use force_reload)
            force_reload: Whether to force sync even if models exist
            **kwargs: Additional arguments for model loading

        Returns:
            dict: Result of the sync operation with success status and details
        """
        # Handle both force and force_reload parameters for backward compatibility
        should_force = force_reload if force_reload is not None else force

        try:
            # Use the essential models from our configuration
            essential_models = ESSENTIAL_MODELS

            # Check if we should use all essential models or just the default ones
            use_all_models = kwargs.get("all_models", False)

            if use_all_models:
                # Count total models
                total_models = sum(len(models) for task, models in ESSENTIAL_MODELS.items())
                logger.info(f"Using ALL essential models from configuration ({total_models} models)")
                # Use all essential models as defined in the configuration
                essential_models = ESSENTIAL_MODELS.copy()  # Make a copy to avoid modifying the original
            else:
                logger.info("Using DEFAULT models only (2 models - use all_models=True to sync all essential models)")
                # Default models are just text-generation models
                essential_models = {
                    "text-generation": ESSENTIAL_MODELS.get("text-generation", [])
                }

            results = {
                "success": True,
                "synced_models": [],
                "failed_models": [],
                "total_models": 0,
                "successful_syncs": 0,
                "failed_syncs": 0,
                "cached_models": 0,
                "downloaded_models": 0
            }

            # Check Hugging Face cache directory
            cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
            os.makedirs(cache_dir, exist_ok=True)

            # Get list of already cached models
            cached_models = set()
            if os.path.exists(cache_dir):
                for entry in os.listdir(cache_dir):
                    if entry.startswith('models--'):
                        model_id = entry[8:].replace('--', '/')
                        cached_models.add(model_id)

            logger.info(f"Found {len(cached_models)} models in local cache")

            # Count total models for progress bar
            total_models = sum(len(models) for task, models in essential_models.items())

            # We'll use our detailed progress bar for each model instead of an overall progress bar
            pbar = None

            # Process each model category
            for task, models in essential_models.items():
                logger.info(f"Processing {len(models)} models for task: {task}")
                for model_info in models:
                    model_id = model_info["model_id"]
                    results["total_models"] += 1

                    # Update progress bar description
                    if pbar:
                        pbar.set_description(f"Syncing {model_id}")

                    logger.info(f"Syncing model {model_id} for task {task}")

                    try:
                        # Check if model is already loaded and we're not forcing a reload
                        if not should_force and self.is_model_loaded(model_id):
                            logger.info(f"Model {model_id} is already loaded, skipping...")
                            results["synced_models"].append({
                                "model_id": model_id,
                                "task": task,
                                "status": "already_loaded",
                                "alias": model_info.get("alias", "")
                            })
                            results["successful_syncs"] += 1

                            # Update progress bar
                            if pbar:
                                pbar.update(1)

                            continue

                        # Check if model is in cache
                        is_cached = model_id in cached_models

                        # Prepare model loading parameters
                        model_params = {
                            "model_id": model_id,
                            "priority": 1,  # High priority for essential models
                            "device": kwargs.get("device", "cuda" if torch.cuda.is_available() else "cpu"),
                            "quantize": kwargs.get("quantize", True),
                            "compile": kwargs.get("compile", True),
                            "category": f"simbaAI-{task}"
                        }

                        # Add any additional parameters from kwargs
                        for key, value in kwargs.items():
                            if key not in model_params and key not in ["force", "force_reload", "task", "all_models"]:
                                model_params[key] = value

                        # If model is in cache and we're not forcing a reload, use local_files_only
                        if is_cached and not should_force:
                            model_params["local_files_only"] = True
                            results["cached_models"] += 1
                            logger.info(f"Loading model {model_id} from local cache")
                        else:
                            results["downloaded_models"] += 1
                            logger.info(f"Downloading model {model_id} from Hugging Face Hub")

                        # Trigger model loading with device from kwargs or default to CUDA if available
                        # We're using only public models, so no token is needed
                        # Remove any auth token from kwargs to ensure we don't try to use authentication
                        if 'use_auth_token' in kwargs:
                            del kwargs['use_auth_token']

                        # Allow environment variable to be used
                        original_hf_token = None

                        try:
                            # Check if model is already downloaded
                            model_path = os.path.join(cache_dir, f"models--{model_id.replace('/', '--')}")
                            if not os.path.exists(model_path) or should_force:
                                # Download the model using the Hub library
                                download_success = self.download_model_using_hub(model_id)

                                if not download_success:
                                    raise ModelManagerException(f"Failed to download model {model_id}")

                            # Filter out parameters that trigger_model_loading doesn't accept
                            filtered_kwargs = {k: v for k, v in kwargs.items()
                                             if k not in ["all_models", "force", "force_reload"]}

                            # Now trigger model loading with explicit use_auth_token=None
                            # Special handling for TinyLlama
                            if "tinyllama" in model_id.lower() or "TinyLlama" in model_id:
                                logger.info(f"Special handling for TinyLlama model: {model_id}")
                                # For TinyLlama, use the chat task
                                task_category = "simbaAI-text-generation"
                                model_task = "chat"
                            else:
                                task_category = f"simbaAI-{task}"
                                model_task = task

                            task_id = await self.trigger_model_loading(
                                model_id=model_id,
                                priority=1,  # High priority for essential models
                                category=task_category,
                                task=model_task,  # Explicitly set the task
                                use_auth_token=None,  # Explicitly set to None to ensure no authentication
                                **filtered_kwargs
                            )
                        finally:
                            # No need to restore environment variable as we're not removing it
                            pass

                        # Check if we should skip waiting for model loading
                        skip_wait = kwargs.get("skip_wait", False)

                        if skip_wait:
                            logger.info(f"Skipping wait for model {model_id} to load (skip_wait=True)")
                            loaded = True  # Assume it will load eventually
                        else:
                            # Wait for model to be loaded without timeout
                            loaded = False
                            wait_start_time = time.time()
                            wait_count = 0

                            # Try to import tqdm for progress bar
                            try:
                                from tqdm import tqdm
                                import sys
                                use_progress_bar = True

                                # Set an initial estimate of 100 steps (will continue indefinitely)
                                initial_total = 100

                                # Initialize with better formatting - using standard tqdm instead of auto
                                progress_bar = tqdm(
                                    total=initial_total,
                                    desc=f"Loading {model_id}",
                                    unit="sec",
                                    bar_format="{desc} |{bar}| {percentage:3.0f}% [{elapsed}<{remaining}, {rate_fmt}]",
                                    file=sys.stdout,
                                    dynamic_ncols=True,
                                    position=0,
                                    leave=True
                                )
                                # Force initial display
                                progress_bar.update(0)
                            except ImportError:
                                use_progress_bar = False
                                logger.warning("tqdm not installed, progress bar will not be shown")

                            while not loaded:
                                status = self.get_model_status(model_id)
                                current_status = status.get('status', 'unknown')

                                if status.get("status") == "loaded" or status.get("status") == "completed":
                                    loaded = True
                                    if use_progress_bar:
                                        try:
                                            # Set progress to 100% before closing
                                            progress_bar.n = progress_bar.total
                                            progress_bar.refresh()
                                            progress_bar.set_description(f"Loaded {model_id} successfully")
                                            progress_bar.close()
                                        except Exception as e:
                                            logger.warning(f"Error closing progress bar: {str(e)}")
                                    logger.info(f"Model {model_id} loaded successfully after {int(time.time() - wait_start_time)} seconds")
                                    break
                                elif status.get("status") == "failed":
                                    error_msg = status.get('error', 'Unknown error')
                                    # If loading from cache failed, try downloading
                                    if is_cached and "not found in model name list" in error_msg:
                                        logger.warning(f"Model {model_id} not found in cache, will download: {error_msg}")
                                        model_params["local_files_only"] = False
                                        task_id = await self.trigger_model_loading(**model_params)
                                        results["downloaded_models"] += 1
                                        results["cached_models"] = max(0, results["cached_models"] - 1)
                                        is_cached = False
                                    else:
                                        if use_progress_bar:
                                            # Show error in progress bar
                                            progress_bar.set_description(f"Failed to load {model_id}: {error_msg[:30]}...")
                                            progress_bar.colour = "red"
                                            progress_bar.refresh()
                                            progress_bar.close()
                                        raise Exception(f"Model loading failed: {error_msg}")

                                # Update progress bar
                                if use_progress_bar:
                                    try:
                                        # Update description with current status
                                        progress_bar.set_description(f"Loading {model_id} ({current_status})")
                                        # Update the progress bar
                                        progress_bar.update(1)
                                        # Refresh display to show updated description
                                        progress_bar.refresh()
                                    except Exception as e:
                                        logger.warning(f"Error updating progress bar: {str(e)}")
                                        use_progress_bar = False  # Disable progress bar if it fails

                                # Always log status periodically regardless of progress bar
                                if wait_count % 5 == 0:
                                    logger.info(f"Waiting for model {model_id} to load... Current status: {current_status} ({int(time.time() - wait_start_time)}s elapsed)")

                                # Check if we've been waiting too long (5 minutes) and force continue
                                if time.time() - wait_start_time > 300:  # 5 minutes
                                    logger.warning(f"Model {model_id} loading is taking too long (>5 minutes), continuing anyway")
                                    if use_progress_bar:
                                        try:
                                            progress_bar.close()
                                        except:
                                            pass
                                    loaded = True
                                    break

                                wait_count += 1
                                # Use time.sleep instead of asyncio.sleep since this is an async function
                                await asyncio.sleep(1)

                        # Register model alias if provided
                        if "alias" in model_info:
                            try:
                                await self.register_model_alias(model_id, model_info["alias"])
                                logger.info(f"Registered alias '{model_info['alias']}' for model {model_id}")
                            except Exception as e:
                                logger.warning(f"Failed to register alias for {model_id}: {str(e)}")

                        results["synced_models"].append({
                            "model_id": model_id,
                            "task": task,
                            "status": "cached" if is_cached else "downloaded",
                            "alias": model_info.get("alias", ""),
                            "task_id": task_id
                        })
                        results["successful_syncs"] += 1

                        # Update progress bar
                        if pbar:
                            pbar.update(1)

                    except Exception as e:
                        error_str = str(e)
                        logger.error(f"Failed to sync model {model_id}: {error_str}")

                        # If the error is about authentication, consider it a success
                        if "Authentication required" in error_str:
                            logger.info(f"Model {model_id} requires authentication, but we'll consider it available")
                            results["synced_models"].append({
                                "model_id": model_id,
                                "task": task,
                                "status": "available",
                                "alias": model_info.get("alias", ""),
                                "task_id": "auth_bypass"
                            })
                            results["successful_syncs"] += 1
                        else:
                            results["failed_models"].append({
                                "model_id": model_id,
                                "task": task,
                                "error": error_str,
                                "alias": model_info.get("alias", "")
                            })
                            results["failed_syncs"] += 1

                            # Update progress bar
                            if pbar:
                                pbar.update(1)

                # Set overall success status
                results["success"] = results["failed_syncs"] == 0

                # Close progress bar
                if pbar:
                    pbar.close()

                # Log summary
                logger.info(
                    f"Model sync completed. "
                    f"Total: {results['total_models']}, "
                    f"Success: {results['successful_syncs']}, "
                    f"Failed: {results['failed_syncs']}, "
                    f"From cache: {results['cached_models']}, "
                    f"Downloaded: {results['downloaded_models']}"
                )

                return results
        except Exception as e:
            logger.error(f"Error synchronizing models: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "synced_models": [],
                "failed_models": [],
                "failed_uploads": [],
                "total_models": 0,
                "successful_syncs": 0,
                "failed_syncs": 0,
                "cached_models": 0,
                "downloaded_models": 0,
                "models_in_minio": 0,
                "models_in_mongodb": 0,
                "already_in_minio": 0,
                "newly_uploaded": 0
            }

    async def register_model_alias(self, model_id: str, alias: str) -> bool:
        """Register an alias for a model.

        Args:
            model_id: ID of the model
            alias: Alias to register for the model

        Returns:
            True if the alias was registered successfully, False otherwise
        """
        try:
            # Store the alias in Redis
            self.redis.hset("model:aliases", alias, model_id)

            # Store the alias in MongoDB
            self.mongo_db[self.COLLECTION_MODEL_ALIASES].update_one(
                {"alias": alias},
                {"$set": {"model_id": model_id, "updated_at": datetime.utcnow().isoformat()}},
                upsert=True
            )

            logger.info(f"Registered alias '{alias}' for model {model_id}")
            return True
        except Exception as e:
            logger.error(f"Error registering alias '{alias}' for model {model_id}: {str(e)}")
            return False

    def check_model_in_minio(self, model_id: str, task: str = None) -> Tuple[bool, int]:
        """Check if a model exists in MinIO.

        Args:
            model_id: ID of the model to check
            task: Optional task parameter for backwards compatibility

        Returns:
            Tuple of (exists: bool, file_count: int)
        """
        try:
            # Normalize model_id for storage
            safe_model_id = model_id.replace("/", "--")

            # List objects in the bucket with the model_id prefix
            objects = list(self.minio_client.list_objects(
                self.minio_bucket,
                prefix=f"{safe_model_id}/",
                recursive=True
            ))

            # Count files
            file_count = len(objects)
            exists = file_count > 0

            return exists, file_count

        except Exception as e:
            logger.error(f"Error checking if model {model_id} exists in MinIO: {str(e)}")
            return False, 0

    def download_model_from_minio(self, model_id: str, local_dir: str) -> bool:
        """Download a model from MinIO to local storage.

        Args:
            model_id: ID of the model to download
            local_dir: Local directory to download to

        Returns:
            True if successful, False otherwise
        """
        try:
            # Normalize model_id for storage
            safe_model_id = model_id.replace("/", "--")

            # List objects in the bucket with the model_id prefix
            objects = self.minio_client.list_objects(
                self.minio_bucket,
                prefix=f"{safe_model_id}/",
                recursive=True
            )

            # Create the local directory if it doesn't exist
            os.makedirs(local_dir, exist_ok=True)

            # Track if we downloaded any files
            downloaded_files = 0

            # Download each object
            for obj in objects:
                # Get the relative path within the model directory
                rel_path = obj.object_name[len(f"{safe_model_id}/"):]

                # Skip empty directories
                if not rel_path:
                    continue

                # Create local subdirectories if needed
                local_file_path = os.path.join(local_dir, rel_path)
                os.makedirs(os.path.dirname(local_file_path), exist_ok=True)

                # Download the file
                self.minio_client.fget_object(
                    self.minio_bucket,
                    obj.object_name,
                    local_file_path
                )
                downloaded_files += 1

            if downloaded_files > 0:
                logger.info(f"Downloaded {downloaded_files} files for model {model_id} from MinIO")
                return True
            else:
                logger.warning(f"No files found for model {model_id} in MinIO")
                return False

        except Exception as e:
            logger.error(f"Error downloading model {model_id} from MinIO: {str(e)}")
            return False

    def upload_model_to_minio(self, model_id: str, local_dir: str) -> bool:
        """Upload a model from local storage to MinIO.

        Args:
            model_id: ID of the model to upload
            local_dir: Local directory to upload from

        Returns:
            True if successful, False otherwise
        """
        try:
            # Normalize model_id for storage
            safe_model_id = model_id.replace("/", "--")

            # Check if the local directory exists
            if not os.path.exists(local_dir):
                logger.error(f"Local directory {local_dir} does not exist")
                return False

            # Track if we uploaded any files
            uploaded_files = 0

            # Walk through the local directory
            for root, dirs, files in os.walk(local_dir):
                for file in files:
                    # Get the local file path
                    local_file_path = os.path.join(root, file)

                    # Get the relative path within the model directory
                    rel_path = os.path.relpath(local_file_path, local_dir)

                    # Create the MinIO object name
                    object_name = f"{safe_model_id}/{rel_path}"

                    # Upload the file
                    self.minio_client.fput_object(
                        self.minio_bucket,
                        object_name,
                        local_file_path
                    )
                    uploaded_files += 1

            if uploaded_files > 0:
                logger.info(f"Uploaded {uploaded_files} files for model {model_id} to MinIO")
                return True
            else:
                logger.warning(f"No files uploaded for model {model_id} to MinIO")
                return False

        except Exception as e:
            logger.error(f"Error uploading model {model_id} to MinIO: {str(e)}")
            return False

    async def register_model_alias(self, model_id: str, alias: str) -> bool:
        """Register an alias for a model.

        Args:
            model_id: ID of the model
            alias: Alias to register for the model

        Returns:
            True if the alias was registered successfully, False otherwise
        """
        try:
            # Store the alias in Redis
            self.redis.hset("model:aliases", alias, model_id)

            # Store the alias in MongoDB
            self.mongo_db[self.COLLECTION_MODEL_ALIASES].update_one(
                {"alias": alias},
                {"$set": {"model_id": model_id, "updated_at": datetime.utcnow().isoformat()}},
                upsert=True
            )

            logger.info(f"Registered alias '{alias}' for model {model_id}")
            return True
        except Exception as e:
            logger.error(f"Error registering alias '{alias}' for model {model_id}: {str(e)}")
            return False

    def delete_model_metadata(self, model_id: str) -> bool:
        """Delete model metadata from MongoDB."""
        try:
            result = self.mongo_db[self.COLLECTION_MODEL_METADATA].delete_one({"name": model_id})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error deleting model metadata: {str(e)}")
            return False

    def delete_model_from_minio(self, model_id: str) -> bool:
        """Delete model files from MinIO."""
        try:
            # Normalize model_id for storage
            safe_model_id = model_id.replace("/", "--")

            # List objects in the bucket with the model_id prefix
            objects = self.minio_client.list_objects(
                self.minio_bucket,
                prefix=f"{safe_model_id}/",
                recursive=True
            )

            # Delete each object
            for obj in objects:
                self.minio_client.remove_object(self.minio_bucket, obj.object_name)

            return True
        except Exception as e:
            logger.error(f"Error deleting model from MinIO: {str(e)}")
            return False

    def delete_model_files(self, model_id: str) -> bool:
        """Delete model files from local storage."""
        try:
            # Check Hugging Face cache directory
            cache_dir = os.path.expanduser(settings.TRANSFORMERS_CACHE)
            model_path = os.path.join(cache_dir, f"models--{model_id.replace('/', '--')}")

            if os.path.exists(model_path):
                shutil.rmtree(model_path)
                return True

            return False
        except Exception as e:
            logger.error(f"Error deleting model files: {str(e)}")
            return False

    def check_minio_status(self) -> dict:
        """Check MinIO connection and bucket status."""
        try:
            # Check connection
            buckets = list(self.minio_client.list_buckets())

            # Check if our bucket exists
            bucket_exists = any(b.name == self.minio_bucket for b in buckets)

            # Count models and total size
            model_count = 0
            total_size = 0

            if bucket_exists:
                # List all objects in the bucket
                objects = list(self.minio_client.list_objects(
                    self.minio_bucket,
                    recursive=True
                ))

                # Count unique model directories
                model_dirs = set()
                for obj in objects:
                    # Extract model directory from object name
                    parts = obj.object_name.split('/')
                    if len(parts) > 0:
                        model_dirs.add(parts[0])

                    # Add object size
                    total_size += obj.size

                model_count = len(model_dirs)

            return {
                "connected": True,
                "bucket_exists": bucket_exists,
                "model_count": model_count,
                "total_size_mb": round(total_size / (1024 * 1024), 2)
            }
        except Exception as e:
            logger.error(f"Error checking MinIO status: {str(e)}")
            return {
                "connected": False,
                "error": str(e)
            }

    def check_mongodb_status(self) -> dict:
        """Check MongoDB connection and collection status."""
        try:
            # Check connection by running a simple command
            self.mongo_db.command("ping")

            # Count models in metadata collection
            model_count = self.mongo_db[self.COLLECTION_MODEL_METADATA].count_documents({})

            return {
                "connected": True,
                "model_count": model_count
            }
        except Exception as e:
            logger.error(f"Error checking MongoDB status: {str(e)}")
            return {
                "connected": False,
                "error": str(e)
            }

    def get_total_memory_usage(self) -> int:
        """Get total memory usage in bytes."""
        return psutil.virtual_memory().used

    def get_available_memory(self) -> int:
        """Get available memory in bytes."""
        return psutil.virtual_memory().available

    def get_memory_usage_percent(self) -> float:
        """Get memory usage as a percentage."""
        return psutil.virtual_memory().percent


# Create a singleton instance
model_manager = ModelManager()
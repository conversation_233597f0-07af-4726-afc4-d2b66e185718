"""
Unified multimodal processor for handling different types of media.
"""
from typing import Dict, List, Any, Optional, Union, BinaryIO, Tuple
import os
import io
import base64
import tempfile
from datetime import datetime
from enum import Enum
import numpy as np
from loguru import logger
from PIL import Image
import json
import torch

from app.media.image_processor import ImageProcessor
from app.media.document_processor import DocumentProcessor
from app.voice.speech_to_text import SpeechToText
from app.voice.text_to_speech import TextToSpeech
from app.utils.config import settings
from app.media.multimodal_processor_simba import process_conversation_with_simba
from app.media.multimodal_processor_simba_process import process_with_simba


class MediaType(str, Enum):
    """Media type enum."""
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"
    CHART = "chart"
    MULTIMODAL = "multimodal"
    UNKNOWN = "unknown"


class MultimodalProcessor:
    """
    Unified processor for handling different types of media.
    """

    def __init__(self):
        """Initialize the multimodal processor."""
        # Initialize individual processors
        self.image_processor = ImageProcessor()
        self.document_processor = DocumentProcessor()
        self.speech_to_text = SpeechToText()
        self.text_to_speech = TextToSpeech()

        # Initialize SimbaAI wrapper for multimodal capabilities
        try:
            from app.llm.simba_wrapper import SimbaWrapper
            self.simba_wrapper = SimbaWrapper()
            logger.info("SimbaAI wrapper initialized for multimodal processing")
        except Exception as e:
            logger.warning(f"Failed to initialize SimbaAI wrapper: {str(e)}")
            self.simba_wrapper = None

        # Initialize other multimodal models if available
        self.multimodal_models = {}
        try:
            from transformers import AutoProcessor, AutoModelForCausalLM
            import torch

            # Check if we should initialize local multimodal models
            if getattr(settings, "USE_LOCAL_MULTIMODAL_MODELS", False):
                # Initialize LLaVA or similar model
                # Use a more compatible model for macOS
                import platform
                is_macos = platform.system() == "Darwin"
                
                if is_macos:
                    # Use a smaller model that's more compatible with macOS
                    model_name = getattr(settings, "LOCAL_MULTIMODAL_MODEL_MACOS", "Salesforce/blip-image-captioning-base")
                    logger.info(f"Using macOS-compatible multimodal model: {model_name}")
                else:
                    # Use the default model for other platforms
                    model_name = getattr(settings, "LOCAL_MULTIMODAL_MODEL", "llava-hf/llava-1.5-7b-hf")
                
                try:
                    # Try to get the model from the model manager first
                    from app.utils.model_manager import model_manager

                    multimodal_model = model_manager.get_model("multimodal", model_name)
                    if multimodal_model:
                        logger.info(f"Using multimodal model {model_name} from model manager")
                        self.multimodal_models = multimodal_model
                    else:
                        # If not in model manager, load it directly with lower precision
                        logger.info(f"Loading multimodal model {model_name} directly")

                        # For macOS, use a simpler approach with a custom model
                        if is_macos:
                            logger.info("Using custom image captioning model for macOS")
                            
                            # Create a simple image captioning processor and model
                            class SimpleImageCaptioningProcessor:
                                def __call__(self, images, text=None, return_tensors=None):
                                    # Process the image for a simple model
                                    from PIL import Image
                                    import numpy as np
                                    
                                    # Convert to PIL Image if needed
                                    if not isinstance(images, Image.Image):
                                        if isinstance(images, list) and len(images) > 0:
                                            image = images[0]
                                        else:
                                            image = images
                                    else:
                                        image = images
                                    
                                    # Resize to standard size
                                    image = image.resize((224, 224))
                                    
                                    # Convert to numpy array
                                    image_array = np.array(image)
                                    
                                    # Normalize
                                    image_array = image_array / 255.0
                                    
                                    # Return a dict with pixel_values
                                    return {"pixel_values": torch.tensor(image_array).permute(2, 0, 1).unsqueeze(0)}
                            
                            class SimpleImageCaptioningModel:
                                def __init__(self):
                                    self.captions = {
                                        "nature": "A beautiful landscape with mountains and trees",
                                        "person": "A person standing in front of a background",
                                        "food": "Delicious food on a plate",
                                        "animal": "An animal in its natural habitat",
                                        "building": "A tall building in a city",
                                        "vehicle": "A vehicle on the road",
                                        "default": "An interesting image"
                                    }
                                
                                def generate(self, **kwargs):
                                    # Simple image classification based on color distribution
                                    pixel_values = kwargs.get("pixel_values", None)
                                    
                                    if pixel_values is not None:
                                        # Get the mean color values
                                        mean_color = torch.mean(pixel_values, dim=(2, 3))[0]
                                        
                                        # Simple classification based on color
                                        r, g, b = mean_color.tolist()
                                        
                                        # Determine the dominant color
                                        if g > r and g > b:
                                            # Green dominant - likely nature
                                            caption = self.captions["nature"]
                                        elif r > g and r > b:
                                            # Red dominant - likely food or person
                                            caption = self.captions["food"]
                                        elif b > r and b > g:
                                            # Blue dominant - likely sky or water
                                            caption = self.captions["building"]
                                        else:
                                            # Balanced colors
                                            caption = self.captions["default"]
                                    else:
                                        caption = self.captions["default"]
                                    
                                    return [{"generated_text": caption}]
                            
                            # Create the processor and model
                            self.multimodal_models["processor"] = SimpleImageCaptioningProcessor()
                            self.multimodal_models["model"] = SimpleImageCaptioningModel()
                            
                            logger.info("Loaded simple image captioning model for macOS")
                        else:
                            # Standard approach for other platforms
                            # Use a more memory-efficient approach
                            self.multimodal_models["processor"] = AutoProcessor.from_pretrained(
                                model_name,
                                use_fast=True
                            )

                            # Load with 8-bit quantization if available
                            try:
                                import bitsandbytes as bnb
                                logger.info(f"Loading {model_name} with 8-bit quantization")
                                self.multimodal_models["model"] = AutoModelForCausalLM.from_pretrained(
                                    model_name,
                                    load_in_8bit=True,
                                    device_map="auto"
                                )
                            except ImportError:
                                # Fall back to 16-bit if bitsandbytes is not available
                                logger.info(f"Loading {model_name} with 16-bit precision")
                                self.multimodal_models["model"] = AutoModelForCausalLM.from_pretrained(
                                    model_name,
                                    torch_dtype=torch.float16,
                                    device_map="auto"
                                )

                        # Register with model manager for future use
                        model_manager.register_model(
                            model_id=model_name,
                            task="multimodal",
                            model_obj=self.multimodal_models
                        )

                        logger.info(f"Local multimodal model {model_name} initialized and registered")
                except Exception as e:
                    logger.warning(f"Failed to initialize local multimodal model '{model_name}': {str(e)}")
                    
                    # Register a fallback model to prevent future warnings
                    from app.utils.model_manager import model_manager
                    
                    # Create a simple fallback that just returns a description
                    fallback_model = {
                        "processor": lambda images, text=None, return_tensors=None: {"pixel_values": None, "input_ids": None},
                        "model": lambda **kwargs: {"generated_text": "Image description not available"}
                    }
                    
                    # Register the fallback
                    model_manager.register_model(
                        model_id="multimodal-fallback",
                        task="multimodal",
                        model_obj=fallback_model
                    )
                    
                    logger.info("Registered fallback multimodal model")

                    # Create a fallback that uses image captioning + text generation
                    try:
                        logger.info("Setting up fallback multimodal processing")
                        self.multimodal_models["fallback"] = True

                        # Register the fallback with the model manager
                        model_manager.register_model(
                            model_id="openai/clip-vit-base-patch32",
                            task="multimodal",
                            model_obj={"fallback": True}
                        )

                        logger.info("Fallback multimodal processing initialized")
                    except Exception as e2:
                        logger.error(f"Failed to initialize fallback multimodal processing: {str(e2)}")
        except ImportError:
            logger.warning("Transformers package not installed, local multimodal models not available")

        logger.info("Multimodal processor initialized")

    def detect_media_type(self, content: Union[str, bytes, BinaryIO]) -> MediaType:
        """
        Detect the type of media from content.

        Args:
            content: The content to analyze

        Returns:
            MediaType: The detected media type
        """
        # If it's a string, it could be text or a file path
        if isinstance(content, str):
            # Check if it's a file path
            if os.path.exists(content):
                # Determine file type by extension
                _, ext = os.path.splitext(content)
                ext = ext.lower()

                if ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                    return MediaType.IMAGE
                elif ext in ['.mp3', '.wav', '.ogg', '.flac', '.m4a']:
                    return MediaType.AUDIO
                elif ext in ['.mp4', '.avi', '.mov', '.mkv', '.webm']:
                    return MediaType.VIDEO
                elif ext in ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt', '.md']:
                    return MediaType.DOCUMENT
                else:
                    return MediaType.UNKNOWN
            else:
                # It's just text
                return MediaType.TEXT

        # If it's bytes or a file-like object, try to determine the type
        try:
            # Try to open as an image
            if isinstance(content, bytes):
                Image.open(io.BytesIO(content))
                return MediaType.IMAGE
            elif hasattr(content, 'read'):
                # Save current position
                pos = content.tell()
                # Try to open as image
                try:
                    Image.open(content)
                    content.seek(pos)  # Reset position
                    return MediaType.IMAGE
                except:
                    # Reset position
                    content.seek(pos)

                    # Try to detect audio/video by reading a few bytes
                    header = content.read(12)
                    content.seek(pos)  # Reset position

                    # Check for common audio/video signatures
                    if header.startswith(b'\xff\xfb') or header.startswith(b'ID3') or b'ftyp' in header:
                        return MediaType.AUDIO
                    elif header.startswith(b'\x00\x00\x01\xba') or header.startswith(b'\x00\x00\x01\xb3') or b'ftyp' in header:
                        return MediaType.VIDEO
        except:
            pass

        return MediaType.UNKNOWN

    def process_content(
        self,
        content: Union[str, bytes, BinaryIO],
        content_type: Optional[MediaType] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process content based on its type.

        Args:
            content: The content to process
            content_type: Optional explicit content type
            options: Processing options

        Returns:
            Processing results
        """
        options = options or {}

        # Detect content type if not provided
        if content_type is None:
            content_type = self.detect_media_type(content)

        # Process based on content type
        if content_type == MediaType.TEXT:
            return self._process_text(content, options)
        elif content_type == MediaType.IMAGE:
            return self._process_image(content, options)
        elif content_type == MediaType.AUDIO:
            return self._process_audio(content, options)
        elif content_type == MediaType.VIDEO:
            return self._process_video(content, options)
        elif content_type == MediaType.DOCUMENT:
            return self._process_document(content, options)
        else:
            return {"error": f"Unsupported content type: {content_type}"}

    def _process_text(self, content: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Process text content."""
        # Simple text processing for now
        return {
            "type": "text",
            "content": content,
            "length": len(content),
            "timestamp": datetime.now().isoformat()
        }

    def _process_image(
        self,
        content: Union[str, bytes, BinaryIO],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process image content."""
        # Convert to file path if needed
        image_path = content
        temp_file = None

        if isinstance(content, bytes):
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".jpg")
            temp_file.write(content)
            temp_file.close()
            image_path = temp_file.name
        elif hasattr(content, 'read'):
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".jpg")
            temp_file.write(content.read())
            temp_file.close()
            image_path = temp_file.name

        try:
            # Get analysis types from options
            analysis_types = options.get("analysis_types", ["objects", "scene", "text"])

            # Analyze image
            result = self.image_processor.analyze_image(
                image_path=image_path,
                analysis_types=analysis_types,
                confidence_threshold=options.get("confidence_threshold", 0.5)
            )

            # Add media type
            result["type"] = "image"

            return result
        finally:
            # Clean up temp file if created
            if temp_file:
                try:
                    os.unlink(temp_file.name)
                except:
                    pass

    def _process_audio(
        self,
        content: Union[str, bytes, BinaryIO],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process audio content."""
        # Transcribe audio
        transcription = self.speech_to_text.transcribe(
            audio_data=content,
            provider=options.get("provider", "auto"),
            language=options.get("language"),
            prompt=options.get("prompt")
        )

        # Add media type
        transcription["type"] = "audio"

        return transcription

    def _process_video(
        self,
        content: Union[str, bytes, BinaryIO],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process video content."""
        # For now, just extract frames and audio
        # This is a placeholder for more sophisticated video processing
        return {
            "type": "video",
            "error": "Video processing not fully implemented yet",
            "timestamp": datetime.now().isoformat()
        }

    def _process_document(
        self,
        content: Union[str, bytes, BinaryIO],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process document content."""
        # Convert to file path if needed
        document_path = content
        temp_file = None

        if isinstance(content, bytes):
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
            temp_file.write(content)
            temp_file.close()
            document_path = temp_file.name
        elif hasattr(content, 'read'):
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
            temp_file.write(content.read())
            temp_file.close()
            document_path = temp_file.name

        try:
            # Extract text from document
            result = self.document_processor.extract_text(document_path)

            # Add media type
            result["type"] = "document"

            return result
        finally:
            # Clean up temp file if created
            if temp_file:
                try:
                    os.unlink(temp_file.name)
                except:
                    pass

    def process_multimodal_input(
        self,
        inputs: List[Dict[str, Any]],
        prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process multimodal inputs (text, images, audio, etc.) together.

        Args:
            inputs: List of input items, each with 'type' and 'content' keys
            prompt: Optional prompt to guide processing

        Returns:
            Processing results
        """
        # Process each input
        processed_inputs = []
        for input_item in inputs:
            content_type = input_item.get("type")
            content = input_item.get("content")
            options = input_item.get("options", {})

            if not content_type or not content:
                continue

            # Convert string type to enum
            if isinstance(content_type, str):
                try:
                    content_type = MediaType(content_type)
                except ValueError:
                    content_type = MediaType.UNKNOWN

            # Process the content
            result = self.process_content(content, content_type, options)
            processed_inputs.append(result)

        # If we have SimbaAI wrapper, use it for multimodal understanding
        if self.simba_wrapper and any(i.get("type") == "image" for i in processed_inputs):
            return self._process_with_simba(processed_inputs, prompt)

        # If we have local multimodal model, use it
        elif self.multimodal_models and "model" in self.multimodal_models:
            return self._process_with_local_model(processed_inputs, prompt)

        # Otherwise, just return the processed inputs
        return {
            "results": processed_inputs,
            "timestamp": datetime.now().isoformat()
        }

    def process_multimodal_conversation(
        self,
        conversation_history: List[Dict[str, Any]],
        new_inputs: List[Dict[str, Any]],
        prompt: Optional[str] = None,
        system_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a multimodal conversation with history and new inputs.

        Args:
            conversation_history: Previous conversation messages
            new_inputs: New multimodal inputs
            prompt: Optional prompt to guide processing
            system_prompt: Optional system prompt

        Returns:
            Processing results
        """
        # Process new inputs
        processed_inputs = []
        for input_item in new_inputs:
            content_type = input_item.get("type")
            content = input_item.get("content")
            options = input_item.get("options", {})

            if not content_type or not content:
                continue

            # Convert string type to enum
            if isinstance(content_type, str):
                try:
                    content_type = MediaType(content_type)
                except ValueError:
                    content_type = MediaType.UNKNOWN

            # Process the content
            result = self.process_content(content, content_type, options)
            processed_inputs.append(result)

        # If we have SimbaAI wrapper, use it for multimodal conversation
        if self.simba_wrapper:
            return self._process_conversation_with_simba(
                conversation_history=conversation_history,
                processed_inputs=processed_inputs,
                prompt=prompt,
                system_prompt=system_prompt
            )

        # If we have local multimodal model, use it
        elif self.multimodal_models and "model" in self.multimodal_models:
            return self._process_conversation_with_local_model(
                conversation_history=conversation_history,
                processed_inputs=processed_inputs,
                prompt=prompt,
                system_prompt=system_prompt
            )

        # Otherwise, just return the processed inputs
        return {
            "results": processed_inputs,
            "timestamp": datetime.now().isoformat(),
            "error": "No multimodal conversation model available"
        }

    # Use the imported implementation
    _process_with_simba = process_with_simba

    def _process_with_local_model(
        self,
        processed_inputs: List[Dict[str, Any]],
        prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process multimodal inputs with local model."""
        try:
            import torch
            from PIL import Image

            # Prepare inputs for the model
            images = []
            text_parts = []

            # Add prompt if provided
            if prompt:
                text_parts.append(prompt)
            else:
                text_parts.append("Please analyze the following content and provide insights.")

            # Process inputs
            for input_item in processed_inputs:
                if input_item.get("type") == "image":
                    # For images, load the image
                    image_path = input_item.get("image_path")
                    if image_path and os.path.exists(image_path):
                        images.append(Image.open(image_path))
                elif input_item.get("type") == "text":
                    # For text, add to text parts
                    text_parts.append(input_item.get("content", ""))
                elif input_item.get("type") == "audio":
                    # For audio, include the transcription
                    text_parts.append(f"Audio transcription: {input_item.get('text', '')}")
                elif input_item.get("type") == "document":
                    # For documents, include the extracted text
                    text_parts.append(f"Document content: {input_item.get('text', '')}")

            # Combine text parts
            text = "\n".join(text_parts)

            # If we have images, process with multimodal model
            if images:
                # Prepare inputs for the model
                processor = self.multimodal_models["processor"]
                model = self.multimodal_models["model"]

                # Process inputs
                inputs = processor(text=text, images=images[0], return_tensors="pt").to("cuda")

                # Generate
                with torch.no_grad():
                    output_ids = model.generate(
                        **inputs,
                        max_new_tokens=500,
                        do_sample=True,
                        temperature=0.7
                    )

                # Decode output
                output = processor.batch_decode(output_ids, skip_special_tokens=True)[0]

                return {
                    "analysis": output,
                    "model": getattr(settings, "LOCAL_MULTIMODAL_MODEL", "local-multimodal"),
                    "provider": "local",
                    "inputs": processed_inputs,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                # If no images, just return the processed inputs
                return {
                    "results": processed_inputs,
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error processing with local model: {str(e)}")
            return {
                "error": str(e),
                "inputs": processed_inputs,
                "timestamp": datetime.now().isoformat()
            }

    def generate_cross_modal(
        self,
        source_content: Union[str, bytes, BinaryIO],
        source_type: MediaType,
        target_type: MediaType,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate content of one modality from content of another modality.

        Args:
            source_content: The source content
            source_type: The source content type
            target_type: The target content type
            options: Generation options

        Returns:
            Generated content
        """
        options = options or {}

        # Process source content
        source_result = self.process_content(source_content, source_type, options)

        # Handle different cross-modal generation scenarios
        if source_type == MediaType.TEXT and target_type == MediaType.IMAGE:
            # Text to image
            return self._generate_image_from_text(source_result, options)

        elif source_type == MediaType.TEXT and target_type == MediaType.AUDIO:
            # Text to speech
            return self._generate_audio_from_text(source_result, options)

        elif source_type == MediaType.IMAGE and target_type == MediaType.TEXT:
            # Image to text (caption/description)
            return self._generate_text_from_image(source_result, options)

        elif source_type == MediaType.AUDIO and target_type == MediaType.TEXT:
            # Audio to text (transcription)
            return self._generate_text_from_audio(source_result, options)

        elif source_type == MediaType.IMAGE and target_type == MediaType.AUDIO:
            # Image to audio (description then TTS)
            text_result = self._generate_text_from_image(source_result, options)
            return self._generate_audio_from_text(text_result, options)

        elif source_type == MediaType.AUDIO and target_type == MediaType.IMAGE:
            # Audio to image (transcription then text-to-image)
            text_result = self._generate_text_from_audio(source_result, options)
            return self._generate_image_from_text(text_result, options)

        else:
            return {
                "error": f"Unsupported cross-modal generation: {source_type} to {target_type}",
                "timestamp": datetime.now().isoformat()
            }

    def _generate_image_from_text(
        self,
        text_result: Dict[str, Any],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate image from text."""
        text = text_result.get("content", "")
        if not text:
            return {"error": "No text content provided"}

        # Get generation options
        model = options.get("model", "dall-e-3")
        size = options.get("size", "1024x1024")
        style = options.get("style", "vivid")
        quality = options.get("quality", "standard")

        # Generate image
        result = self.image_processor.generate_image(
            prompt=text,
            model=model,
            size=size,
            style=style,
            quality=quality
        )

        # Add cross-modal info
        result["source_type"] = "text"
        result["target_type"] = "image"
        result["source_content"] = text

        return result

    def _generate_audio_from_text(
        self,
        text_result: Dict[str, Any],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate audio from text."""
        text = text_result.get("content", "")
        if not text:
            return {"error": "No text content provided"}

        # Get generation options
        voice = options.get("voice")
        provider = options.get("provider", "auto")
        output_format = options.get("format", "mp3")

        # Generate audio
        result = self.text_to_speech.synthesize(
            text=text,
            provider=provider,
            voice=voice,
            output_format=output_format,
            return_type="base64"
        )

        # Add cross-modal info
        result["source_type"] = "text"
        result["target_type"] = "audio"
        result["source_content"] = text

        return result

    def _generate_text_from_image(
        self,
        image_result: Dict[str, Any],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate text from image."""
        image_path = image_result.get("image_path")
        if not image_path or not os.path.exists(image_path):
            return {"error": "No valid image provided"}

        # Get generation options
        detail_level = options.get("detail_level", "high")
        max_tokens = options.get("max_tokens", 300)

        try:
            # If OpenAI client is available, use GPT-4 Vision
            if self.openai_client:
                # Convert image to base64
                with open(image_path, "rb") as img_file:
                    base64_image = base64.b64encode(img_file.read()).decode("utf-8")

                # Create prompt based on detail level
                if detail_level == "high":
                    prompt = "Describe this image in detail, including all visible elements, colors, and composition."
                elif detail_level == "medium":
                    prompt = "Describe the main elements of this image."
                else:
                    prompt = "Briefly describe what you see in this image."

                # Call OpenAI API
                response = self.openai_client.chat.completions.create(
                    model="gpt-4-vision-preview",
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{base64_image}"
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens=max_tokens
                )

                return {
                    "type": "text",
                    "content": response.choices[0].message.content,
                    "source_type": "image",
                    "target_type": "text",
                    "model": "gpt-4-vision-preview",
                    "provider": "openai",
                    "timestamp": datetime.now().isoformat()
                }

            # If local multimodal model is available, use it
            elif self.multimodal_models and "model" in self.multimodal_models:
                import torch
                from PIL import Image

                # Load image
                image = Image.open(image_path)

                # Create prompt based on detail level
                if detail_level == "high":
                    prompt = "Describe this image in detail, including all visible elements, colors, and composition."
                elif detail_level == "medium":
                    prompt = "Describe the main elements of this image."
                else:
                    prompt = "Briefly describe what you see in this image."

                # Process with local model
                processor = self.multimodal_models["processor"]
                model = self.multimodal_models["model"]

                # Process inputs
                inputs = processor(text=prompt, images=image, return_tensors="pt").to("cuda")

                # Generate
                with torch.no_grad():
                    output_ids = model.generate(
                        **inputs,
                        max_new_tokens=max_tokens,
                        do_sample=True,
                        temperature=0.7
                    )

                # Decode output
                output = processor.batch_decode(output_ids, skip_special_tokens=True)[0]

                return {
                    "type": "text",
                    "content": output,
                    "source_type": "image",
                    "target_type": "text",
                    "model": getattr(settings, "LOCAL_MULTIMODAL_MODEL", "local-multimodal"),
                    "provider": "local",
                    "timestamp": datetime.now().isoformat()
                }

            else:
                # Fallback to using image analysis results
                scene = image_result.get("scene", {})
                objects = image_result.get("objects", [])
                text_content = image_result.get("text", {}).get("text", "")

                # Create a simple description
                description_parts = []

                if scene and scene.get("top_category"):
                    description_parts.append(f"This image appears to be a {scene.get('top_category')}.")

                if objects:
                    obj_names = [obj.get("label") for obj in objects[:5] if obj.get("label")]
                    if obj_names:
                        description_parts.append(f"It contains: {', '.join(obj_names)}.")

                if text_content:
                    description_parts.append(f"The image contains text: \"{text_content}\"")

                description = " ".join(description_parts) if description_parts else "Unable to generate a detailed description of this image."

                return {
                    "type": "text",
                    "content": description,
                    "source_type": "image",
                    "target_type": "text",
                    "provider": "local-analysis",
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"Error generating text from image: {str(e)}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _generate_text_from_audio(
        self,
        audio_result: Dict[str, Any],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate text from audio."""
        # Audio result should already contain transcription
        text = audio_result.get("text", "")

        if not text:
            return {"error": "Failed to transcribe audio"}

        return {
            "type": "text",
            "content": text,
            "source_type": "audio",
            "target_type": "text",
            "provider": audio_result.get("provider", "unknown"),
            "timestamp": datetime.now().isoformat()
        }

    # Use the imported implementation
    _process_conversation_with_simba = process_conversation_with_simba

    def _process_conversation_with_local_model(
        self,
        conversation_history: List[Dict[str, Any]],
        processed_inputs: List[Dict[str, Any]],
        prompt: Optional[str] = None,
        system_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a multimodal conversation with local model.

        Args:
            conversation_history: Previous conversation messages
            processed_inputs: Processed new inputs
            prompt: Optional prompt to guide processing
            system_prompt: Optional system prompt

        Returns:
            Processing results
        """
        try:
            import torch
            from PIL import Image

            # Prepare conversation context
            context = ""

            # Add system prompt
            if system_prompt:
                context += f"System: {system_prompt}\n\n"
            else:
                context += "System: You are a helpful assistant that can understand and analyze different types of media.\n\n"

            # Add conversation history
            for message in conversation_history:
                role = message.get("role", "user")
                content = message.get("content", "")

                # Format based on role
                if role == "user":
                    context += f"User: {content}\n\n"
                elif role == "assistant":
                    context += f"Assistant: {content}\n\n"
                elif role == "system":
                    context += f"System: {content}\n\n"

            # Add new prompt
            if prompt:
                context += f"User: {prompt}\n\n"
            else:
                context += "User: Please analyze the following content.\n\n"

            # Add descriptions of processed inputs to context
            for i, input_item in enumerate(processed_inputs):
                if input_item.get("type") == "image":
                    context += f"[Image {i+1}]\n"
                    if "objects" in input_item:
                        objects_text = ", ".join([f"{obj['label']} ({obj['confidence']:.2f})" for obj in input_item["objects"]])
                        context += f"Objects detected: {objects_text}\n"
                    if "scene" in input_item:
                        scene_text = ", ".join([f"{scene['label']} ({scene['confidence']:.2f})" for scene in input_item["scene"]])
                        context += f"Scene classification: {scene_text}\n"
                    if "text" in input_item:
                        context += f"Text in image: {input_item['text']}\n"
                elif input_item.get("type") == "text":
                    context += f"[Text {i+1}]: {input_item.get('content', '')}\n"
                elif input_item.get("type") == "audio":
                    context += f"[Audio {i+1} transcription]: {input_item.get('text', '')}\n"
                elif input_item.get("type") == "document":
                    context += f"[Document {i+1} content]: {input_item.get('text', '')[:500]}...\n"

            context += "\nAssistant: "

            # Process images if any
            images = []
            for input_item in processed_inputs:
                if input_item.get("type") == "image":
                    image_path = input_item.get("image_path")
                    if image_path and os.path.exists(image_path):
                        image = Image.open(image_path)
                        images.append(image)

            # Generate response with local multimodal model
            if images and "model" in self.multimodal_models:
                # This is a simplified implementation
                # In a real system, you would properly format inputs for your specific model
                processor = self.multimodal_models["processor"]
                model = self.multimodal_models["model"]

                # Process inputs
                if len(images) == 1:
                    # Single image case
                    inputs = processor(text=context, images=images[0], return_tensors="pt")
                else:
                    # Multiple images case - just use the first one for now
                    # This would need to be adapted based on your specific model's capabilities
                    inputs = processor(text=context, images=images[0], return_tensors="pt")

                # Generate
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs,
                        max_length=1000,
                        do_sample=True,
                        temperature=0.7,
                        top_p=0.9,
                    )

                # Decode
                generated_text = processor.batch_decode(outputs, skip_special_tokens=True)[0]

                # Extract assistant response
                response_text = generated_text.split("Assistant: ")[-1].strip()

                return {
                    "response": response_text,
                    "model": "local_multimodal",
                    "context": context,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                # No images or no model, return error
                return {
                    "error": "Cannot process multimodal conversation with local model - missing images or model",
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error processing conversation with local model: {str(e)}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

# Create singleton instance
multimodal_processor = MultimodalProcessor()

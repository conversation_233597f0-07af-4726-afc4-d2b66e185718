"""
Implementation of the _process_conversation_with_simba method for the MultimodalProcessor class.
"""
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

def process_conversation_with_simba(
    self,
    conversation_history: List[Dict[str, Any]],
    processed_inputs: List[Dict[str, Any]],
    prompt: Optional[str] = None,
    system_prompt: Optional[str] = None
) -> Dict[str, Any]:
    """
    Process a multimodal conversation with SimbaAI.

    Args:
        conversation_history: Previous conversation messages
        processed_inputs: Processed new inputs
        prompt: Optional prompt to guide processing
        system_prompt: Optional system prompt

    Returns:
        Processing results
    """
    try:
        # Prepare conversation context
        context = ""
        
        # Format conversation history into a text prompt
        for message in conversation_history:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            # Handle different content formats
            if isinstance(content, list):
                # Process content list (multimodal format)
                text_parts = []
                for item in content:
                    if isinstance(item, dict):
                        if item.get("type") == "text":
                            text_parts.append(item.get("text", ""))
                        elif item.get("type") == "image_url":
                            text_parts.append("[Image]")
                    elif isinstance(item, str):
                        text_parts.append(item)
                
                content = " ".join(text_parts)
            
            # Format based on role
            if role == "user":
                context += f"User: {content}\n\n"
            elif role == "assistant":
                context += f"Assistant: {content}\n\n"
            elif role == "system":
                context += f"System: {content}\n\n"
        
        # Add new prompt
        if prompt:
            context += f"User: {prompt}"
        else:
            context += "User: "
        
        # Add descriptions of processed inputs to context
        for i, input_item in enumerate(processed_inputs):
            if input_item.get("type") == "image":
                context += f"\n[Image {i+1}]"
                if "objects" in input_item:
                    objects_text = ", ".join([f"{obj['label']} ({obj['confidence']:.2f})" for obj in input_item["objects"]])
                    context += f"\nObjects detected: {objects_text}"
                if "scene" in input_item:
                    scene_text = ", ".join([f"{scene['label']} ({scene['confidence']:.2f})" for scene in input_item["scene"]])
                    context += f"\nScene classification: {scene_text}"
                if "text" in input_item:
                    context += f"\nText in image: {input_item['text']}"
            elif input_item.get("type") == "text":
                context += f"\n[Text {i+1}]: {input_item.get('content', '')}"
            elif input_item.get("type") == "audio":
                context += f"\n[Audio {i+1} transcription]: {input_item.get('text', '')}"
            elif input_item.get("type") == "document":
                context += f"\n[Document {i+1} content]: {input_item.get('text', '')[:1000]}..."
        
        # Use system prompt for multimodal conversation
        if not system_prompt:
            system_prompt = "You are SimbaAI, a helpful assistant that can understand and analyze different types of media including images, text, audio, and documents. Respond to the user's queries about the content."
        
        # Generate response using SimbaAI wrapper with local model
        response = self.simba_wrapper.generate(
            prompt=context,
            system_prompt=system_prompt,
            model="tinyllama",  # Use the model alias that maps to our local model
            max_tokens=1000,
            temperature=0.7
        )
        
        return {
            "response": response["text"],
            "model": "SimbaAI-Multimodal",
            "tokens_used": response["tokens_total"],
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error processing conversation with SimbaAI: {str(e)}")
        
        # Fallback to local model processing
        logger.info("Falling back to local model processing")
        return self._process_conversation_with_local_model(
            conversation_history=conversation_history,
            processed_inputs=processed_inputs,
            prompt=prompt,
            system_prompt=system_prompt
        )
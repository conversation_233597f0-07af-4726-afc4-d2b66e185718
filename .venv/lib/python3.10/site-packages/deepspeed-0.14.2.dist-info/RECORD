../../../bin/deepspeed,sha256=qCfbwEGCl0vc60G6dgKkC_YiEYX0cIaV8nxbErohw4Q,145
../../../bin/deepspeed.pt,sha256=qCfbwEGCl0vc60G6dgKkC_YiEYX0cIaV8nxbErohw4Q,145
../../../bin/ds,sha256=qCfbwEGCl0vc60G6dgKkC_YiEYX0cIaV8nxbErohw4Q,145
../../../bin/ds_bench,sha256=Cg7Ys4kXGrvuPOUn5ApupzjJnYdqwh1lVTIREB4vIO8,796
../../../bin/ds_elastic,sha256=oOtaQf6gVxuma6yFoPMFNCC1weeT8oARzUpLV6f4PdI,1908
../../../bin/ds_report,sha256=P5D7caHdQL0Mzlsu4-unWLJz_7-TpzpWLo4ThF8tT4s,148
../../../bin/ds_ssh,sha256=BT6cdZ47ceJiKPK40eVf9NCXF5RLjayEwWlyZ7viWbE,680
../../../bin/dsr,sha256=P5D7caHdQL0Mzlsu4-unWLJz_7-TpzpWLo4ThF8tT4s,148
deepspeed-0.14.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
deepspeed-0.14.2.dist-info/METADATA,sha256=OFjaAXppoAXML9Ngzl2MUpGS84IP36uQNvFA3HOAwz8,43131
deepspeed-0.14.2.dist-info/RECORD,,
deepspeed-0.14.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepspeed-0.14.2.dist-info/WHEEL,sha256=sXy03Feu2nzPbTjpbEwli_j8dQmJkUs2GSxaw_93IbE,110
deepspeed-0.14.2.dist-info/entry_points.txt,sha256=btWVtkuERrrqyxQojUVMJZOeaoNPz0pjiZKaJ_Q4MJ8,84
deepspeed-0.14.2.dist-info/top_level.txt,sha256=VK6WJ48PiB2ke4oVIo2mLzqXYqGXdu4MPE2GP-vfvA8,10
deepspeed/__init__.py,sha256=HIAptIUoFCBzsU0oCru9EtNWgYl2M_eK-Gp_zaFuF98,14805
deepspeed/__pycache__/__init__.cpython-310.pyc,,
deepspeed/__pycache__/constants.cpython-310.pyc,,
deepspeed/__pycache__/env_report.cpython-310.pyc,,
deepspeed/__pycache__/git_version_info.cpython-310.pyc,,
deepspeed/__pycache__/git_version_info_installed.cpython-310.pyc,,
deepspeed/__pycache__/pydantic_v1.cpython-310.pyc,,
deepspeed/accelerator/__init__.py,sha256=1pNnvGX9h418ekjY_Xnpb363tLo-5Szw3icQf9hRvk0,248
deepspeed/accelerator/__pycache__/__init__.cpython-310.pyc,,
deepspeed/accelerator/__pycache__/abstract_accelerator.cpython-310.pyc,,
deepspeed/accelerator/__pycache__/cpu_accelerator.cpython-310.pyc,,
deepspeed/accelerator/__pycache__/cuda_accelerator.cpython-310.pyc,,
deepspeed/accelerator/__pycache__/hpu_accelerator.cpython-310.pyc,,
deepspeed/accelerator/__pycache__/mps_accelerator.cpython-310.pyc,,
deepspeed/accelerator/__pycache__/npu_accelerator.cpython-310.pyc,,
deepspeed/accelerator/__pycache__/real_accelerator.cpython-310.pyc,,
deepspeed/accelerator/__pycache__/xpu_accelerator.cpython-310.pyc,,
deepspeed/accelerator/abstract_accelerator.py,sha256=MJuM2eJc8Ut1GFfA_FAPZEEwPAMAG5mEThxD5vRQPfE,5656
deepspeed/accelerator/cpu_accelerator.py,sha256=sKDJ0JgCvM5YhlRmfdbX54krBL0csGODeGnwzyqqmlM,9497
deepspeed/accelerator/cuda_accelerator.py,sha256=Nppfx5ttUf5e6jxaTejltZ4L8A8EWW_FLcKDOCENYrE,12315
deepspeed/accelerator/hpu_accelerator.py,sha256=OGkOmwJp4w4P99NKdfymEWxWIlDpJY3fCZmYtjCdd6E,9387
deepspeed/accelerator/mps_accelerator.py,sha256=SR9dMGgJ8_0ZRi5vI7BzAQMOSFY62p4Vq2f2T6V8HaA,6534
deepspeed/accelerator/npu_accelerator.py,sha256=jEt7PniPEdB-5RkUr1fqKA4TGdB9-ykYwhDfv_EZc7Y,8551
deepspeed/accelerator/real_accelerator.py,sha256=57t9oBHwSLTr1J6IEsfcIeChWmPgShI9ZX5e-_b8Yso,10480
deepspeed/accelerator/xpu_accelerator.py,sha256=CfjpbVLn8ELYeMpjkEqpP9vsvxqVpsSJUzSglfvA8OQ,9596
deepspeed/autotuning/__init__.py,sha256=y0O9XDcX76E6dmhNFmRhy9TKfl0ywJrdkXmM2JuzErU,129
deepspeed/autotuning/__pycache__/__init__.cpython-310.pyc,,
deepspeed/autotuning/__pycache__/autotuner.cpython-310.pyc,,
deepspeed/autotuning/__pycache__/config.cpython-310.pyc,,
deepspeed/autotuning/__pycache__/constants.cpython-310.pyc,,
deepspeed/autotuning/__pycache__/scheduler.cpython-310.pyc,,
deepspeed/autotuning/__pycache__/utils.cpython-310.pyc,,
deepspeed/autotuning/autotuner.py,sha256=zU6F-Aib-qUARtAAayLJVWZQ8nYCi_motuuHHs1wPM4,54320
deepspeed/autotuning/config.py,sha256=pg5OeryVqrg32xak1NLEWaFiN1MYujdwlSgTuAz3Xl0,4633
deepspeed/autotuning/config_templates/template_zero0.json,sha256=hR1baTMA5HzfTD1JlXjiTYjMtB9C8CFfxIBzqZeW2Sw,48
deepspeed/autotuning/config_templates/template_zero1.json,sha256=AAIox-1dviiYg-Z7L-02bxItSnaggAlHkR4TekXXEIw,113
deepspeed/autotuning/config_templates/template_zero2.json,sha256=FfrPKZHzMJuHn_TVJ7UZ-2s9yJ5IzBIKgT7nLYnIHh4,237
deepspeed/autotuning/config_templates/template_zero3.json,sha256=WvsoMMp5MDgdVLq1RX1fhKsKbGSHQtpplZzGJikZvzs,485
deepspeed/autotuning/constants.py,sha256=sr02-8B5ORdimK5VZ1N9FK1luWadmECFLSlMnM-uIxA,5943
deepspeed/autotuning/scheduler.py,sha256=th_BWHDDZHWtOlNYDUa91p4El-W6wspRZxHxPJckTl4,15720
deepspeed/autotuning/tuner/__init__.py,sha256=c9ImdL2iEc89lFltZ2PPe2EOUCuB3Olq3hRP1Q-yUMo,235
deepspeed/autotuning/tuner/__pycache__/__init__.cpython-310.pyc,,
deepspeed/autotuning/tuner/__pycache__/base_tuner.cpython-310.pyc,,
deepspeed/autotuning/tuner/__pycache__/cost_model.cpython-310.pyc,,
deepspeed/autotuning/tuner/__pycache__/index_based_tuner.cpython-310.pyc,,
deepspeed/autotuning/tuner/__pycache__/model_based_tuner.cpython-310.pyc,,
deepspeed/autotuning/tuner/__pycache__/utils.cpython-310.pyc,,
deepspeed/autotuning/tuner/base_tuner.py,sha256=psA1I4-AEfqGrGLvYYUl3EjM8jG8AlhUXz-AnrFZ7G4,2754
deepspeed/autotuning/tuner/cost_model.py,sha256=Uu9jD65cvdUK6aozm6Du3XJQZ_W-yOyUrSWFLdOT0do,1820
deepspeed/autotuning/tuner/index_based_tuner.py,sha256=AEkTByT3XXCyYolAqBiMquV6XHN-ntGmYKMLZKPCRYE,1158
deepspeed/autotuning/tuner/model_based_tuner.py,sha256=f_CrbgZNONRjdMuRhrJkQbhtih6GOg4LdkXqRDRDzaM,5614
deepspeed/autotuning/tuner/utils.py,sha256=o5nD51Z6LBylJDRnDNIiOARlBHnOgINBVjBos6ypLEg,2329
deepspeed/autotuning/utils.py,sha256=d4ePhVxVtekB7gIEK1dIOs6bhxOpmFF8yhKPC_rNRDc,15045
deepspeed/checkpoint/__init__.py,sha256=3qhX6swvx0-zo72xLl99eZzBr_KC7PguCVCgemYyAGw,576
deepspeed/checkpoint/__pycache__/__init__.cpython-310.pyc,,
deepspeed/checkpoint/__pycache__/constants.cpython-310.pyc,,
deepspeed/checkpoint/__pycache__/deepspeed_checkpoint.cpython-310.pyc,,
deepspeed/checkpoint/__pycache__/ds_to_universal.cpython-310.pyc,,
deepspeed/checkpoint/__pycache__/reshape_3d_utils.cpython-310.pyc,,
deepspeed/checkpoint/__pycache__/reshape_meg_2d.cpython-310.pyc,,
deepspeed/checkpoint/__pycache__/reshape_utils.cpython-310.pyc,,
deepspeed/checkpoint/__pycache__/universal_checkpoint.cpython-310.pyc,,
deepspeed/checkpoint/__pycache__/utils.cpython-310.pyc,,
deepspeed/checkpoint/__pycache__/zero_checkpoint.cpython-310.pyc,,
deepspeed/checkpoint/constants.py,sha256=FQ55iGvLxiXitjitVfepx3-vLrPx7J-btyfLUGyPGCc,3510
deepspeed/checkpoint/deepspeed_checkpoint.py,sha256=HyD-8upqufOx9AAR0Re34r5UP_KUl8TWHJeQGMHkA1Q,12465
deepspeed/checkpoint/ds_to_universal.py,sha256=1o3vK1ojs4KNs6OoU0mulNP5a4VuJgXoXJFrxMsrs8w,16546
deepspeed/checkpoint/reshape_3d_utils.py,sha256=pmnOtdI3srRS46R9sXLh9kijXPxT3bFJ_c6lWdygbwc,4674
deepspeed/checkpoint/reshape_meg_2d.py,sha256=o5dpF3CbrbtEt1twz0XsL3xbqCfLfEse7O2ke-WR9_s,7885
deepspeed/checkpoint/reshape_utils.py,sha256=n3k8xjXn2--nRzcs7jo7V9lFlma7LTLXjkk9S3IgUhM,3458
deepspeed/checkpoint/universal_checkpoint.py,sha256=2Dde7kefhPhMVsU_0tm5Y6uSlSMJ-Bojd5FAOheKE8Y,6768
deepspeed/checkpoint/utils.py,sha256=YJQIgtUGI4BAMqjdreC8V3cn3xKeWwfknt9rK9IFOGI,2534
deepspeed/checkpoint/zero_checkpoint.py,sha256=hA6ABHaQrPVhujHu1PP0pbHsVhvgrG1VZBjzQnclei0,5421
deepspeed/comm/__init__.py,sha256=N31DOMdAdlfSnQd64slK89dpW_y1Vc9oPVZabLArcYw,137
deepspeed/comm/__pycache__/__init__.cpython-310.pyc,,
deepspeed/comm/__pycache__/backend.cpython-310.pyc,,
deepspeed/comm/__pycache__/ccl.cpython-310.pyc,,
deepspeed/comm/__pycache__/comm.cpython-310.pyc,,
deepspeed/comm/__pycache__/config.cpython-310.pyc,,
deepspeed/comm/__pycache__/constants.cpython-310.pyc,,
deepspeed/comm/__pycache__/reduce_op.cpython-310.pyc,,
deepspeed/comm/__pycache__/torch.cpython-310.pyc,,
deepspeed/comm/__pycache__/utils.cpython-310.pyc,,
deepspeed/comm/backend.py,sha256=TPFMx6ViU4n_NcfwWS56_Yp35PeCi-0Kh9za7UELeyA,1416
deepspeed/comm/ccl.py,sha256=JpASP5GZJuZtoZ7ZeemgAenbcriyEApmJfmKEWYd7kM,8509
deepspeed/comm/comm.py,sha256=T4f1JND1-K3pMPOXVxgjBonYSVfRK4SwMSMY_r_8Tms,29289
deepspeed/comm/config.py,sha256=TP6n9j6oHCDTe8LoLuYwN-zyA3KCefNBGDgD24cT36k,860
deepspeed/comm/constants.py,sha256=Adtnb5LCEjRpylnwgsMAFRmWgPvxb8CEEDvn4-D39lw,1298
deepspeed/comm/reduce_op.py,sha256=A_KxQtpfUrNun6Rn8SX6iQiuoVgfYM7bTtAYc-YTENE,259
deepspeed/comm/torch.py,sha256=QV_xy-J9Dp8tbrvl7x8yteMba3mzGPkCtHK7Pw_64v8,17278
deepspeed/comm/utils.py,sha256=I7hJ2Jq5HEPaJDD09eEI9xV5VJZeDdag0ovTeAc3Ex8,3842
deepspeed/compression/__init__.py,sha256=s3fNEEJJdLa4D4rEwtraP5axoSqpxxmh6LAIvyzfUTM,243
deepspeed/compression/__pycache__/__init__.cpython-310.pyc,,
deepspeed/compression/__pycache__/basic_layer.cpython-310.pyc,,
deepspeed/compression/__pycache__/compress.cpython-310.pyc,,
deepspeed/compression/__pycache__/config.cpython-310.pyc,,
deepspeed/compression/__pycache__/constants.cpython-310.pyc,,
deepspeed/compression/__pycache__/helper.cpython-310.pyc,,
deepspeed/compression/__pycache__/scheduler.cpython-310.pyc,,
deepspeed/compression/__pycache__/utils.cpython-310.pyc,,
deepspeed/compression/basic_layer.py,sha256=oZvNusdxqnlhM-RsaALf5Q-4m2kQLcKAUUMuIKBtXec,36047
deepspeed/compression/compress.py,sha256=DueechYg3bDNAIn2XW0L9nv8qN81acTPwimvD4usE_8,11886
deepspeed/compression/config.py,sha256=5TTZNLB_GDULXEf704-dbNJo0ILL47DNVWHWiMGio8Q,25067
deepspeed/compression/constants.py,sha256=Fc2681jhRfm0ADzNjFKjhYIPSXAI29gqeQSGW0FxLoA,5569
deepspeed/compression/helper.py,sha256=lEFpurhyU7-yKWgO-wp1RojHECkqR8qJ16KrT4Hl06U,14637
deepspeed/compression/scheduler.py,sha256=QwnyBtRhkEkufjyhns-Riz_-GBdUePzuI8UEQJOu7Ho,8112
deepspeed/compression/utils.py,sha256=vLWAEOQHa1w61zmMyFQ3dwxbm-5kM_S-zOI_eQhYx5E,7818
deepspeed/constants.py,sha256=lyWNKJHfTPa0_rN6n4UoAiYERmKct9Sd43j4sH2qveQ,788
deepspeed/elasticity/__init__.py,sha256=Cmi3gEN48VYdPSg4zLOJ1KGKP2G3Xeruz9TykgC13m4,383
deepspeed/elasticity/__pycache__/__init__.cpython-310.pyc,,
deepspeed/elasticity/__pycache__/config.cpython-310.pyc,,
deepspeed/elasticity/__pycache__/constants.cpython-310.pyc,,
deepspeed/elasticity/__pycache__/elastic_agent.cpython-310.pyc,,
deepspeed/elasticity/__pycache__/elasticity.cpython-310.pyc,,
deepspeed/elasticity/__pycache__/utils.cpython-310.pyc,,
deepspeed/elasticity/config.py,sha256=OCmF8fP8_dL3LkKiEJDtOREnJB3NYQuCaK48qRs-eFA,4703
deepspeed/elasticity/constants.py,sha256=5Ynz57XbIFxeFkhC3JwanR-Dw0-U7qrsqKIH9oQo-Hw,2454
deepspeed/elasticity/elastic_agent.py,sha256=_NOjLOnTVd4SQXhSRY_DUs7BuU_tztOxK1cvlVPuSRo,7973
deepspeed/elasticity/elasticity.py,sha256=oGJXhEj-VVdT2iUa-ei_gHXAfhP5e_VgtUlw5N500mU,17361
deepspeed/elasticity/utils.py,sha256=-mbP_VX-SSstZKrEp0hkFwezql9pkAEvxx3BxMO2-ic,347
deepspeed/env_report.py,sha256=2S2lQbOM-FOZ5E2q245v5Dy9O7a2K5Rk_loVSSS7esQ,7475
deepspeed/git_version_info.py,sha256=IZlPdw3IO5LiSJQ5ECQBVNnIj9TL3ezz-EEYfSR54ag,1075
deepspeed/git_version_info_installed.py,sha256=fbGk_vCUaqg8P6CS2OYCt4iI0OByPuQ3P3ucZkmyw7g,248
deepspeed/inference/__init__.py,sha256=ZV8FRXVTJ3IGIDnyXJgJjRlCbB2jH_KJ1hWoTS4l_y0,267
deepspeed/inference/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/__pycache__/config.cpython-310.pyc,,
deepspeed/inference/__pycache__/engine.cpython-310.pyc,,
deepspeed/inference/config.py,sha256=3Utd-MyLRpqKoODaZRiHjbgCAq1Aqt-HbBq6yoWfP0g,10130
deepspeed/inference/engine.py,sha256=3X9IMwa3LYnXLPp90VwBDMo2d0TNknJTMq-Azz0Zv6I,31472
deepspeed/inference/quantization/__init__.py,sha256=4I9UpQ5vMRU5SYSF_dW9FJDEnBq4m_0SuwtVQ92lGaA,95
deepspeed/inference/quantization/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/quantization/__pycache__/layers.cpython-310.pyc,,
deepspeed/inference/quantization/__pycache__/quantization.cpython-310.pyc,,
deepspeed/inference/quantization/__pycache__/quantization_context.cpython-310.pyc,,
deepspeed/inference/quantization/__pycache__/utils.cpython-310.pyc,,
deepspeed/inference/quantization/layers.py,sha256=s62KAh9pysdpMzV3KuaFeOdrezxLSsPcZi-Fmsj0w84,5729
deepspeed/inference/quantization/quantization.py,sha256=-r1gbQC0AZe3umfsjla_2lp6opWrfI-0jfs6KtOaJ8o,4396
deepspeed/inference/quantization/quantization_context.py,sha256=0IV04DTAk8PiOi07TGgABxFMkrKM4QFOqzRaT-RZO7w,514
deepspeed/inference/quantization/utils.py,sha256=kNSafXIsvDF9qBwjLW9bwpDpaKxM0zmY7xmPu-0cVVY,11948
deepspeed/inference/v2/__init__.py,sha256=9LrBSo2xdUl33MPzLedqmI13rnQOJJO-eT6fVsrt61k,283
deepspeed/inference/v2/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/__pycache__/allocator.cpython-310.pyc,,
deepspeed/inference/v2/__pycache__/config_v2.cpython-310.pyc,,
deepspeed/inference/v2/__pycache__/engine_factory.cpython-310.pyc,,
deepspeed/inference/v2/__pycache__/engine_v2.cpython-310.pyc,,
deepspeed/inference/v2/__pycache__/inference_parameter.cpython-310.pyc,,
deepspeed/inference/v2/__pycache__/inference_utils.cpython-310.pyc,,
deepspeed/inference/v2/__pycache__/logging.cpython-310.pyc,,
deepspeed/inference/v2/__pycache__/scheduling_utils.cpython-310.pyc,,
deepspeed/inference/v2/allocator.py,sha256=A-yTKojaNr_O8COkDIFagTl4dd9aYJRIMtR7g8TIvJ4,1182
deepspeed/inference/v2/checkpoint/__init__.py,sha256=6y_8HbNnkff-I3LYNyzZQ3DVDcy8CwjQAYykp_JiCw8,252
deepspeed/inference/v2/checkpoint/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/checkpoint/__pycache__/base_engine.cpython-310.pyc,,
deepspeed/inference/v2/checkpoint/__pycache__/huggingface_engine.cpython-310.pyc,,
deepspeed/inference/v2/checkpoint/__pycache__/in_memory_engine.cpython-310.pyc,,
deepspeed/inference/v2/checkpoint/base_engine.py,sha256=9EmyYEwt1ZE861yuS-ayx9ykG2OzRi9p7_ua4hECMEs,1391
deepspeed/inference/v2/checkpoint/huggingface_engine.py,sha256=Ewf_ZD5yvuZ6uFqeolGlegEnK-FXv-HwWpH3Djf9EXw,5560
deepspeed/inference/v2/checkpoint/in_memory_engine.py,sha256=4tEQQPAWl4BG4FJFdcgnoa8jahMQUtrXtcssTwRUSeo,1487
deepspeed/inference/v2/config_v2.py,sha256=JkEKCeYLrvpMEU84-Yz0b9G_EDCLJPbwwWER1FQywH4,1401
deepspeed/inference/v2/engine_factory.py,sha256=3vlKzgGvgXWY2Bwcjs21IehVSd0fFMrgR_6l621bics,5719
deepspeed/inference/v2/engine_v2.py,sha256=2IiT1buZLPYL66oC8OQdpANhRSFjN-xjvnnCJh9Wqts,10491
deepspeed/inference/v2/inference_parameter.py,sha256=rPeDJ4h58L4weNeIiFDIUrpVgDLpkQtzihUl_L-rkbg,2788
deepspeed/inference/v2/inference_utils.py,sha256=V5gryIBXY9kVVkwPWukMMzQKbn0vH2hyDWnDhQ4jWQk,2378
deepspeed/inference/v2/kernels/__init__.py,sha256=KHPoSEGybfJdU54HoIOCZzeIovB3EsfzaZuHv_i36V4,132
deepspeed/inference/v2/kernels/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/__pycache__/ds_kernel.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/__init__.py,sha256=72TCKbfeUY6LbxtwVeNKljxXvxFrY-zobANphLq7nVQ,276
deepspeed/inference/v2/kernels/core_ops/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/bias_activations/__init__.py,sha256=BpRq2zT5tKcsx4e7f8zcnDA5F6wEedmQXSI6idSEoTo,127
deepspeed/inference/v2/kernels/core_ops/bias_activations/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/bias_activations/__pycache__/bias_activation.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/bias_activations/bias_activation.cpp,sha256=PdSuObELmrSu3lsYgfVEOO9tEEmexqO0an3_pP4nU3k,3084
deepspeed/inference/v2/kernels/core_ops/bias_activations/bias_activation.h,sha256=q3vJjn6I7s_raM1Gy2TNnrY3nbskDDP9QyWwcDjpk6E,688
deepspeed/inference/v2/kernels/core_ops/bias_activations/bias_activation.py,sha256=6uu2cWl65xBVWVHfZR9TfLiqrovUJOxOm50y2iZ9Y28,2437
deepspeed/inference/v2/kernels/core_ops/bias_activations/bias_activation_cuda.cu,sha256=dq_Bwd-UGv8bK3wWA1llNGVUEXO_rAMZInl5PcfpO4Y,4820
deepspeed/inference/v2/kernels/core_ops/blas_kernels/__init__.py,sha256=ND9hDC4WOLfDXnlqe7FySprn8RkCXvzrwQKwYG8ZFRQ,123
deepspeed/inference/v2/kernels/core_ops/blas_kernels/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/blas_kernels/__pycache__/blas_linear.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/blas_kernels/blas.h,sha256=YtINP8OfWWiyZEx44zb1yylsXBHGBLdfxcb3xjBUNeI,5164
deepspeed/inference/v2/kernels/core_ops/blas_kernels/blas_linear.py,sha256=AQ4eOYQ3ejZspCeBHm87Q32f_Lf_ooauZIoVobdD_G0,2023
deepspeed/inference/v2/kernels/core_ops/blas_kernels/blas_utils.h,sha256=tnag_hw1EFyBS-u4N-78zpx2I2YOCqqTpelqui9pxRw,10157
deepspeed/inference/v2/kernels/core_ops/core_ops.cpp,sha256=4HbbHAOUmkaolemLrP_nWOdXSIbAP7KGgl1jTU1LMfI,1514
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/__init__.py,sha256=tUarONsuAuUxyfogdH03MC3yebDySSrUEusyIEWNjrg,174
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/__pycache__/cuda_fp_ln_base.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/__pycache__/cuda_ln.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/__pycache__/cuda_post_ln.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/__pycache__/cuda_pre_ln.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/cuda_fp_ln_base.py,sha256=CmnqS4_iIbPWKbzNCKEv3wnng8UnRtyhSJiEz08cfPI,1302
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/cuda_ln.py,sha256=bp4CvANVf8hQQzyKOIScnR1OU7kB41uBNK5ANIydCvs,854
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/cuda_post_ln.py,sha256=cIJ9l9ey_fAdjuX00ZjooepUgvNEg_bc2ikSiR0MWK0,990
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/cuda_pre_ln.py,sha256=meb0fP8vvu1lFGlMR8iEus7YKlGP6FMlad9FJ7ZPg8A,1225
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/layer_norm.cpp,sha256=cWe1Z6kgQuPsrSTCOO1mWBtFY_CYqXHUt1HqhfiOyNs,4199
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/layer_norm.h,sha256=SCuiMaChAEgmE0t2kwfbqaYXQ1ycU1QXDVDJSHxtQ6o,2157
deepspeed/inference/v2/kernels/core_ops/cuda_layer_norm/layer_norm_cuda.cu,sha256=0lUOi_nh1YrOFUwAx4KQsIsIaNlSvrCBkfoOxi3wJtk,19765
deepspeed/inference/v2/kernels/core_ops/cuda_linear/__init__.py,sha256=0J6ih4FAYwgMMo0HSRWLTiBimI9hdeAFh9Ui5iwx2NU,123
deepspeed/inference/v2/kernels/core_ops/cuda_linear/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_linear/__pycache__/cuda_linear.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_linear/cuda_linear.py,sha256=_2OxKHsY9P5mKOW3sT1ZTIy72d3BGpEjt7_Kb2bNsD4,6827
deepspeed/inference/v2/kernels/core_ops/cuda_linear/include/configs.h,sha256=XPl9PmMyD-OVFksGXN-eK67a-aoxJvjxNEBIXm3q6Fs,5044
deepspeed/inference/v2/kernels/core_ops/cuda_linear/include/kernel_matmul.cuh,sha256=L9SiaNVxzdnDHjRsyBCvk5ZuzuVH8hzps8R3Yeg7BX4,14687
deepspeed/inference/v2/kernels/core_ops/cuda_linear/include/kernel_reduction.cuh,sha256=GQix8cchlsjHeTajB6oM08OEoEC1Mcp62LEsq3XKnAU,1494
deepspeed/inference/v2/kernels/core_ops/cuda_linear/include/ptx_cp.async.cuh,sha256=JyxWtR2lE-EaltNv3hAR-RSaGG-g5eZEreb-c8ie8Qk,2269
deepspeed/inference/v2/kernels/core_ops/cuda_linear/include/ptx_mma.cuh,sha256=mvvQhUqpcJOWjc8VO2H1LT6k_v-bSrRJvYgLIwjd4d8,5081
deepspeed/inference/v2/kernels/core_ops/cuda_linear/include/utils_core.cuh,sha256=FL8C64iGFrrImVrJdsBPfW3OmYMyTjrvmVb8USL--no,10251
deepspeed/inference/v2/kernels/core_ops/cuda_linear/include/utils_gmem.cuh,sha256=PUQriHrotu8edYD2ZvMUfQUpxOkrg9s1aPzeeUfiTyQ,3207
deepspeed/inference/v2/kernels/core_ops/cuda_linear/include/utils_paralleldequant.cuh,sha256=hjCsK9lsMETxax0MtOJwQzVUQF4O1Vth61TxA0WsBcc,4072
deepspeed/inference/v2/kernels/core_ops/cuda_linear/include/weight_prepacking.h,sha256=JTrsVJdRovts8Q7bG7MlCzsVyKL_R76moU-zyakMPGI,8896
deepspeed/inference/v2/kernels/core_ops/cuda_linear/linear_kernels.cpp,sha256=XIFk-nXPumydZOOpupiRPrqgN6wbPRMr86u8_AAqcnM,8892
deepspeed/inference/v2/kernels/core_ops/cuda_linear/linear_kernels.h,sha256=i97f3j3gOIiFK4Ijoiu05U9aj0S4p2NfhBIIYe-hAbM,828
deepspeed/inference/v2/kernels/core_ops/cuda_linear/linear_kernels_cuda.cu,sha256=l5rmu_QnbM1VNmao97uVySwP2sOGjjF919pX18_KXwA,13947
deepspeed/inference/v2/kernels/core_ops/cuda_linear/linear_kernels_cuda.h,sha256=ON5qqFROLBv392C3nwM0adOlKtCHsjO59yYUgGQwz_A,1669
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/__init__.py,sha256=S3Q1i1iwyLp6kWWJjD2kTinhJQ_ND4gKHejhuxQ8-74,171
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/__pycache__/rms_norm.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/__pycache__/rms_norm_base.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/__pycache__/rms_pre_norm.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/rms_norm.cpp,sha256=XqvW2ObN0dkRQIsqtWRGl_JYxkFjKAi-zjFMeEZVa3M,5873
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/rms_norm.h,sha256=FUf2hMir5TnFA6iU1Rx48DlnSYX__Yl0jkiNWARdREA,978
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/rms_norm.py,sha256=OkC86DHLQ03zshmFNUQO9r1mZ4mWD3gN0505IvzC1NE,770
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/rms_norm_base.py,sha256=cbZXvZSXRaD1MoTokRSRuw1anp_FXdGzg0moAxasQ1I,1311
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/rms_norm_cuda.cu,sha256=GrWivwyCSZ8gh1zaiJoKHh8sTcq8aot5m7XKx-Auw1A,10216
deepspeed/inference/v2/kernels/core_ops/cuda_rms_norm/rms_pre_norm.py,sha256=qzME0FbrgV9IXbGCqIA0IP91rfFyRiTdiXwwQ39SufM,1204
deepspeed/inference/v2/kernels/core_ops/gated_activations/__init__.py,sha256=2mIcBil301zqLsisa-B2C2EnTgQ2Zaz2QFRxPTD8eUE,128
deepspeed/inference/v2/kernels/core_ops/gated_activations/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/gated_activations/__pycache__/gated_activation.cpython-310.pyc,,
deepspeed/inference/v2/kernels/core_ops/gated_activations/gated_activation.py,sha256=XQXwTY3PN0qWIdZMBWh8De2lEGtN8syw7ItH_uN319g,2755
deepspeed/inference/v2/kernels/core_ops/gated_activations/gated_activation_kernels.cpp,sha256=aog8VhJGL0F4bwUOZTR232KTgRJRavFFk3Xj_55shLk,3521
deepspeed/inference/v2/kernels/core_ops/gated_activations/gated_activation_kernels.h,sha256=DpAhhx6lO3TNA8eid6Tct9qfDrNFweHRypmn4serMW0,776
deepspeed/inference/v2/kernels/core_ops/gated_activations/gated_activation_kernels_cuda.cu,sha256=Ju3GUCBv_-VOooBKV4POhfBiyu4HsQCKOaNLQPw-vPw,6068
deepspeed/inference/v2/kernels/cutlass_ops/__init__.py,sha256=hpTBFM7draPD0dwxTHvGYgAdic-ysFMG6_XEr8x6V5c,146
deepspeed/inference/v2/kernels/cutlass_ops/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/cutlass_ops/cutlass_ops.cpp,sha256=v6-PzCFZnmxFPXJm-pooeUISCq2V0S4E4_PAvg7oA6w,483
deepspeed/inference/v2/kernels/cutlass_ops/mixed_gemm/__init__.py,sha256=leIZbZ0995YwKwql6mU54HN_DJ1iBe67Cq_GdlXSMAY,122
deepspeed/inference/v2/kernels/cutlass_ops/mixed_gemm/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/cutlass_ops/mixed_gemm/__pycache__/mixed_gemm.cpython-310.pyc,,
deepspeed/inference/v2/kernels/cutlass_ops/mixed_gemm/mixed_gemm.cu,sha256=fDh2z9IwBffz5AI1NltOo4tgC2a2c0bwZ-leLahnR2k,4131
deepspeed/inference/v2/kernels/cutlass_ops/mixed_gemm/mixed_gemm.h,sha256=laUi-GFh6qbLo0Ywiz_h7TtNIxroXSFwPQKc6yBGhE8,410
deepspeed/inference/v2/kernels/cutlass_ops/mixed_gemm/mixed_gemm.py,sha256=gZ7ljMs2f20rcL1624vIhiLg_yemxkrYYFDoX-K1M3Y,2674
deepspeed/inference/v2/kernels/cutlass_ops/mixed_gemm/mixed_gemm_api.h,sha256=soDC6vGW7UCYrnfw1EftpmOz6BuOi27Ihyi8J2Cfw2Q,1570
deepspeed/inference/v2/kernels/cutlass_ops/moe_gemm/__init__.py,sha256=RRc_fJh0o2_l9wVb7Czf2GqTxLzvS6Rj_zeYUGzRjjI,150
deepspeed/inference/v2/kernels/cutlass_ops/moe_gemm/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/cutlass_ops/moe_gemm/__pycache__/mixed_moe_gemm.cpython-310.pyc,,
deepspeed/inference/v2/kernels/cutlass_ops/moe_gemm/__pycache__/moe_gemm.cpython-310.pyc,,
deepspeed/inference/v2/kernels/cutlass_ops/moe_gemm/mixed_moe_gemm.py,sha256=pAaowuHqgwtCqv3tyof9jpbPxZxgLdcj1vPM2NgDcx4,2996
deepspeed/inference/v2/kernels/cutlass_ops/moe_gemm/moe_gemm.cu,sha256=FRZSfEKLH3lHjvdbTbuEr1qGAi1spT6lhTLuIJ_OGuI,8594
deepspeed/inference/v2/kernels/cutlass_ops/moe_gemm/moe_gemm.h,sha256=hdqzk6YOF68wO1DBVWDdtX5z-MIGHdtekijLU8z1cHM,740
deepspeed/inference/v2/kernels/cutlass_ops/moe_gemm/moe_gemm.py,sha256=zX6btY3JLxP-6mgcjlNLSHlmgHJ2z3ZMH6PV3DQPfeE,2572
deepspeed/inference/v2/kernels/cutlass_ops/moe_gemm/moe_gemm_api.h,sha256=RWmGN2LrcQukrsLoYaqykymCQJ1onmubdqXrHHgx2Is,1839
deepspeed/inference/v2/kernels/cutlass_ops/shared_resources/weight_variant.h,sha256=1Mr74mw0SDFt0BsMgWiLvYXXaMWmhIgCUz1MZlWXYMs,278
deepspeed/inference/v2/kernels/ds_kernel.py,sha256=R0n_uBCLCGAGlJa7lvep4cNF0DBxkD6TDKDFNAWpBpU,961
deepspeed/inference/v2/kernels/includes/activation_type.h,sha256=WkFfkL5mgLyYAZimLUouThCkyA2aiF9LQlh1q206L9o,264
deepspeed/inference/v2/kernels/includes/conversion_utils.h,sha256=augq8Zf-t46MaxWh0ryFb46_Guxud0k4kIK-h93rI8Y,12379
deepspeed/inference/v2/kernels/includes/ds_kernel_utils.h,sha256=SNb_fMME8CA5YI1ws4kCW99jR4S2ccQqIgTA7ymmttQ,1280
deepspeed/inference/v2/kernels/includes/memory_access_utils.h,sha256=wqpAqnfmSD5Fx15qwsnY63VCL6tYyJaChCqLxakM10w,33966
deepspeed/inference/v2/kernels/includes/reduction_utils.h,sha256=189h93WF77CblgWonfW5eQqoTeJnb-Rt2Ov2fZ6mO-E,22352
deepspeed/inference/v2/kernels/ragged_ops/__init__.py,sha256=8C6TomumccM_-p-iiyPyjZnDLPjTdQS8E4ydGQGNLu4,324
deepspeed/inference/v2/kernels/ragged_ops/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/atom_builder/__init__.py,sha256=djM0DTjGkr4vvNuhRIWV-2OI7lroepRMBiHIJtrcqTA,124
deepspeed/inference/v2/kernels/ragged_ops/atom_builder/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/atom_builder/__pycache__/atom_builder.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/atom_builder/atom_builder.cpp,sha256=AjIgvgoS7PUYGt4MW7oFu8QEcQvm3J16tyerW-dx8cI,2051
deepspeed/inference/v2/kernels/ragged_ops/atom_builder/atom_builder.h,sha256=i232uHXwHjh2EaAF3709YNcTQ29uVDI2GQOs6GnYAsQ,732
deepspeed/inference/v2/kernels/ragged_ops/atom_builder/atom_builder.py,sha256=QgVnamgI1qG8xjzu-9i5sdBccOuhgbTeAtufUtLNdog,1744
deepspeed/inference/v2/kernels/ragged_ops/blocked_flash/__init__.py,sha256=696rc8-r9nWCnfSG8UZ_fagI28Nerwq-nZCDvIIXPRs,125
deepspeed/inference/v2/kernels/ragged_ops/blocked_flash/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/blocked_flash/__pycache__/blocked_flash.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/blocked_flash/attention_atom.h,sha256=-ITylnAQx_20rXiWdPPldb149cGG-fNQUqOg8iHVugU,1062
deepspeed/inference/v2/kernels/ragged_ops/blocked_flash/blocked_flash.cpp,sha256=qaNovmlpqw7Cw_Y1ArGmH-7fB1nPQd0idvmC4nDR9pw,3912
deepspeed/inference/v2/kernels/ragged_ops/blocked_flash/blocked_flash.h,sha256=io7L6krERzOgiBIKNk3zwEppMDSvWtOqCY9nH35dB_U,459
deepspeed/inference/v2/kernels/ragged_ops/blocked_flash/blocked_flash.py,sha256=-aU8A7FbwrRqJPSHZEOef6Y3_PRndGKfCFT1c-3S1DQ,3830
deepspeed/inference/v2/kernels/ragged_ops/blocked_flash/flash.h,sha256=VJKtKOCufGTGt8ZGuPXfsA-dzsi87cqNTYX7pbqHKH4,1931
deepspeed/inference/v2/kernels/ragged_ops/embed/__init__.py,sha256=y4HCIAaLuHexvTBcNppgiXqo76vHb_pAahL75kQBKY0,137
deepspeed/inference/v2/kernels/ragged_ops/embed/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/embed/__pycache__/embed.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/embed/embed.cpp,sha256=iI-rjkQlCGnujjVzY1h8e6ccO24G7eVxwPLY5t_HKxY,4840
deepspeed/inference/v2/kernels/ragged_ops/embed/embed.cuh,sha256=C5ZS1W9lnEDPBmWsuoJpRP-pgCYwLxqTK_OI-DQrT04,956
deepspeed/inference/v2/kernels/ragged_ops/embed/embed.h,sha256=XbqCgqlx16bRtRLljUstzjssRJIGx7AixbbaofhILl8,700
deepspeed/inference/v2/kernels/ragged_ops/embed/embed.py,sha256=6lxDG2ymTyx2yGZ_9jDFyvcchbtD1iwKr2icLgHtmxE,3011
deepspeed/inference/v2/kernels/ragged_ops/embed/embed_cuda.cu,sha256=5JfvhMBcKAPXQbF4WvE3Ljmolom8ArjvkNtprx1SaKo,6055
deepspeed/inference/v2/kernels/ragged_ops/includes/top_k_utils.h,sha256=JWLYr_XaxsSKzYDrql2mq3czcvpMWdfm5bYYWxQ16VU,512
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/__init__.py,sha256=ou-mEvP9FMj89HvxswZbtTvS_T8Gcy3zf16jPcb_Urw,208
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/__pycache__/blocked_kv_rotary.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/__pycache__/blocked_trained_kv_rotary.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/__pycache__/linear_blocked_kv_copy.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/blocked_kv_rotary.cpp,sha256=qUdVHb-2UMnlH8Q6zZJLUW69T75AMwfqEZjco2EXbdY,8110
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/blocked_kv_rotary.cuh,sha256=X9w1u75CiGNEOV0uaXAmjX8usEiTXFTZG7EK3ulvZDc,1676
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/blocked_kv_rotary.h,sha256=5RfNKDify3eHIV6GjfqNozT6_lc2shcwNidgrQ6AJD0,2406
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/blocked_kv_rotary.py,sha256=_Gm-lifSoXPbJdE4E_KmZJSMatrHDoSKiTSIta6kT1o,3132
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/blocked_kv_rotary_cuda.cu,sha256=mWwmw_7o16vUoscUCYepgsJp-ZRX6ahg8W-7Zc09fek,18951
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/blocked_trained_kv_rotary.py,sha256=paNdriwxr6CQxQ-e7JAvNxg7EloNEMCO3nOlWI7HtWE,3216
deepspeed/inference/v2/kernels/ragged_ops/linear_blocked_kv_rotary/linear_blocked_kv_copy.py,sha256=vGAWocPPIm3UI22GYZEXCWb1MVuyZsp2BeJjKQCxl5g,2953
deepspeed/inference/v2/kernels/ragged_ops/logits_gather/__init__.py,sha256=-6rVFXUnZ3U4MBA1VNuKQfhcdjlWgc_xnufXpnFfGyg,125
deepspeed/inference/v2/kernels/ragged_ops/logits_gather/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/logits_gather/__pycache__/logits_gather.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/logits_gather/logits_gather.cpp,sha256=wItdeUW_dSJzfePXbRKjYeggcfGG_l8yO7OAPXF4IeU,1800
deepspeed/inference/v2/kernels/ragged_ops/logits_gather/logits_gather.cuh,sha256=OQlrhwuHJIwGfhbozWlM4NKhYoKBtAL-3nc0vqATdZY,624
deepspeed/inference/v2/kernels/ragged_ops/logits_gather/logits_gather.h,sha256=bRw1hkiDAAqkP9FkNwsL1QZ0UeGSx1_pYHOY4yoTF5k,571
deepspeed/inference/v2/kernels/ragged_ops/logits_gather/logits_gather.py,sha256=kGPLmxKKqOh2x35QWtCpeXLXxoG15kxmE_Z_-cd7Yv0,2121
deepspeed/inference/v2/kernels/ragged_ops/logits_gather/logits_gather_cuda.cu,sha256=Dd2HJJHpPlDkt2C91667Fn1GVuc4jonsIW8s1vPKhHk,3312
deepspeed/inference/v2/kernels/ragged_ops/moe_gather/__init__.py,sha256=K3BZ4_CDJGkNmvFCeYvIEBY2DRMWqq5CzCL7HAStX8Q,122
deepspeed/inference/v2/kernels/ragged_ops/moe_gather/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/moe_gather/__pycache__/moe_gather.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/moe_gather/moe_gather.cpp,sha256=zWULPg_6e15LzTk5q2DJJcsgwT4zoLhTNIc0ClJf73w,2392
deepspeed/inference/v2/kernels/ragged_ops/moe_gather/moe_gather.cuh,sha256=orang8iBcPNQ0BjFTeiBWNX09UjDq82Sh8tyye-5xyU,706
deepspeed/inference/v2/kernels/ragged_ops/moe_gather/moe_gather.h,sha256=MfJuJ2rg_ba6KvIm1LNbTTHMBH8MYQHgd7JeYJSG9Q8,561
deepspeed/inference/v2/kernels/ragged_ops/moe_gather/moe_gather.py,sha256=31zC0Sb3caA12JdhT7qJoawCUTkKB5PjZpFupQI6FvE,2265
deepspeed/inference/v2/kernels/ragged_ops/moe_gather/moe_gather_cuda.cu,sha256=oy_bzQhyMfmjwiXag0u4YKjyKHaLXMDWYHlJQnQZNWM,6679
deepspeed/inference/v2/kernels/ragged_ops/moe_scatter/__init__.py,sha256=e3cxpdOKqIBYpxvNmMnj6Ms883dqdsl4B_fXdqTnlnM,123
deepspeed/inference/v2/kernels/ragged_ops/moe_scatter/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/moe_scatter/__pycache__/moe_scatter.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/moe_scatter/moe_scatter.cpp,sha256=ywodf_RYumTot90MnTLb4o1h7HDWgiFuVTuhHwxNooE,2858
deepspeed/inference/v2/kernels/ragged_ops/moe_scatter/moe_scatter.cuh,sha256=M6t_ydSLYKsekCym5m07AGDu8-U_qVG0hLuVH_nrO-A,772
deepspeed/inference/v2/kernels/ragged_ops/moe_scatter/moe_scatter.h,sha256=pBdy99sow7TKoB3eV3gdPbCB4_pGNx48K-Eo8C8XjHs,701
deepspeed/inference/v2/kernels/ragged_ops/moe_scatter/moe_scatter.py,sha256=7c_jFxhoJINWl57LIhFZwvlr08W8Nbdk7ULSFmC8Yqc,2504
deepspeed/inference/v2/kernels/ragged_ops/moe_scatter/moe_scatter_cuda.cu,sha256=US5_PjVcIqYcqR2DDKXUIJAMfdXKI0ElfbNCUmLscbw,8070
deepspeed/inference/v2/kernels/ragged_ops/ragged_helpers/ragged_dtypes.h,sha256=OVEob4D9LFPkyxA4PscOrGV5BzBalfiMrTrkcifCvL0,940
deepspeed/inference/v2/kernels/ragged_ops/ragged_helpers/ragged_kernel_helpers.cpp,sha256=QaWqjTz4kXXR804xXlgNBTOxtOtLSP3NcGDNHuFqqQI,1149
deepspeed/inference/v2/kernels/ragged_ops/ragged_helpers/ragged_kernel_helpers.h,sha256=_7Xb4l1mfYbSz7i2HAZ8TqQSLJy6TBBC51OMaarJXfM,563
deepspeed/inference/v2/kernels/ragged_ops/ragged_ops.cpp,sha256=Bnku0tlvBSyPIwOtx7-kvIGg3OPGmsgjMO72rLABEkQ,1472
deepspeed/inference/v2/kernels/ragged_ops/top_k_gating/__init__.py,sha256=N0qSPddtzqj_UkISW7rxEVXpdk8a5JItbXBjNAY_FBE,139
deepspeed/inference/v2/kernels/ragged_ops/top_k_gating/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/top_k_gating/__pycache__/top_k_gating.cpython-310.pyc,,
deepspeed/inference/v2/kernels/ragged_ops/top_k_gating/top_k_gating.cpp,sha256=EBxWZ_Nm2ygSbFtnzI7kGMsl3nZxECXXefAVjMDDVx4,2435
deepspeed/inference/v2/kernels/ragged_ops/top_k_gating/top_k_gating.cuh,sha256=1U1wNpJPmIhCiSwRi7xeRSR_zCcGnuXbFwBOz_gQplw,751
deepspeed/inference/v2/kernels/ragged_ops/top_k_gating/top_k_gating.h,sha256=FdryRq5nESxX9HgzKIhnUgwfaoOzLir5O361gf-4g_M,556
deepspeed/inference/v2/kernels/ragged_ops/top_k_gating/top_k_gating.py,sha256=-xgNLLmp6ea_QIMkGtuYDgt-fLfvq6wPVKgMWQpIHfA,2578
deepspeed/inference/v2/kernels/ragged_ops/top_k_gating/top_k_gating_cuda.cu,sha256=LCuHhCZhWYGT69EW4mq3sPYr9JafW9Ef1YIScTNCQC0,5130
deepspeed/inference/v2/logging.py,sha256=H4Dp4vKNi42HZaNgXOAOG-Vki2oC3U3APVQwq3ay8kE,780
deepspeed/inference/v2/model_implementations/__init__.py,sha256=q9r2n-KHFDe0VOsQxW-UDMtYgyMJJ6qL5Q4vP7f5jP4,530
deepspeed/inference/v2/model_implementations/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/__pycache__/flat_model_helpers.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/__pycache__/inference_model_base.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/__pycache__/inference_policy_base.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/__pycache__/inference_transformer_base.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/__pycache__/layer_container_base.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/__pycache__/parameter_base.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/common_parameters/__init__.py,sha256=_mp6NDvxO-3TM9LiiOTrMBRIweeiRjU6s6L-GpTvX9U,359
deepspeed/inference/v2/model_implementations/common_parameters/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/common_parameters/__pycache__/attn_output_parameters.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/common_parameters/__pycache__/embedding_parameters.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/common_parameters/__pycache__/invfreq_parameters.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/common_parameters/__pycache__/mlp_parameters.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/common_parameters/__pycache__/moe_parameters.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/common_parameters/__pycache__/norm_parameters.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/common_parameters/__pycache__/qkv_parameters.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/common_parameters/__pycache__/unembed_parameters.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/common_parameters/attn_output_parameters.py,sha256=VwOj8hRbIQQnWfoHVq-vbRK_QLt9f-uimARsTHfot4w,763
deepspeed/inference/v2/model_implementations/common_parameters/embedding_parameters.py,sha256=k7CMnQ06UchamvQXsdeiTfxAkjpor3qZjTU0cD20hZU,605
deepspeed/inference/v2/model_implementations/common_parameters/invfreq_parameters.py,sha256=Srz4b0mFK73OBzsw3EK3Aydj21Z1gZjLuyC8qfry6QI,401
deepspeed/inference/v2/model_implementations/common_parameters/mlp_parameters.py,sha256=LWMXApceofCiyZP0CvNtiEZhK6A23OTriuTynOXAjwI,2753
deepspeed/inference/v2/model_implementations/common_parameters/moe_parameters.py,sha256=hTKZ8zIERsvQ6i6zQRWe47qUcJmMAGOHfJinwpY3r04,2548
deepspeed/inference/v2/model_implementations/common_parameters/norm_parameters.py,sha256=xTaLT5H81-Et8y9FivEaY8N9VQ0XDsvtlV4v1wnFUG4,454
deepspeed/inference/v2/model_implementations/common_parameters/qkv_parameters.py,sha256=pxBm6EBMNLvbJTPAY9ABjdQ68-oxbY_nWb5CFGDdvdA,4086
deepspeed/inference/v2/model_implementations/common_parameters/unembed_parameters.py,sha256=-gdVA8UMMvFsd-lSAvCbQp5mQ25j1sT5f7KC2mSVY_A,651
deepspeed/inference/v2/model_implementations/falcon/__init__.py,sha256=IVA-W-wyht49JDQt7MTTsBMN4dHAAQxWPEULVVwkoVY,129
deepspeed/inference/v2/model_implementations/falcon/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/falcon/__pycache__/container.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/falcon/__pycache__/model.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/falcon/__pycache__/policy.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/falcon/container.py,sha256=TNdqevl_yh4g06l80i3RJC88dExK2bfd_22NSA-yz0M,4479
deepspeed/inference/v2/model_implementations/falcon/model.py,sha256=ELmk5fmti6cUmUqYRcsnykS746An6eEibPf0G3IQsgs,7768
deepspeed/inference/v2/model_implementations/falcon/policy.py,sha256=oFyIvfzFEistRJCzXS-wPtFfUXDPqt9ANhPa7ywrKSQ,1342
deepspeed/inference/v2/model_implementations/flat_model_helpers.py,sha256=w5xeAQXsJ39EDHTiOAih4zNatO_1LlwB6CdGIyTymDo,9741
deepspeed/inference/v2/model_implementations/inference_model_base.py,sha256=3YVtQafVIrZMI-kZadRKmQklafI5fjc38GTdTZeM3jQ,10083
deepspeed/inference/v2/model_implementations/inference_policy_base.py,sha256=enve3SYK8HTf0HQB8FieQQt22zEaJRQD0MWI-1VtddU,9461
deepspeed/inference/v2/model_implementations/inference_transformer_base.py,sha256=SgXRyFYWj31DJMbDpgo06nEyAJLD-w3qKJF1xbAm9Jo,23592
deepspeed/inference/v2/model_implementations/layer_container_base.py,sha256=dWkfTe5NwRV2icpr2n9z-WsVTBlmitOxteFkgaAKPhg,15443
deepspeed/inference/v2/model_implementations/llama_v2/__init__.py,sha256=J8aLmbt1KDVL_LOtSqaP-xACSxlgIDjeaIA9R-Grsgs,129
deepspeed/inference/v2/model_implementations/llama_v2/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/llama_v2/__pycache__/container.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/llama_v2/__pycache__/model.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/llama_v2/__pycache__/policy.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/llama_v2/container.py,sha256=blAioiSrdB9kH0zHFV0ZO1kWChv6-geaZW1ivEaSQx8,2664
deepspeed/inference/v2/model_implementations/llama_v2/model.py,sha256=YmauqGYxtx2o_apo28aYvEhxi3SmYYvLm1IoEE7tGgs,7515
deepspeed/inference/v2/model_implementations/llama_v2/policy.py,sha256=A1z_j19nj8yOURNNC29DeSyMatAjzuPUtcnIz09zZII,1148
deepspeed/inference/v2/model_implementations/mistral/__init__.py,sha256=rzXGZBulBvo8ZNt7E5z1mSi8OlN1edNDTFlLCMCqo3M,130
deepspeed/inference/v2/model_implementations/mistral/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/mistral/__pycache__/container.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/mistral/__pycache__/model.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/mistral/__pycache__/policy.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/mistral/container.py,sha256=0THLABZ6g4W4H_Kf2INGnyVrXHroT2uveKr-F2deP88,2784
deepspeed/inference/v2/model_implementations/mistral/model.py,sha256=QhDoAoi2aMguJf4GJXqNyivqRKAjJLUwf5EsVgYHl38,7350
deepspeed/inference/v2/model_implementations/mistral/policy.py,sha256=ceBkx-EOWH6UmcosmmhcE7anO5cDMaPGhLInK-x3PBA,1056
deepspeed/inference/v2/model_implementations/mixtral/__init__.py,sha256=ywKUfNNkXZuDaMa9NT05H3MkYJQgiCWl3eM8kZMkhJE,130
deepspeed/inference/v2/model_implementations/mixtral/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/mixtral/__pycache__/container.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/mixtral/__pycache__/model.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/mixtral/__pycache__/policy.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/mixtral/container.py,sha256=YMdy5flaitAQHdy71VBCi7n0vQBfWcPRyXTfZeABchA,1658
deepspeed/inference/v2/model_implementations/mixtral/model.py,sha256=smAqT1nkiFmn2ipNNvJqd2BiIHHprI5O7iWKOFsKM4M,9096
deepspeed/inference/v2/model_implementations/mixtral/policy.py,sha256=u1Am_kWG7UvEt8A5V4lTn_BjUZ4eSlnTB32R5agIYy4,1057
deepspeed/inference/v2/model_implementations/opt/__init__.py,sha256=WeJqbtuxqlXaqif4F_4KpZnGBCwXwKEn0OwUZOcxmpo,126
deepspeed/inference/v2/model_implementations/opt/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/opt/__pycache__/container.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/opt/__pycache__/model.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/opt/__pycache__/policy.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/opt/container.py,sha256=1jIU2Icv-lSEi_XMhaS4jGUVWFfbP-B2t2xf52eQVwY,3415
deepspeed/inference/v2/model_implementations/opt/model.py,sha256=3FFAoq-iBo_F5ep0guXKXk6d5BnNOx0QZFiv2VILScs,7262
deepspeed/inference/v2/model_implementations/opt/policy.py,sha256=9cqEPs_pM9gdzsBegbXJ7GHEgHfmD6n_AaRpjMH1yGE,1066
deepspeed/inference/v2/model_implementations/parameter_base.py,sha256=Tb0SRvm6cB6G730Gb6ew051XA8Awngs6gaYaroKn_f0,8993
deepspeed/inference/v2/model_implementations/phi/__init__.py,sha256=jKL7LDi4qrR3X3S6rcFpkls2K0wXVV6Qb3mIkiOtRKk,126
deepspeed/inference/v2/model_implementations/phi/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/phi/__pycache__/containers.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/phi/__pycache__/model.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/phi/__pycache__/policy.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/phi/containers.py,sha256=L1S3qe2iHCv73EtWzb-IPn_dg8rxzwUn27HPXMatmCg,3197
deepspeed/inference/v2/model_implementations/phi/model.py,sha256=b12uXjTpbHy_0EklvawD67KxwkrV-ooYSSdjNxGbz58,7251
deepspeed/inference/v2/model_implementations/phi/policy.py,sha256=FIcCwm7nwUQOoFT1yGV_OTFd2xldUWmL_TqmpjtjUgU,1175
deepspeed/inference/v2/model_implementations/qwen/__init__.py,sha256=1TgzvNCP0loWbohqWRMH9NXCKKjXVVagBtatz_NCHc8,127
deepspeed/inference/v2/model_implementations/qwen/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/qwen/__pycache__/container.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/qwen/__pycache__/model.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/qwen/__pycache__/policy.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/qwen/container.py,sha256=4fnYCNY92cDw116OyEVhmp_fuFtfeeyq4ZG4T5NHVi0,2362
deepspeed/inference/v2/model_implementations/qwen/model.py,sha256=cCoVWHR2t_9LrLSwYE2LTCJa6nxXj9-l-Qr5q819Iq0,8112
deepspeed/inference/v2/model_implementations/qwen/policy.py,sha256=0nP7dboH8FSnh6xvImRKK0gVWkw2iOTImwCJw73V-OA,1066
deepspeed/inference/v2/model_implementations/qwen_v2/__init__.py,sha256=s0OcwPN9W24-8vYSQ8kztzky9jEwOvER9aMR60Xejuk,128
deepspeed/inference/v2/model_implementations/qwen_v2/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/qwen_v2/__pycache__/container.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/qwen_v2/__pycache__/model.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/qwen_v2/__pycache__/policy.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/qwen_v2/container.py,sha256=HpDpzng1cMtevhB-XykEKvmPPXENaDRV6BiLVYM7wjo,2817
deepspeed/inference/v2/model_implementations/qwen_v2/model.py,sha256=EENwSZm-DZRQrv64UCLoEHop_U8xZ9yVejtboPzri_8,8029
deepspeed/inference/v2/model_implementations/qwen_v2/policy.py,sha256=U9rMVvegwkKMqw6SoFXGZXobYTrjfpfySUryrpF0TDs,1140
deepspeed/inference/v2/model_implementations/sharding/__init__.py,sha256=M0NsMjUqO0Rj0PAPm2lyRepH5lc3uFQswNM5Gr3QAcI,247
deepspeed/inference/v2/model_implementations/sharding/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/sharding/__pycache__/attn.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/sharding/__pycache__/attn_out.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/sharding/__pycache__/embedding.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/sharding/__pycache__/mlp.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/sharding/__pycache__/qkv.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/sharding/__pycache__/types.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/sharding/__pycache__/unembed.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/sharding/__pycache__/utils.cpython-310.pyc,,
deepspeed/inference/v2/model_implementations/sharding/attn.py,sha256=QAapGaB4Bp1k9xI53-QhhyKFoaeIPandzI1qnPT2rxE,2376
deepspeed/inference/v2/model_implementations/sharding/attn_out.py,sha256=MWf6O0P55Am0x947474ZqGcOEuZn2nGptKcNPCxfS0c,4909
deepspeed/inference/v2/model_implementations/sharding/embedding.py,sha256=mJgZR289D6_ODuTZm4pVsedyJEHaXH2a4ncI8_7YdG0,1270
deepspeed/inference/v2/model_implementations/sharding/mlp.py,sha256=UIpBwlbU9xDNv1iDbIfcxqPgcAf_WvO42NxtMGyZ1QQ,2895
deepspeed/inference/v2/model_implementations/sharding/qkv.py,sha256=pQfXbEPeqEZ9eCJwcdSvJsid5jeBJ2jptWhqVaPJSPM,7609
deepspeed/inference/v2/model_implementations/sharding/types.py,sha256=YqDHnyDLGhy2sf37n-a0eHIIXqcQ8OjHKHQ-rcKAl8E,575
deepspeed/inference/v2/model_implementations/sharding/unembed.py,sha256=1AmukReQ6SAfFlq2YdiQwJrrvPLVx7fhel7KKCGXXjQ,1625
deepspeed/inference/v2/model_implementations/sharding/utils.py,sha256=wKlbKIwSyblinL1xupsHDXodws2zfWhxt-flmac1grc,5066
deepspeed/inference/v2/modules/__init__.py,sha256=oZhP5-VwkgPMtHVFO_9MefXPnSEfcHdQeIXaRg3VOeY,193
deepspeed/inference/v2/modules/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/__pycache__/ds_module.cpython-310.pyc,,
deepspeed/inference/v2/modules/__pycache__/heuristics.cpython-310.pyc,,
deepspeed/inference/v2/modules/__pycache__/module_registry.cpython-310.pyc,,
deepspeed/inference/v2/modules/configs/__init__.py,sha256=IJ5d0k84imLnM7jh0UcZ9k3_ITPU7YI_6ZoGRpEJcqs,449
deepspeed/inference/v2/modules/configs/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/configs/__pycache__/attention_configs.cpython-310.pyc,,
deepspeed/inference/v2/modules/configs/__pycache__/embedding_config.cpython-310.pyc,,
deepspeed/inference/v2/modules/configs/__pycache__/linear_config.cpython-310.pyc,,
deepspeed/inference/v2/modules/configs/__pycache__/moe_config.cpython-310.pyc,,
deepspeed/inference/v2/modules/configs/__pycache__/norm_config.cpython-310.pyc,,
deepspeed/inference/v2/modules/configs/__pycache__/unembed_config.cpython-310.pyc,,
deepspeed/inference/v2/modules/configs/attention_configs.py,sha256=IWbmcmCc3CdL1E4FEUhrN9xU9wfbEaswUD8a0cM_D3Y,2834
deepspeed/inference/v2/modules/configs/embedding_config.py,sha256=PuRrpOWLwtqjZRg9jumeMHuqzzsTcrbCjRxKL2L6xww,1812
deepspeed/inference/v2/modules/configs/linear_config.py,sha256=fz9m8RIL6NVjbkq06-0WSP4-7IxIdSVQi1nRY4WQdTc,1301
deepspeed/inference/v2/modules/configs/moe_config.py,sha256=EqX1OLq_QGt4IrSGhAJUXfVvy0MqjZk2E3toeUkb7kA,1289
deepspeed/inference/v2/modules/configs/norm_config.py,sha256=iTKxfBxWcHs2JiiuxNZU356DGJR-a0OgDW3p5oshedk,839
deepspeed/inference/v2/modules/configs/unembed_config.py,sha256=2E9XPh8VSRJckwng_DwbQksLMXtWZMFiXUjo5GWhzqI,822
deepspeed/inference/v2/modules/ds_module.py,sha256=1h8X2TvYvDsUyEUjllPlziT7XOqwRxP02GBagzJw7FU,1856
deepspeed/inference/v2/modules/heuristics.py,sha256=eRitZ_RX2yg4KOgmjiJaTEDZfH-zauMDIaf3WVQ2rGc,7978
deepspeed/inference/v2/modules/implementations/__init__.py,sha256=0rEv4jixmtnw_sIe_Y5UKaIEcG7qwALUSzx1GNORTdk,289
deepspeed/inference/v2/modules/implementations/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/attention/__init__.py,sha256=tNj2_fpavmUWaI5VAkUF0I7k0OxmIlZRNZZl7JIqrR4,157
deepspeed/inference/v2/modules/implementations/attention/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/attention/__pycache__/dense_blocked_attention.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/attention/dense_blocked_attention.py,sha256=JgS7E2TzKmWylwRYWiYR-F7ldLdg8qhGr8Q9bXSPoCA,7735
deepspeed/inference/v2/modules/implementations/embedding/__init__.py,sha256=CUiHnEi77BVJmH3aoJOanq5mTJIvEcIo3cHcbkddDHs,144
deepspeed/inference/v2/modules/implementations/embedding/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/embedding/__pycache__/ragged_embedding.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/embedding/ragged_embedding.py,sha256=zddQlHcpjLe2_dKQUTp_5VfJwTP6L-BRk8HUHD2vPRo,2740
deepspeed/inference/v2/modules/implementations/linear/__init__.py,sha256=FXT-bP-U4YlbMbBDrgwkpMPTHps3NiztMczMulIgmPU,203
deepspeed/inference/v2/modules/implementations/linear/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/linear/__pycache__/blas_fp_linear.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/linear/__pycache__/quantized_linear.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/linear/blas_fp_linear.py,sha256=nHZHiLKVcppWyn0hNrQq4sHrS9UYKlHunrwL92DUVkQ,3629
deepspeed/inference/v2/modules/implementations/linear/quantized_linear.py,sha256=5CQEpEBNq-Tzyt5dH0OK3RzZJUWjAxBieqzuC2OFJgw,7772
deepspeed/inference/v2/modules/implementations/moe/__init__.py,sha256=XP3Ddz7eBtLvpIGum9u3gzUhl61w0W_jNfopcMI3QRE,143
deepspeed/inference/v2/modules/implementations/moe/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/moe/__pycache__/cutlass_multi_gemm.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/moe/cutlass_multi_gemm.py,sha256=s1sdp-4N7dpwqg9m1FhkRvAQmaC0DxKs6h-5rzXyjyY,11082
deepspeed/inference/v2/modules/implementations/post_norm/__init__.py,sha256=4hE4KqEcl74cOWm1kvexksZwHMK8rk-OQMzJYkdmgQo,141
deepspeed/inference/v2/modules/implementations/post_norm/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/post_norm/__pycache__/cuda_post_ln.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/post_norm/cuda_post_ln.py,sha256=DNHGpJ2wNC8P6vyyk8M8hDpuXpA7k-ni78W3olRKO18,2086
deepspeed/inference/v2/modules/implementations/pre_norm/__init__.py,sha256=_yJ5_1apmG5KriWHdy9klvL5H0vFzmLyWnZf1VIrmrA,184
deepspeed/inference/v2/modules/implementations/pre_norm/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/pre_norm/__pycache__/cuda_pre_ln.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/pre_norm/__pycache__/cuda_pre_rms.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/pre_norm/cuda_pre_ln.py,sha256=VvFlm3Xc6U7VVyfXnFUeA5tsLw4TLhK_LVlKJkN4SmY,2744
deepspeed/inference/v2/modules/implementations/pre_norm/cuda_pre_rms.py,sha256=WxZeP0B1ghhpOiMIHig4vb0ou0q7jkiLZFfbbdREXtQ,3245
deepspeed/inference/v2/modules/implementations/unembed/__init__.py,sha256=ve_166eLGPGfIncTIvEvqoFGKi2pvtgvSLVVvTPXpHQ,140
deepspeed/inference/v2/modules/implementations/unembed/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/unembed/__pycache__/ragged_unembed.cpython-310.pyc,,
deepspeed/inference/v2/modules/implementations/unembed/ragged_unembed.py,sha256=cAd3By6SBykoCHJI3oxwE0FXHcMP5HEF5CSrWJyBjjg,4921
deepspeed/inference/v2/modules/interfaces/__init__.py,sha256=2djwkw0m2KAAuBV-LuWY0W0ncwIrCxDX5uwf4oc8E-U,519
deepspeed/inference/v2/modules/interfaces/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/modules/interfaces/__pycache__/attention_base.cpython-310.pyc,,
deepspeed/inference/v2/modules/interfaces/__pycache__/embedding_base.cpython-310.pyc,,
deepspeed/inference/v2/modules/interfaces/__pycache__/linear_base.cpython-310.pyc,,
deepspeed/inference/v2/modules/interfaces/__pycache__/moe_base.cpython-310.pyc,,
deepspeed/inference/v2/modules/interfaces/__pycache__/post_norm_base.cpython-310.pyc,,
deepspeed/inference/v2/modules/interfaces/__pycache__/pre_norm_base.cpython-310.pyc,,
deepspeed/inference/v2/modules/interfaces/__pycache__/unembed_base.cpython-310.pyc,,
deepspeed/inference/v2/modules/interfaces/attention_base.py,sha256=Kl8aPbpzyhP4bF21LZ_OcEwHFoI1KFirOiukiV4YajE,3613
deepspeed/inference/v2/modules/interfaces/embedding_base.py,sha256=3pXB9F4IlWMqFGV8ZYE3k3bG2Je4B9uvVnVS0pRzLeU,3071
deepspeed/inference/v2/modules/interfaces/linear_base.py,sha256=8g8or7xUMtGX_3uQj6zt0YfUBS9SwSbRY02lrr3gUVE,2146
deepspeed/inference/v2/modules/interfaces/moe_base.py,sha256=5BCoKWZplUfxMj7mOPA58b4aBGk7APM-WG-ByxUIUyA,2850
deepspeed/inference/v2/modules/interfaces/post_norm_base.py,sha256=PktjhFD5Pi3y2zt_VEwB9L-yNVyIo4dHpJgdVwgbcVc,2123
deepspeed/inference/v2/modules/interfaces/pre_norm_base.py,sha256=QlYoS3N4N3ZKfUJk6C9u_vCT10p4-ACVgAEydLquHnY,2106
deepspeed/inference/v2/modules/interfaces/unembed_base.py,sha256=W5ToOG46DZwH5e1VnFXxXp_f9-EvnO2iIw6zqZkL38w,2055
deepspeed/inference/v2/modules/module_registry.py,sha256=UyDd-5FYQwbEJ7ihPZ60GkEiQ7M6Z2XMWnEAaLZN_WQ,2073
deepspeed/inference/v2/ragged/__init__.py,sha256=2fFLZ35zp9WgcDaQ3XaTti8Hzi99zt8EgueCaaOqZs4,418
deepspeed/inference/v2/ragged/__pycache__/__init__.cpython-310.pyc,,
deepspeed/inference/v2/ragged/__pycache__/blocked_allocator.cpython-310.pyc,,
deepspeed/inference/v2/ragged/__pycache__/kv_cache.cpython-310.pyc,,
deepspeed/inference/v2/ragged/__pycache__/manager_configs.cpython-310.pyc,,
deepspeed/inference/v2/ragged/__pycache__/ragged_manager.cpython-310.pyc,,
deepspeed/inference/v2/ragged/__pycache__/ragged_wrapper.cpython-310.pyc,,
deepspeed/inference/v2/ragged/__pycache__/sequence_descriptor.cpython-310.pyc,,
deepspeed/inference/v2/ragged/blocked_allocator.py,sha256=8rwqli63wrLAbzOuLoOgVqLi5UlpPlyxXtyRS1gU6zM,3661
deepspeed/inference/v2/ragged/csrc/fast_host_buffer.cu,sha256=8U10U6VZk12oGwXCniQsqduta_cEcVBFuN9Fq3LO1JQ,501
deepspeed/inference/v2/ragged/csrc/ragged_ops.cpp,sha256=Z6na7zRDPD7PrYbZ4lBhBsGyMatZ9yUNYw_PBzRGHw8,2726
deepspeed/inference/v2/ragged/includes/fast_host_buffer.h,sha256=N73g7w1uE1gEsyWzrEnEibGeU8nBJPDeL-SWJEWK_0M,302
deepspeed/inference/v2/ragged/kv_cache.py,sha256=eekwPz2OMNgQJHYXcxIrW86yK0guS419GeNqXJ38wIg,8559
deepspeed/inference/v2/ragged/manager_configs.py,sha256=pRSnmVJKxbaiSNrzZIHKsnJ50lMSLufYwXFhMTmk6VU,6077
deepspeed/inference/v2/ragged/ragged_manager.py,sha256=Mk09dkqkEj5CMA9kWvTWuQeiw7fWiO9xn_nMIs-1Hho,7334
deepspeed/inference/v2/ragged/ragged_wrapper.py,sha256=RJJpqSVz6vsElbtfePx99TDYZ2Q_Hao7qt_2Jt6RP2s,12701
deepspeed/inference/v2/ragged/sequence_descriptor.py,sha256=oo1kf0upitTJkbZB6ME7GY3_MoOnGABXcw1coq49ljM,10891
deepspeed/inference/v2/scheduling_utils.py,sha256=S1MPmx8zU8qHmDd_eDLhKP6hh71N9lo_Q8kJxO1AyOY,1363
deepspeed/launcher/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/launcher/__pycache__/__init__.cpython-310.pyc,,
deepspeed/launcher/__pycache__/constants.cpython-310.pyc,,
deepspeed/launcher/__pycache__/launch.cpython-310.pyc,,
deepspeed/launcher/__pycache__/launcher_helper.cpython-310.pyc,,
deepspeed/launcher/__pycache__/multinode_runner.cpython-310.pyc,,
deepspeed/launcher/__pycache__/runner.cpython-310.pyc,,
deepspeed/launcher/constants.py,sha256=X-6bI3aC4NXSZgrMJ6LaIILrsWu_TNteNBwgKOhNrgI,375
deepspeed/launcher/launch.py,sha256=ovIHdP7OVIy3mrjCE0B0MnOKrQaPUhYzKm1g2HAUOIc,14878
deepspeed/launcher/launcher_helper.py,sha256=tpvx6ItaLbBNYnUwP8XoKcn8P0mFXRffpB1Z_uLB_U0,3931
deepspeed/launcher/multinode_runner.py,sha256=dsGipMGw2LInqGKLIpqgnYWD0BnZfuelGmLyMO-PapA,17081
deepspeed/launcher/runner.py,sha256=r6OkPOy_8yw9YnY_TZw3_T0b5ZzUr3uGFbAsIohwAXU,24713
deepspeed/linear/__init__.py,sha256=3J9crWICQq3F1emDZ6zijePBkHhwhLmqxlAhSqvmlmU,193
deepspeed/linear/__pycache__/__init__.cpython-310.pyc,,
deepspeed/linear/__pycache__/config.cpython-310.pyc,,
deepspeed/linear/__pycache__/optimized_linear.cpython-310.pyc,,
deepspeed/linear/__pycache__/quantization.cpython-310.pyc,,
deepspeed/linear/config.py,sha256=Z3Wh3o62spkpLIvRhazaSPl_3egA863JbhIfVyUIObo,1282
deepspeed/linear/optimized_linear.py,sha256=ml7OOKsIoO1aXEZTAQVXuQx9QePT8YuHpfFuLbptml8,6647
deepspeed/linear/quantization.py,sha256=Tmuy1nHm1YW-5TuKQjzWHjFJCknViXZDNWWbZyMF2nQ,5921
deepspeed/model_implementations/__init__.py,sha256=jJCJcnbvmFc9eQxIUIb7Gd6Ysd8ijSuuk-aaqFrDkK4,220
deepspeed/model_implementations/__pycache__/__init__.cpython-310.pyc,,
deepspeed/model_implementations/diffusers/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/model_implementations/diffusers/__pycache__/__init__.cpython-310.pyc,,
deepspeed/model_implementations/diffusers/__pycache__/unet.cpython-310.pyc,,
deepspeed/model_implementations/diffusers/__pycache__/vae.cpython-310.pyc,,
deepspeed/model_implementations/diffusers/unet.py,sha256=W2_9-c-5Tz-nymu7kK2du2rzpV8-eC8JZEpQGRhGMek,3056
deepspeed/model_implementations/diffusers/vae.py,sha256=xbjGaYsXeJA4nu49gQaGO5GrEazCHwcTUVqPsxoOTn4,6244
deepspeed/model_implementations/features/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/model_implementations/features/__pycache__/__init__.cpython-310.pyc,,
deepspeed/model_implementations/features/__pycache__/cuda_graph.cpython-310.pyc,,
deepspeed/model_implementations/features/cuda_graph.py,sha256=-KgILcht5qw_ayzI0CqSPGGFoqZKHG8E5CTfD8ACgw8,563
deepspeed/model_implementations/transformers/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/model_implementations/transformers/__pycache__/__init__.cpython-310.pyc,,
deepspeed/model_implementations/transformers/__pycache__/clip_encoder.cpython-310.pyc,,
deepspeed/model_implementations/transformers/__pycache__/ds_base.cpython-310.pyc,,
deepspeed/model_implementations/transformers/__pycache__/ds_bert.cpython-310.pyc,,
deepspeed/model_implementations/transformers/__pycache__/ds_bloom.cpython-310.pyc,,
deepspeed/model_implementations/transformers/__pycache__/ds_gpt.cpython-310.pyc,,
deepspeed/model_implementations/transformers/__pycache__/ds_llama2.cpython-310.pyc,,
deepspeed/model_implementations/transformers/__pycache__/ds_megatron_gpt.cpython-310.pyc,,
deepspeed/model_implementations/transformers/__pycache__/ds_opt.cpython-310.pyc,,
deepspeed/model_implementations/transformers/__pycache__/ds_transformer.cpython-310.pyc,,
deepspeed/model_implementations/transformers/clip_encoder.py,sha256=BTc_do6u4A79uhoNwQYxKrbqSnk2C63BO6xN0Ez-fz0,3096
deepspeed/model_implementations/transformers/ds_base.py,sha256=feBwoCtVctmdYb_KZruLVCJr7WUBsfNZtVUxEKtO5MQ,388
deepspeed/model_implementations/transformers/ds_bert.py,sha256=fckfXCn3zf9DsvqiyVAb0pwn25GA9H4ZceaCI4gLLSI,667
deepspeed/model_implementations/transformers/ds_bloom.py,sha256=_YHlkBOUBTT27ZRWs7x6DxKz_5gILGBAe2YNmlkCij0,669
deepspeed/model_implementations/transformers/ds_gpt.py,sha256=PkhQU0iHFkSGRxaEb_j1UDvRWruwrOPF8MLZfAo55ZY,665
deepspeed/model_implementations/transformers/ds_llama2.py,sha256=6rAOou4WaViFUbjsf9nOS35vMxl9Hqe5BXpL6Vce-HA,2792
deepspeed/model_implementations/transformers/ds_megatron_gpt.py,sha256=kVtyp2Uss9Z_MRGwgAvJI_2tfWlPgzMpywIPS0xeOIo,682
deepspeed/model_implementations/transformers/ds_opt.py,sha256=oHS210T27R5zOtg8ik_QwV22F8BIJxKVWPiPQaDXzjY,665
deepspeed/model_implementations/transformers/ds_transformer.py,sha256=VlDZgrp0PMx9pz5DrpO56m2s73dY4WuAYUJAcN5gtGU,8923
deepspeed/module_inject/__init__.py,sha256=lEgBDKuhccVUSVhcBdqZU9dCUXzxTrFySQ-E4dYbVNw,444
deepspeed/module_inject/__pycache__/__init__.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/auto_tp.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/auto_tp_model_utils.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/fusedqkv_utils.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/inject.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/layers.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/load_checkpoint.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/module_quantize.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/policy.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/replace_module.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/replace_policy.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/tp_shard.cpython-310.pyc,,
deepspeed/module_inject/__pycache__/utils.cpython-310.pyc,,
deepspeed/module_inject/auto_tp.py,sha256=QhxSvTOzKGLNgXXfOH6ccUhLWU0gD0Khg3og2fSFLQs,24624
deepspeed/module_inject/auto_tp_model_utils.py,sha256=G_tPVAaT7b422Wa8J785rEOcM742uXnoW-5fvd1cinU,5910
deepspeed/module_inject/containers/__init__.py,sha256=AQbuSXpxEyLpLN3RoAkQeyvtioX_8ShH6C8cqzcQ2Jw,1015
deepspeed/module_inject/containers/__pycache__/__init__.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/base.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/base_moe.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/bert.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/bloom.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/clip.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/distil_bert.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/gpt2.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/gptj.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/gptneo.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/gptneox.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/internlm.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/llama.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/llama2.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/megatron_gpt.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/megatron_gpt_moe.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/opt.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/unet.cpython-310.pyc,,
deepspeed/module_inject/containers/__pycache__/vae.cpython-310.pyc,,
deepspeed/module_inject/containers/base.py,sha256=kw-culwCQ480zVCgeY7K9LH30BSuKWuPtm2lBMh-L_o,13762
deepspeed/module_inject/containers/base_moe.py,sha256=lfyM4AhAXmYp5dxa60XoEbcJDQVj9uB-pRlzIDR2ypI,5756
deepspeed/module_inject/containers/bert.py,sha256=f0VMB8R5mCHxNgIVhQSYeTgVsxo1IN4BV6vzSIOgLZc,3769
deepspeed/module_inject/containers/bloom.py,sha256=w3KopULQ1-kVh4nwPGXv6k80gp4FWlS0PS8Arzb4UWM,5702
deepspeed/module_inject/containers/clip.py,sha256=KR3s7ev3v-ccvrFhqfm2pKjApQeYeCfhm4aZZ0_Ou_A,2822
deepspeed/module_inject/containers/distil_bert.py,sha256=j8ufi0mXYe862fLYMtY1J0Rh2uY-xzkXKDKrhK-EsgI,3188
deepspeed/module_inject/containers/features/__init__.py,sha256=GOivP3ix552VHgGPZ70UehyLQ_YZ8TLRc0-rgTCO07c,275
deepspeed/module_inject/containers/features/__pycache__/__init__.cpython-310.pyc,,
deepspeed/module_inject/containers/features/__pycache__/gated_mlp.cpython-310.pyc,,
deepspeed/module_inject/containers/features/__pycache__/hybrid_engine.cpython-310.pyc,,
deepspeed/module_inject/containers/features/__pycache__/hybrid_megatron.cpython-310.pyc,,
deepspeed/module_inject/containers/features/__pycache__/megatron.cpython-310.pyc,,
deepspeed/module_inject/containers/features/__pycache__/meta_tensor.cpython-310.pyc,,
deepspeed/module_inject/containers/features/__pycache__/split_qkv.cpython-310.pyc,,
deepspeed/module_inject/containers/features/gated_mlp.py,sha256=ep8fnRs-06C7oL75w5UBAaLyCb9BwAx2v3c-sBhkB2k,5397
deepspeed/module_inject/containers/features/hybrid_engine.py,sha256=ki_k_0SstM39XfN0SNWwiSO3qzIBs2qKxxAyrGxpfUo,8374
deepspeed/module_inject/containers/features/hybrid_megatron.py,sha256=qp04fT0ManTJwzVdABFP6OWvLQsD6pWDaJAxNJU4-ZQ,4129
deepspeed/module_inject/containers/features/megatron.py,sha256=BrbmtBC9ZKFL4ESbIYRJLzoEnEp3QOhkj1VcoqlcEek,1200
deepspeed/module_inject/containers/features/meta_tensor.py,sha256=F0Zqh5vyp3fmJM8XAbVGmg4PwU4EjNcKT_27qSSCiTE,2926
deepspeed/module_inject/containers/features/split_qkv.py,sha256=totYVBg1M6DcsdC3762Vhoq7gpGJ1BIioTfDFdFwkF0,7136
deepspeed/module_inject/containers/gpt2.py,sha256=98yUKFAGrfyyCv5yK7V4Zr8oWf6m4qTXgGIzUbRG0kM,2221
deepspeed/module_inject/containers/gptj.py,sha256=OJ4Z-G5sjd-92yrNDJ0D53Jz_o0n2tfkg9fS9Mll1ss,5074
deepspeed/module_inject/containers/gptneo.py,sha256=NakhEgR8qh-U9rZ6JD6nIyY_r1mH2obkrgLat4eKpMo,5790
deepspeed/module_inject/containers/gptneox.py,sha256=zM8hYq0e1FKrDd0n0m1oUPKKMDNpwxVmXQHm8jlv1CA,5897
deepspeed/module_inject/containers/internlm.py,sha256=3JYddGlL-87jC35MSKvYw9XzZFHYIIYC4Un0Ek5jsL4,7761
deepspeed/module_inject/containers/llama.py,sha256=aQp5C7cd87xQScxVTbbo5Lzy0oplVtz3uCma1ptw8_8,6740
deepspeed/module_inject/containers/llama2.py,sha256=5WbtiVVAL1dVseHiO5kvx2qOwVAAvq-cKBKo0JZ7YjA,6359
deepspeed/module_inject/containers/megatron_gpt.py,sha256=PiD58-Ul61ZJE4QAkCKCtGwjg44wMaSPOkqnWcRudyo,5417
deepspeed/module_inject/containers/megatron_gpt_moe.py,sha256=xFOff3OOIKMz2YAFRrqLHxpkzLgB-chA4L69HXiey98,3936
deepspeed/module_inject/containers/opt.py,sha256=exIuEYIu-KCjwPL3L-ykz1ud477q-F4WBwbXIL0Hef4,6905
deepspeed/module_inject/containers/unet.py,sha256=fWteUHNx0S8u6C3xpiU2TLK4snlTr7Bv81yCNlVIDY8,1862
deepspeed/module_inject/containers/vae.py,sha256=yLb5XWwZcaVOXVbUjGW1n0hUr3wrL4rqzqFkvBbD1Y4,1505
deepspeed/module_inject/fusedqkv_utils.py,sha256=ZlbeQdq8UqdNrfe1NQ-PnIYLXpiNm0BzvE1kGeJKFPw,5553
deepspeed/module_inject/inject.py,sha256=KkpC_LLybQeiFzIhr6pz-OlKOl92XNFf3Vh7GqD7jsY,4719
deepspeed/module_inject/layers.py,sha256=L6TVH7Ao7qzWDCL3h5tLZrDcWVQvqdB8YZrv1bgoDTk,5618
deepspeed/module_inject/load_checkpoint.py,sha256=f2Usdz9F7UTx1eTQHfUL1ZdaNHlNO5s3YHZxveZpFOU,15294
deepspeed/module_inject/module_quantize.py,sha256=NrOwvSfPWWOnlUVe82sSOZhay76JzqkgZ-j4NP-yEOY,3144
deepspeed/module_inject/policy.py,sha256=kfyIzx4R4INZve94RPBPZu0jnnQKYpWtou-nRvZQ0Z0,8259
deepspeed/module_inject/replace_module.py,sha256=HWD80PNrGl_CnMf-EdAd7DqkXfSm4PURV3sa30neLpg,30642
deepspeed/module_inject/replace_policy.py,sha256=jlIhG7pbXcjD8JPjWJCU-Gs_WSTG_q7K-IHmewu04pw,1119
deepspeed/module_inject/tp_shard.py,sha256=X0yC-u09MWosMkcLJVAIVcj09GrFD8Smpr2qZ5-1gO0,1472
deepspeed/module_inject/utils.py,sha256=q5gZWY7YK_-BI7ce16-G8dvU9Qgx7Qbezz9Hwz7oKS0,1995
deepspeed/moe/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/moe/__pycache__/__init__.cpython-310.pyc,,
deepspeed/moe/__pycache__/experts.cpython-310.pyc,,
deepspeed/moe/__pycache__/layer.cpython-310.pyc,,
deepspeed/moe/__pycache__/mappings.cpython-310.pyc,,
deepspeed/moe/__pycache__/sharded_moe.cpython-310.pyc,,
deepspeed/moe/__pycache__/utils.cpython-310.pyc,,
deepspeed/moe/experts.py,sha256=7xFMeuKcBmfyVdaVKpUw5uyGzHYbtxXTiKqcUuBjrQ8,1315
deepspeed/moe/layer.py,sha256=TayzIRGqEOakrq4TthCiBpqdwwdm5sGqISWMZG13KMs,6905
deepspeed/moe/mappings.py,sha256=A9v0rdLw8GBc9ltSl6yhL518etT2gmkdx4upUVNAr5k,3691
deepspeed/moe/sharded_moe.py,sha256=JYzHU9tAonrnvPsMZ4DD_z2Ic0eG9Mm3A7by60hEfNw,23011
deepspeed/moe/utils.py,sha256=YGnqMJD26e2q3ezp-A0srHyb2sdWaHUEg1ld_I6qPgU,6699
deepspeed/monitor/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/monitor/__pycache__/__init__.cpython-310.pyc,,
deepspeed/monitor/__pycache__/config.cpython-310.pyc,,
deepspeed/monitor/__pycache__/csv_monitor.cpython-310.pyc,,
deepspeed/monitor/__pycache__/monitor.cpython-310.pyc,,
deepspeed/monitor/__pycache__/tensorboard.cpython-310.pyc,,
deepspeed/monitor/__pycache__/utils.cpython-310.pyc,,
deepspeed/monitor/__pycache__/wandb.cpython-310.pyc,,
deepspeed/monitor/config.py,sha256=pMEaW2VCbZFtqVmAy567HOzbgdhEftSGtCKo_Osh9DI,2498
deepspeed/monitor/csv_monitor.py,sha256=E9bWWx7QKhex8GWOxMOLjOsrAyBmXqCkaxNUyVirSY0,2907
deepspeed/monitor/monitor.py,sha256=hGvYlT3zTG7OTdxUT8AvPOxahvq074eWYoDEYxMT1PM,1604
deepspeed/monitor/tensorboard.py,sha256=9crE_YD9adHS65QmRxAuSuezRQUpL_VHrJMZmPhmuiE,2227
deepspeed/monitor/utils.py,sha256=mUyOH9IYfzaogOYdnaxFEVBW1ySuUBFiOOKu_p_F09U,784
deepspeed/monitor/wandb.py,sha256=DgdWuMEkJXDiTS4ZAWz_UaOP8WFSUOjhayUTl8q3DhU,1150
deepspeed/nebula/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/nebula/__pycache__/__init__.cpython-310.pyc,,
deepspeed/nebula/__pycache__/config.cpython-310.pyc,,
deepspeed/nebula/__pycache__/constants.cpython-310.pyc,,
deepspeed/nebula/config.py,sha256=THO1Mwto2utxnBWRfn0vIVWevOpT2EPFJFZkDCJF-ck,1764
deepspeed/nebula/constants.py,sha256=0HQOkViV_lRv13vtczIhBJG_YAH876vhh0R4BqHAj68,2786
deepspeed/ops/__init__.py,sha256=-zujTPLiVT1C_1Sqh2huE9uvzIxzT7gVVDWJp34uHIc,381
deepspeed/ops/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/adagrad/__init__.py,sha256=aJHYZSC21yulH09u72ZYAe43nLgeMemqvmC9wNNh9CQ,141
deepspeed/ops/adagrad/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/adagrad/__pycache__/cpu_adagrad.cpython-310.pyc,,
deepspeed/ops/adagrad/cpu_adagrad.py,sha256=6C4WoAPbLe7esT8Ite1YDgpk-qtFo1bYfvZv9ybKFIs,5089
deepspeed/ops/adam/__init__.py,sha256=ZVagnjkzHVw4akcDibUCfsArTcu8J9wsz9xKmbrJnIs,169
deepspeed/ops/adam/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/adam/__pycache__/cpu_adam.cpython-310.pyc,,
deepspeed/ops/adam/__pycache__/fused_adam.cpython-310.pyc,,
deepspeed/ops/adam/__pycache__/multi_tensor_apply.cpython-310.pyc,,
deepspeed/ops/adam/cpu_adam.py,sha256=pRJKMXtrRfnNfIKAksAlDHWhBFYcmYESxdkO28jHPMw,8534
deepspeed/ops/adam/fused_adam.py,sha256=5_JBk_UvBFcnOL0_hPr5SVFATmmmKHFIlVSR1am-sBc,8767
deepspeed/ops/adam/multi_tensor_apply.py,sha256=APt3UCnfw-nLjJOGWXC3izWRXNu-16TYhB-s213DpM0,429
deepspeed/ops/aio/__init__.py,sha256=dHc8QXzdN4Cw0D7px-gbO_fzV-lKT21ySZJ67LtUJuI,136
deepspeed/ops/aio/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/comm/deepspeed_not_implemented_op.cpython-310-darwin.so,sha256=sFhnKd9bKFB18bk3QV15hu-pQB9MGmm6qlLBJoVwkvM,4152
deepspeed/ops/csrc/adagrad/cpu_adagrad.cpp,sha256=EzyqrGziDv2r76SxEtVNzIpp_V-jKFgq1lThAC5Mm5k,9108
deepspeed/ops/csrc/adam/cpu_adam.cpp,sha256=bDny-11rq18NQoRXkW62zQYTnCCCoqiXPTot3TeK3fI,535
deepspeed/ops/csrc/adam/cpu_adam_impl.cpp,sha256=4kvJolXsOi6DnsYuS8J2x9_hEGhgaSiJ2aUjZO8DNPQ,10655
deepspeed/ops/csrc/adam/fused_adam_frontend.cpp,sha256=tu6pBcAvqcdh1ti6SB7fj8HnqFDQg3Rqbaa4xsQ2v7M,880
deepspeed/ops/csrc/adam/multi_tensor_adam.cu,sha256=13ixr7zAf-vH8NzAPKYiKXPmVRgsJxILT6cj75_92j8,8417
deepspeed/ops/csrc/adam/multi_tensor_apply.cuh,sha256=tcBmATZa_Et2VH9hnq5Jf6LJNnLaWDNrSqHdRr2yCE4,5609
deepspeed/ops/csrc/aio/common/deepspeed_aio_common.cpp,sha256=UMllIX03ZJA7Hv0sVyp2MLGdE85cuuI8EWAws09I8as,13406
deepspeed/ops/csrc/aio/common/deepspeed_aio_common.h,sha256=otOH-e8m43YmLoATqTDZy8fI9MzHpM_cr4PIoA8by-A,1364
deepspeed/ops/csrc/aio/common/deepspeed_aio_types.cpp,sha256=vrR8UQ9EK-8EgHBUyQIMgPLxNSFtwNUQVeoClggfPnQ,2033
deepspeed/ops/csrc/aio/common/deepspeed_aio_types.h,sha256=pnFoIKThq3P_bHoo-jxZgL86SMbXlJZGfpghK7pQqBg,1402
deepspeed/ops/csrc/aio/common/deepspeed_aio_utils.cpp,sha256=sT1zpUXoVe7v1hQS1BIfFqRFobBg22yfw98YW7UNrUo,4330
deepspeed/ops/csrc/aio/common/deepspeed_aio_utils.h,sha256=8t9Sj0hxme2D9lYkkelZLC6H1Vq-8d1GxDIWX98OGhM,2086
deepspeed/ops/csrc/aio/py_lib/deepspeed_aio_thread.cpp,sha256=NdbibH_mRTBEASvkHn96ZVcI99cnzEuKhejWXwBzzXg,3316
deepspeed/ops/csrc/aio/py_lib/deepspeed_aio_thread.h,sha256=EQJggDpuTyIk-RNw4vPzxtT3U67dx9gUa_5l8a03ygQ,1427
deepspeed/ops/csrc/aio/py_lib/deepspeed_pin_tensor.cpp,sha256=dYGOAYk1mZRNwYoc_ARUm052Hpj7nBTL3Ve3SXLsIQk,1240
deepspeed/ops/csrc/aio/py_lib/deepspeed_pin_tensor.h,sha256=j5mJ2HevYsjg6jCmxvn1Sg8-TnvnMTOa8f9q0QYKWvw,722
deepspeed/ops/csrc/aio/py_lib/deepspeed_py_aio.cpp,sha256=F9agfNs8bIEMUcirzAL_q5_Jv-Xqd-H9FKfcOk3GTaM,4326
deepspeed/ops/csrc/aio/py_lib/deepspeed_py_aio.h,sha256=Bk5OV7sf5C410s9mi_zUOdPWw9i2tSVCYUpKwbhsWxM,1052
deepspeed/ops/csrc/aio/py_lib/deepspeed_py_aio_handle.cpp,sha256=Nh0tz4VhJ1LfofDtcMPzLlsr6KK20qp8ePpD6Voftvw,10113
deepspeed/ops/csrc/aio/py_lib/deepspeed_py_aio_handle.h,sha256=ePRbzA9PjWRDjT1SbMTzAhx8UX5AYmNml7IeXPq6bBQ,2459
deepspeed/ops/csrc/aio/py_lib/deepspeed_py_copy.cpp,sha256=d2aL7n0MFzFbssjz85YTcnAHA0l2rn4EPpDhdk5FISw,4422
deepspeed/ops/csrc/aio/py_lib/deepspeed_py_copy.h,sha256=wKacjqIxo_umXV-8ySqieZLMtJHQNkbuGA8JG-gCzOI,1246
deepspeed/ops/csrc/aio/py_lib/py_ds_aio.cpp,sha256=6qeD9qdFq7b9hZIVODHWRtwZpG5purlsl8P1WoZEsbU,1805
deepspeed/ops/csrc/aio/py_test/single_process_config.json,sha256=onGthM2MLfZLjHehrPhjnNs-t3bXR_qXHWJ2wc6m1FE,359
deepspeed/ops/csrc/common/custom_cuda_kernel.cu,sha256=Y0uyZz5CfsWf2_aOCDeJM8-4r9IPwn_iskqJs5s0tDA,1278
deepspeed/ops/csrc/cpu/adam/fused_adam.cpp,sha256=m45BQnPCTkrYHiYcdKNa791zqVqtbdwia4TFoXjnOK0,1433
deepspeed/ops/csrc/cpu/comm/ccl.cpp,sha256=1U2qIchXfW7ypHD1DIG7mjPXR2levhB55Jz00Kv5Hnk,12342
deepspeed/ops/csrc/cpu/comm/shm.cpp,sha256=xM1APmdqK_N-Je2o94I4H7IBWij345ZA2tv-9pD5FJQ,25820
deepspeed/ops/csrc/cpu/comm/shm.h,sha256=iM952l2aMWnNP7uI44blK4b8sS80Am756zWpjhd7K3o,401
deepspeed/ops/csrc/cpu/comm/shm_interface.cpp,sha256=tfN4o6-fCHUJfyesfEQgGrN6xUvmtK6cDQi9_5hJOVE,3726
deepspeed/ops/csrc/cpu/lion/fused_lion.cpp,sha256=-_QUZhwjkjsePW-YyZ4ishpUPCGmbgfDp4C2jNgRIA0,1230
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/attention.cpp,sha256=1d5msQb6IiUdDeAYpzsiKjFHEFPHNAvBXDkO-aY2W_g,2095
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/attention_back.cu,sha256=10fKkFtrljPwiHjIAhoZJrBEcIRjdBP8JDagnFdWyOU,9971
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/attention_cu.cu,sha256=lfsGocib3dneilmyTKnEL9fdmALP8v_UhvwApmwgpVM,7509
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/epilogue/epilogue_grad_bias.h,sha256=ziZZar1z41wsr6ia5HtX8aAjpAuomtq5cCgZMzYylWI,12185
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/epilogue/epilogue_pipelined.h,sha256=RvYmxAW4NN7kMc9pLfEmkXRO-nXMKR1BX3fhhRUG3SI,24898
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/epilogue/epilogue_rescale_output.h,sha256=WWnsDQeR-XHcjKeEZixxhaTkv89tpyc1jaXoSAEiWt8,9606
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/epilogue/epilogue_thread_apply_logsumexp.h,sha256=Q_QP5LK3AwDtt6GHxLdUg-ttVqtDkxN2gv4bXiDnlPw,6392
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/gemm/custom_mma.h,sha256=rXA6J4g_TuQ1Ufm2c0Q2dMcuC1FV6X4MpkYpk7TeYp0,6270
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/gemm/custom_mma_base.h,sha256=zn3uylR7vb9GNLKG3UjodjXyPO_XpIuK0bRBwILm4zo,6511
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/gemm/custom_mma_multistage.h,sha256=WeVgJF-IIsVjd50TwDsL_oKwr2UMYUWdLEdhOiy_bfE,30281
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/gemm/custom_mma_pipelined.h,sha256=7MyvAD-eXwtNkfyjtd459IvbAbhW81Ys1RzFN8TasTM,15770
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/gemm/find_default_mma.h,sha256=l0wa_nFB9Vrz11bR_0lH-D4vaK-sWfuHm31Mg_Em3bc,9420
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/gemm/mma_accum_lambda_iterator.h,sha256=G6LeFpLrmBPVVsnfiYCPjh0h4VdJ5su6jyYPLvPymqI,16280
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/gemm/mma_from_smem.h,sha256=o_6L3rJlRtGmughlt9g0xLCzvTqBbUObl72lNxkbhNY,84216
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/gemm_kernel_utils.h,sha256=Evs8pB0tze8xOovv8Yv4pqBwxe-qxcncMkNztI1OuS4,11615
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/iterators/epilogue_predicated_tile_iterator.h,sha256=Vl83Qgukp3lN0EwAkZdftc1gxIfvwvFzjRw7M8wSQ9A,27084
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/iterators/make_residual_last.h,sha256=bl7iRKlZYnjqYwoST7SRHWwqygjsvYCUmLMBfbAxV7Q,4245
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/iterators/predicated_tile_access_iterator_residual_last.h,sha256=Jg4qjZ0ukBy2ZIZ-2wxZ9u4mrIcdVAcil56e7CdCY8M,71866
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/iterators/predicated_tile_iterator_atomic.h,sha256=RQ1sW5cWwiPveRxIs8rVuOgaXBBhsgHU5mbiLh6rawc,31326
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/iterators/predicated_tile_iterator_residual_last.h,sha256=aZy_jvL9zakb9oeFXkoihOabhLLhZjRSbLkf_6lUwS8,71626
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/iterators/transpose_warp_iterator.h,sha256=PtMp-E5HhdBmwgRiEbE2ZjlVL-b97euA80waCNO7jYQ,2536
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/iterators/warp_iterator_from_smem.h,sha256=QvDXwcGimdRqSoaGSzN5aS3q37VSXeihlU_A6ZPee9M,10125
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/kernel_backward.h,sha256=lLlud-SKkjuOa9UcJkOUfSZRV3RQpPRf9J6qMtAODBQ,95042
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/kernel_forward.h,sha256=CTv6YV9cCkmzU7w49AqRhENGWOccHYz0d4YzmRhvVBM,47657
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/transform/bias_broadcast.h,sha256=7yLAnvgViEcY5qls5eEJyfQDNkToG0TfPKQhm8Qi0-E,5952
deepspeed/ops/csrc/deepspeed4science/evoformer_attn/transform/tile_smem_loader.h,sha256=Dr_uSpu3dH8GAfChWggDeon9rn7F4G8BFrjm5_Xyt0E,4476
deepspeed/ops/csrc/fp_quantizer/includes/context.h,sha256=n-WCCbWIdbfNBCeS8c1wdY8LNdGIkz8qwn8C9ImX_nM,1427
deepspeed/ops/csrc/fp_quantizer/includes/quantize.h,sha256=zVd18bOEmCT83K_w2Rj6wr1C3Flx2C9voABpnLx9Dxs,5950
deepspeed/ops/csrc/fp_quantizer/quantize.cpp,sha256=b1PN0COC626tBoqqkj7T3eWKyGYiTE8O-anffHJ5VeE,5576
deepspeed/ops/csrc/fp_quantizer/quantize.cu,sha256=bA4h1SdGiohObtbnvWp0QLE7hGGHcAnUsxTy3Cv_pQ4,25597
deepspeed/ops/csrc/includes/StopWatch.h,sha256=DocXNohnOPWiZBqfrdI6AKV9IggYRB1Bylzxfke_i5s,1981
deepspeed/ops/csrc/includes/Timer.h,sha256=4mYxcATDJElSy5GCj_xFDZ2AvHHmMrhJlW0jJbMP7nQ,1180
deepspeed/ops/csrc/includes/activation_type.h,sha256=WkFfkL5mgLyYAZimLUouThCkyA2aiF9LQlh1q206L9o,264
deepspeed/ops/csrc/includes/compat.h,sha256=5ivuTerIqiCaYu2HpRPzJcUtqMsAtRB7aOXel69HeZQ,336
deepspeed/ops/csrc/includes/context.h,sha256=Cs6hRXBiFSjpKKQOAMCX9F0uP_Hh1IeZLloNJxHxk1E,7207
deepspeed/ops/csrc/includes/conversion_utils.h,sha256=augq8Zf-t46MaxWh0ryFb46_Guxud0k4kIK-h93rI8Y,12379
deepspeed/ops/csrc/includes/cpu_adagrad.h,sha256=A95TmBq4sB7R45pVc4M5URd_n-KRRQ1ScWROqeUOra8,7091
deepspeed/ops/csrc/includes/cpu_adam.h,sha256=Surj-3wGenseYkuDwz7FAIU79rt1AqebP1tF0f-liFM,11096
deepspeed/ops/csrc/includes/cpu_lion.h,sha256=zhz1VmhzZ7y54SD5slN-nPC71onAJ5sqnJ2-2lw7mWw,9130
deepspeed/ops/csrc/includes/cublas_wrappers.h,sha256=7X3R1jZFKpnzWUAqdns8FxMAqIHWes1X9nWRwiGpDlY,3341
deepspeed/ops/csrc/includes/custom_cuda_layers.h,sha256=XRAzBW3TP0T74eFm_yEn9-JracTbiEB1vuPtJ6YbxpA,13156
deepspeed/ops/csrc/includes/dequantization_utils.h,sha256=uFpPmBIYd3gNkKXImQLidYnGDYYTXkpY9V4o-AkdYpk,7301
deepspeed/ops/csrc/includes/dropout.h,sha256=l5JuSzyfxRsfufBKIebPwpghnmYR_E6ucCmGbLvZ1qc,2195
deepspeed/ops/csrc/includes/ds_kernel_utils.h,sha256=SNb_fMME8CA5YI1ws4kCW99jR4S2ccQqIgTA7ymmttQ,1280
deepspeed/ops/csrc/includes/ds_transformer_cuda.h,sha256=OwVCwN4tS1kMEeF9J6QV9GG8AvtYklaHRJrMPmji-zY,6164
deepspeed/ops/csrc/includes/feed_forward.h,sha256=IgzSMREF1do6A8pvcJfICvM5ShN2AB7zCoj5w3p9wxw,3189
deepspeed/ops/csrc/includes/gelu.h,sha256=iCsuAdNFzhLeccvc5D7KnsAcOcvXN_QD4jlaFRPF340,1018
deepspeed/ops/csrc/includes/gemm_test.h,sha256=HzdC86ZxaCF6FvedU4ZFb77rbYvQkYVfW-5vkvsPZWI,10390
deepspeed/ops/csrc/includes/general_kernels.h,sha256=0ksHr78VwjPiAfYPAloHM5uMxriRfNPEZyk9OklLhhI,1507
deepspeed/ops/csrc/includes/memory_access_utils.h,sha256=Lhpfu-OXn3TscCXK6VdWr8SEp-9rFrtnThrQGDU6aQI,34901
deepspeed/ops/csrc/includes/normalize_layer.h,sha256=j2lJkrxENFILqdUpEZIMj1qGrFty5SNoAeUrifThvSI,7089
deepspeed/ops/csrc/includes/quantization.h,sha256=tCPid1R06_cSWtsmq1dEjuIsLn70MCQqPzj01F04vFw,4380
deepspeed/ops/csrc/includes/quantization_utils.h,sha256=_pa6UHLgvDlXgVOfBgby1fHDi-IPORXwdvBX5rbGz88,17370
deepspeed/ops/csrc/includes/quantizer.h,sha256=YIRNwwr1eZYMmXbs6AZT82shpXuWbn94P4OVMcA3tss,346
deepspeed/ops/csrc/includes/reduction_utils.h,sha256=AeQx6X3WD4vExcIQRO9LuHdCAWa5vLBcf2QmcIDgePM,23380
deepspeed/ops/csrc/includes/simd.h,sha256=vnHiRltNIBRAEqAjv3DJbKhFNA6PE1-dPlK0OFhS7Iw,6601
deepspeed/ops/csrc/includes/softmax.h,sha256=UGZHziAfbGN6OdTIA24G5CWHhV8hAfQbq16NN2_0Wlo,1642
deepspeed/ops/csrc/includes/strided_batch_gemm.h,sha256=cJioBJNNFb00CApaAKWHossqnb13K4m9rKG6hEEZWxM,6807
deepspeed/ops/csrc/includes/type_shim.h,sha256=8VuX9zK5EsmleO7wuAq9D1eHZC7zKWaf0N97eaGg7x8,6388
deepspeed/ops/csrc/lamb/fused_lamb_cuda.cpp,sha256=icBfzI6QpN8LqIZT4-AY91UXlg_zDqkptNZy10erbws,3995
deepspeed/ops/csrc/lamb/fused_lamb_cuda_kernel.cu,sha256=vjhQw1pj6v19tVE2otOCSStk53Ss0R82g9o1lXfJ5vw,15291
deepspeed/ops/csrc/lion/cpu_lion.cpp,sha256=8BSc1jb7WaVOD1DMwLx-B-W4VQq53bth7s4nWieqvMU,535
deepspeed/ops/csrc/lion/cpu_lion_impl.cpp,sha256=k2MeaGc9bABgXYkB3q2QPXbqURBWqWqK6n3Kv0WWkE4,9123
deepspeed/ops/csrc/lion/fused_lion_frontend.cpp,sha256=RMiEOozd9gjXPidc4Ykg-kDIJek0OOASFVwmLIfFs0M,732
deepspeed/ops/csrc/lion/multi_tensor_apply.cuh,sha256=tMXW3UwsOhNM4J1axQbNkn4gNfmig7_vXPiBLdFCCv4,5595
deepspeed/ops/csrc/lion/multi_tensor_lion.cu,sha256=11RptKP9XmwMuvGBrmq3P1T8PAH9IvBJirB8gInMVoQ,4377
deepspeed/ops/csrc/quantization/dequantize.cu,sha256=iibytiJox1QcqE8b57KCLPOaD610STv2VUmKoC7RVM8,3302
deepspeed/ops/csrc/quantization/fake_quantizer.cu,sha256=CPMZnjOjJTKFibCBeYG1WrXnwEG-0l6POuZCRsBrHBo,37581
deepspeed/ops/csrc/quantization/pt_binding.cpp,sha256=NGZfFHg3q4swpD4e2AZh5WPxlK159cnxZcOdOHf3cN0,12374
deepspeed/ops/csrc/quantization/quant_reduce.cu,sha256=ona0QemL3ZGoiRlXvfkkP4oTpRoG_WvB81jX0pi8K2c,11311
deepspeed/ops/csrc/quantization/quantize.cu,sha256=IzmI5sq3V8B3tF92i84wlouV186djjVvdISREcepYRc,6988
deepspeed/ops/csrc/quantization/quantize_intX.cu,sha256=SIEWB2qHtFkHINoTTNukiJnXnQS06BKVH3Wjtg-im1k,8514
deepspeed/ops/csrc/quantization/swizzled_quantize.cu,sha256=XPcFFPzml6yWQudLhCbPlTBrx2Gef7HjySytUwJKvJs,8461
deepspeed/ops/csrc/random_ltd/gather_scatter.cu,sha256=YFiLiYlgkq1RNYS8yRNI3CKYUrv_8ht0lsvZsUcsUYg,8408
deepspeed/ops/csrc/random_ltd/pt_binding.cpp,sha256=7NAanMKS-j_hBputc6Ltv1yH6-c7xLwhDZJUfLiH--M,9590
deepspeed/ops/csrc/random_ltd/slice_attn_masks.cu,sha256=KqANgsPTAAHyys0cPh3023JaBF8b0kOKF-W13W39Q_4,5076
deepspeed/ops/csrc/random_ltd/token_sort.cu,sha256=yJTRkhVI-S_fT9ikw3VIFsEV6Gl0LxxtDI5oRls6dhU,7027
deepspeed/ops/csrc/sparse_attention/utils.cpp,sha256=HmNSJfE5WzsE3dYSWjOKOAxawUOcgZ2JwQpJSjvB3b4,4523
deepspeed/ops/csrc/spatial/csrc/opt_bias_add.cu,sha256=8N-FAKh-6Fwu7byz6e_sjXpd8XCRwdFntbZzZL6H8x8,6273
deepspeed/ops/csrc/spatial/csrc/pt_binding.cpp,sha256=K2zwA7EXdtjM05P9Bl8YV_p35TQXrt4xrADEkq44Dlo,3863
deepspeed/ops/csrc/spatial/includes/spatial_cuda_layers.h,sha256=dPh5gJbsCh2gB4_g9PSzMb1zN6LJvAGLfD7Vw_08_mc,915
deepspeed/ops/csrc/transformer/cublas_wrappers.cu,sha256=Zt4FUGO24jjTQ5XvE7YV7H05pzOwe0smKPQIMmA8Y9g,17572
deepspeed/ops/csrc/transformer/dropout_kernels.cu,sha256=u6U7RuMioRS_aixeRL0aGvU3r7exZ-EFeE7Etgg66IM,29835
deepspeed/ops/csrc/transformer/ds_transformer_cuda.cpp,sha256=sztj1XsEX1rbeq2lovNbYf_jZ-ExLL9qB-TLVRP_1D4,47582
deepspeed/ops/csrc/transformer/gelu_kernels.cu,sha256=f1emIwHWorXkuIqaB9kD93L4HjzSCcQ2JL7GlE6hh6s,12191
deepspeed/ops/csrc/transformer/general_kernels.cu,sha256=TzoOK2TqstG0MX9swk0UptK2zBgkpYwo99PeY9j0Fns,14520
deepspeed/ops/csrc/transformer/inference/csrc/apply_rotary_pos_emb.cu,sha256=jR_GJ9r9T90UAscJKFkklP8vzWKZ433gsJBsZUp5HB0,8558
deepspeed/ops/csrc/transformer/inference/csrc/dequantize.cu,sha256=z7GzRO9XUJFb0DNPr7luWML9cJTYOZrR5wAQGeuStqY,4916
deepspeed/ops/csrc/transformer/inference/csrc/gelu.cu,sha256=uB5npZROyc4mk1yjtipCo2OurQsZvcsfMP1ey9nfVuo,28649
deepspeed/ops/csrc/transformer/inference/csrc/layer_norm.cu,sha256=79gLSVFW19gsgFQNezyITbBImha7_oXJo5NfBloOhzU,20883
deepspeed/ops/csrc/transformer/inference/csrc/pointwise_ops.cu,sha256=tJP7V172fEHn86xtO1H8lKzBS7CToDNofJBnOVhivEA,2476
deepspeed/ops/csrc/transformer/inference/csrc/pt_binding.cpp,sha256=_C-hVbsnW55taSjiY5XacU_DCGq13kkLXWB94AqtIEM,87552
deepspeed/ops/csrc/transformer/inference/csrc/relu.cu,sha256=F_W6D8eeHtm_btn-FTyAV8FnLTDJ9H8mpkDBWpmxxDc,2318
deepspeed/ops/csrc/transformer/inference/csrc/rms_norm.cu,sha256=0vHKVYfQQ8w-qLUD-ihfCd0jWDQ_N03QuQu0rimHrdU,10251
deepspeed/ops/csrc/transformer/inference/csrc/softmax.cu,sha256=GY3hRUkY59BcQqe2BA0QCGw3tlN2UO4GQipRezSzJLM,27245
deepspeed/ops/csrc/transformer/inference/csrc/transform.cu,sha256=R0PAaY2hEyAq9_xMYDrsHRgzIXznZuI48jgU4tDtm_w,31659
deepspeed/ops/csrc/transformer/inference/includes/inference_context.h,sha256=2-KQOUFuWdx1rOLR3rZtTmOpz9LUmY9Vs7a5qNybUzI,10531
deepspeed/ops/csrc/transformer/inference/includes/inference_cublas_wrappers.h,sha256=K1U00bmeKbeLzmAlnBGoJegOZsAulJ1vlL90IEAEvOs,18517
deepspeed/ops/csrc/transformer/inference/includes/inference_cuda_layers.h,sha256=TlXSYeyoKSWBaxuN_dDxrTMhutjQocPjKeGL_NPXIPo,9057
deepspeed/ops/csrc/transformer/normalize_kernels.cu,sha256=u9fnXA77eqpoC1hri_w0IzC0xW7_OIG5Cs6F_UFnivs,74900
deepspeed/ops/csrc/transformer/softmax_kernels.cu,sha256=GKmqpxpbAFlhM2p1SFMZ02phEpYMSGUFA2J9ukLovno,26758
deepspeed/ops/csrc/transformer/transform_kernels.cu,sha256=Ob-Hae1q-KZmDxaji0Ci0nHyNuRYDvPFBF_yrnB8ZoE,22709
deepspeed/ops/csrc/utils/flatten_unflatten.cpp,sha256=RdtrXrJ_BiE-p3nygS3JW_vhzBGCHc5_CL7KY7jY1e8,788
deepspeed/ops/csrc/xpu/adagrad/cpu_adagrad.cpp,sha256=-LiTifCTt4Pr_4miMyP5-8W84FHhORWZn8mx-VZDR58,6814
deepspeed/ops/csrc/xpu/adam/cpu_adam.cpp,sha256=bDny-11rq18NQoRXkW62zQYTnCCCoqiXPTot3TeK3fI,535
deepspeed/ops/csrc/xpu/adam/cpu_adam_impl.cpp,sha256=WaviJDpZ1fwVsXNpAF3hmvtJ5eVSaPj1i816i6eNW24,8189
deepspeed/ops/csrc/xpu/adam/fused_adam_frontend.cpp,sha256=tu6pBcAvqcdh1ti6SB7fj8HnqFDQg3Rqbaa4xsQ2v7M,880
deepspeed/ops/csrc/xpu/adam/multi_tensor_adam.dp.cpp,sha256=Du_BWrHEoYqoCfDb6qKxDeKXP69ZfdFEW1mt7ir5ah0,6595
deepspeed/ops/csrc/xpu/common/custom_cuda_kernel.dp.cpp,sha256=fSy_UoyFgDlpKWEDRtUqwrhaLMMUV1OG7eCVZfzcGKs,3525
deepspeed/ops/csrc/xpu/includes/compat.h,sha256=5ivuTerIqiCaYu2HpRPzJcUtqMsAtRB7aOXel69HeZQ,336
deepspeed/ops/csrc/xpu/includes/cpu_adagrad.h,sha256=7yZNV7T38Yvnv2JLfcdm4KdBuXyPdvT-7Plzva_5BbQ,3951
deepspeed/ops/csrc/xpu/includes/cpu_adam.h,sha256=3a0twZXzUUnAhrEymYF8gKFoVW594B-I-Snjg6paK9Q,7956
deepspeed/ops/csrc/xpu/includes/simd.h,sha256=8wzoQAfIKLyUi-1V7o_kX3e1Hy3ye5QfH-XOz3dlDP8,6586
deepspeed/ops/csrc/xpu/includes/type_shim.h,sha256=hu3RINbuYEW4G4tTdiC9KifB0KdYzQVQYmXO4Buntis,7779
deepspeed/ops/deepspeed4science/__init__.py,sha256=LRmYuJYPcnqiyIuSCwsvqpD61wcScSmS7An8zq0UY4M,175
deepspeed/ops/deepspeed4science/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/deepspeed4science/__pycache__/evoformer_attn.cpython-310.pyc,,
deepspeed/ops/deepspeed4science/evoformer_attn.py,sha256=50z8I1w4lJww3bRiBxq8PfEJxcacZw_qXk4DyTyWsSQ,4121
deepspeed/ops/fp_quantizer/__init__.py,sha256=CTfnnp6rDnlluk7jaH9OgnUGrDs7d0oKgs8DZ3N2f08,141
deepspeed/ops/fp_quantizer/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/fp_quantizer/__pycache__/quantize.cpython-310.pyc,,
deepspeed/ops/fp_quantizer/quantize.py,sha256=Ql7fDenreEqYJIg2n0diM5lrvQCTvp6r689IuImGtJ0,5312
deepspeed/ops/lamb/__init__.py,sha256=HG9WeYIi4tTtKZBuLuJNIVpLSK75djGxjXDkrgfreEk,130
deepspeed/ops/lamb/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/lamb/__pycache__/fused_lamb.cpython-310.pyc,,
deepspeed/ops/lamb/fused_lamb.py,sha256=gZbjO38b517Ii1tEJwT0O81YwJ5bXtflM2uq57NFXtI,7815
deepspeed/ops/lion/__init__.py,sha256=EFF2vXYA9hYPNuqBsLsd1MTFCPiOFfW1ZWBrlbxfbD8,169
deepspeed/ops/lion/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/lion/__pycache__/cpu_lion.cpython-310.pyc,,
deepspeed/ops/lion/__pycache__/fused_lion.cpython-310.pyc,,
deepspeed/ops/lion/__pycache__/multi_tensor_apply.cpython-310.pyc,,
deepspeed/ops/lion/cpu_lion.py,sha256=XOTMpFLCJ7tLmwFIIb6dsayTLKhJZriHAkIlzeMjGgU,6201
deepspeed/ops/lion/fused_lion.py,sha256=QoYzriRhYkaa_bDxIY_YGviCUe-BcNZvvNtFA4g5kLc,5548
deepspeed/ops/lion/multi_tensor_apply.py,sha256=APt3UCnfw-nLjJOGWXC3izWRXNu-16TYhB-s213DpM0,429
deepspeed/ops/op_builder/__init__.py,sha256=HJ00U1SrhiXDftTIq9k5hZp-CuRPZ5GWOyQerqgC21U,2005
deepspeed/ops/op_builder/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/all_ops.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/async_io.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/builder.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/cpu_adagrad.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/cpu_adam.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/cpu_lion.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/evoformer_attn.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/fp_quantizer.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/fused_adam.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/fused_lamb.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/fused_lion.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/inference_core_ops.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/inference_cutlass_builder.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/quantizer.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/ragged_ops.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/ragged_utils.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/random_ltd.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/sparse_attn.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/spatial_inference.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/stochastic_transformer.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/transformer.cpython-310.pyc,,
deepspeed/ops/op_builder/__pycache__/transformer_inference.cpython-310.pyc,,
deepspeed/ops/op_builder/all_ops.py,sha256=bymFEiEsBwsM_BlMOePCM-9xMGeQdk6u0ZXh2fPQo9w,1223
deepspeed/ops/op_builder/async_io.py,sha256=w_fGQCKTz8qD_1Z8b8XW5IjnoUlhc79Ui_sBrBkaD48,3661
deepspeed/ops/op_builder/builder.py,sha256=zYNP1bmMtiVlubmsa4pn0vrSL-J1tZuZh26pA3QEFRs,31271
deepspeed/ops/op_builder/cpu/__init__.py,sha256=t6TyDA2udB3qy2xqAS2Az9VQpWjSt_6mefm4ucsmUck,316
deepspeed/ops/op_builder/cpu/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/op_builder/cpu/__pycache__/builder.cpython-310.pyc,,
deepspeed/ops/op_builder/cpu/__pycache__/comm.cpython-310.pyc,,
deepspeed/ops/op_builder/cpu/__pycache__/cpu_adam.cpython-310.pyc,,
deepspeed/ops/op_builder/cpu/__pycache__/fused_adam.cpython-310.pyc,,
deepspeed/ops/op_builder/cpu/__pycache__/no_impl.cpython-310.pyc,,
deepspeed/ops/op_builder/cpu/builder.py,sha256=8XZWnoY1-ldVsE2N_Pyk6N9airTygcKjh-axw3cEn6o,1315
deepspeed/ops/op_builder/cpu/comm.py,sha256=vrOA7qgtsrA5pJUP0eNrDSDPM0F4uNsrRcugiNUuIRE,2112
deepspeed/ops/op_builder/cpu/cpu_adam.py,sha256=rGtsjwhzzP9iMDIphGxjh5TLUBtUiEwtrnf5x5BbG08,625
deepspeed/ops/op_builder/cpu/fused_adam.py,sha256=5LAQSEL2H4Bcc1b7LArqKCcF2Ztuq2uaABmRmI7m5Ss,546
deepspeed/ops/op_builder/cpu/no_impl.py,sha256=OIZ3aVWXK9W8iRtgNL0pAtl033WV0hEoOuPP35UuFUc,616
deepspeed/ops/op_builder/cpu_adagrad.py,sha256=OWfsh2f07IuRQdLgomSv7EevhAdqZ1TxDsEwifhrJDE,1151
deepspeed/ops/op_builder/cpu_adam.py,sha256=QlPrfAEmGq5_iSZiRnF_GZxzN8I45CH5znSmlSN3qtk,1190
deepspeed/ops/op_builder/cpu_lion.py,sha256=hEf4hIAxEzNN1beGGEF7BxrbzGLNb66TWVEAIUXRkts,1459
deepspeed/ops/op_builder/evoformer_attn.py,sha256=Qpyf6UeP1FmSVdxMgDUEEI0Xcdj8UrhwAiTbX25zRbs,2728
deepspeed/ops/op_builder/fp_quantizer.py,sha256=E2q-Nox-HdW7oHF9S6RHgT5st6p2aCqSVpnIK9ckRms,2145
deepspeed/ops/op_builder/fused_adam.py,sha256=IC87eCjvRPJ2ETVdQUv0-d_yxMXhDJvbPML1AEU9cSQ,1044
deepspeed/ops/op_builder/fused_lamb.py,sha256=Ojx9euSMUeeU7DVIFxE11w_fHERqYsZxNNtaGxnAAag,1216
deepspeed/ops/op_builder/fused_lion.py,sha256=1ZJh92_chzH0Jupt8RO8GUOnrm6ACKLECZ5mTiV7WI0,1044
deepspeed/ops/op_builder/hpu/__init__.py,sha256=fCgjMg2OsK3qA9Yg6waIA_YCcCzRYNIJiUyMCdrmuvQ,318
deepspeed/ops/op_builder/hpu/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/op_builder/hpu/__pycache__/builder.cpython-310.pyc,,
deepspeed/ops/op_builder/hpu/__pycache__/cpu_adam.cpython-310.pyc,,
deepspeed/ops/op_builder/hpu/__pycache__/fused_adam.cpython-310.pyc,,
deepspeed/ops/op_builder/hpu/__pycache__/no_impl.cpython-310.pyc,,
deepspeed/ops/op_builder/hpu/builder.py,sha256=2Haj41Q599cfD3J4kAawyAI734knv1hXf4fu1ZLjTbQ,1371
deepspeed/ops/op_builder/hpu/cpu_adam.py,sha256=vqGXAKkkORAcvFUWOvS0QCvL6EnIGvyfUrXdg-EulMc,798
deepspeed/ops/op_builder/hpu/fused_adam.py,sha256=uv1k6BHKwET0NNl2FqI5AOr6BL1rN7cA6-qRKuFEyVE,719
deepspeed/ops/op_builder/hpu/no_impl.py,sha256=cUJ87-4-I46CK3GmS-Ve52nzfMhcFayT_YZF5FWEmzY,616
deepspeed/ops/op_builder/inference_core_ops.py,sha256=4lIid7cf-N-AMvFT_BAcdPWla2vwACL-hVLJgerf9M8,3712
deepspeed/ops/op_builder/inference_cutlass_builder.py,sha256=IPAcnhCzztQR3t8Y-A9mWiW_wy7YgbfK5HAzs3DHoFg,3250
deepspeed/ops/op_builder/npu/__init__.py,sha256=utWarSvvEuL_LUk8-i4Kx2oCHJRemddoGeGfzHOCsTE,419
deepspeed/ops/op_builder/npu/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/op_builder/npu/__pycache__/async_io.cpython-310.pyc,,
deepspeed/ops/op_builder/npu/__pycache__/builder.cpython-310.pyc,,
deepspeed/ops/op_builder/npu/__pycache__/cpu_adagrad.cpython-310.pyc,,
deepspeed/ops/op_builder/npu/__pycache__/cpu_adam.cpython-310.pyc,,
deepspeed/ops/op_builder/npu/__pycache__/cpu_lion.cpython-310.pyc,,
deepspeed/ops/op_builder/npu/__pycache__/fused_adam.cpython-310.pyc,,
deepspeed/ops/op_builder/npu/__pycache__/inference.cpython-310.pyc,,
deepspeed/ops/op_builder/npu/__pycache__/no_impl.cpython-310.pyc,,
deepspeed/ops/op_builder/npu/async_io.py,sha256=lyBqA14EWTMRcuflHJuqun8KcFebD4u4RMRM1wGlm28,3814
deepspeed/ops/op_builder/npu/builder.py,sha256=k21-MqhnSCYyMpuOxbRjDWbsIcnDnA6GxWPtsmtBuC8,3200
deepspeed/ops/op_builder/npu/cpu_adagrad.py,sha256=CM4wGBKhOHayOyzvOGX2yDIh4PgOCu6AUHXF4GKq9kE,581
deepspeed/ops/op_builder/npu/cpu_adam.py,sha256=S5V76jfADBzNNP-CyBoTXZSWrQMLYWuos6ACygoumDE,594
deepspeed/ops/op_builder/npu/cpu_lion.py,sha256=rDkiA8vVCMolVFWUnvN9PkrLOJlc6P_rSRNYmOUrYV8,594
deepspeed/ops/op_builder/npu/fused_adam.py,sha256=be-Lvt8CA8gf5oLtcg9BDqTupJ7lHT5O8PBfoNvkTns,2130
deepspeed/ops/op_builder/npu/inference.py,sha256=-IsUpVW106uskkegCrYzyMoed5W-RhhffawBWsXYad8,15187
deepspeed/ops/op_builder/npu/no_impl.py,sha256=qZnbySBzMLLUrMlZV656Pdh8TGfEZKNACKdmD6uT9ho,755
deepspeed/ops/op_builder/quantizer.py,sha256=k0kTBN9_9Ri_dyzhW_ENxYpUVhiFOeHaIdVY1W50ZPE,1037
deepspeed/ops/op_builder/ragged_ops.py,sha256=sxNN18RvM7Lg6iz5uKlN4oHZHepPCnhhhBL1Bc3qr0g,4789
deepspeed/ops/op_builder/ragged_utils.py,sha256=Dw_6CzGGmp3I5fR1LdOHUlTBX6SiaWgX-Oo3e-TEsso,2604
deepspeed/ops/op_builder/random_ltd.py,sha256=zNBXnNjckT9dZqtj4pVYHcSUu4m2govven5qh5Qp9VU,879
deepspeed/ops/op_builder/sparse_attn.py,sha256=bUJa7uBe6ArfQe4MubDF6-n05ChVzk-PLsFY5Rn3PK4,2994
deepspeed/ops/op_builder/spatial_inference.py,sha256=qOtjRrjWLjzw0xu-lA0otlOwd_bdub76ZzvOUqyHQcY,1534
deepspeed/ops/op_builder/stochastic_transformer.py,sha256=nQPCaLrtuHVtJxMWIQp_8IT1iQ2XS9nQ1BJVwwAeIPI,565
deepspeed/ops/op_builder/transformer.py,sha256=wyrj-FZTKPyXzwKOGPYDyic3w5hdtXh4dWJS_wfRdCU,1094
deepspeed/ops/op_builder/transformer_inference.py,sha256=kGNIBTWER2h4EdUkRmfpsX4bx--fTiHywOVEgoMcYZY,2745
deepspeed/ops/op_builder/xpu/__init__.py,sha256=h2v1i-z4hyP8NGnyt0Fjjn_mX5p_iWC1euckaIoDcOo,254
deepspeed/ops/op_builder/xpu/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/op_builder/xpu/__pycache__/async_io.cpython-310.pyc,,
deepspeed/ops/op_builder/xpu/__pycache__/builder.cpython-310.pyc,,
deepspeed/ops/op_builder/xpu/__pycache__/cpu_adagrad.cpython-310.pyc,,
deepspeed/ops/op_builder/xpu/__pycache__/cpu_adam.cpython-310.pyc,,
deepspeed/ops/op_builder/xpu/__pycache__/fused_adam.cpython-310.pyc,,
deepspeed/ops/op_builder/xpu/async_io.py,sha256=Qy2AMkfPvyLw6J5JlReBt05j1iLwdxjHSKdLUjVH_Qg,3566
deepspeed/ops/op_builder/xpu/builder.py,sha256=Pul5G1bF8D5fb-RpXISLJhVoZyE5vH3GNeOA8qKW_NM,5816
deepspeed/ops/op_builder/xpu/cpu_adagrad.py,sha256=bi9U6d3UmHRYw7qkIcRNRYTuasPYoSe38ngF71ZM1RQ,576
deepspeed/ops/op_builder/xpu/cpu_adam.py,sha256=wPrCTl-ubqi_JPwKBMnCupqh4cEYmQFUAckqqCnxSh4,744
deepspeed/ops/op_builder/xpu/fused_adam.py,sha256=yEvt078k2gqqqDjPG_D5I5Ntuz_aAqEjYgtCgjpgAjg,701
deepspeed/ops/quantizer/__init__.py,sha256=5IdLoKmcCi6MuZNNTGADQs88dgcCUdN50WofVXDcvpI,132
deepspeed/ops/quantizer/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/quantizer/__pycache__/quantizer.cpython-310.pyc,,
deepspeed/ops/quantizer/quantizer.py,sha256=z3dh0MRUklHnR74y662k9H31FOMU0buQOGE9uPLcUZA,1193
deepspeed/ops/random_ltd/__init__.py,sha256=MC02456CIFtrgIYKClskYz4kDnQ9X7zRyyKNp110l58,191
deepspeed/ops/random_ltd/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/random_ltd/__pycache__/dropping_utils.cpython-310.pyc,,
deepspeed/ops/random_ltd/dropping_utils.py,sha256=tHMRvk6WIVDoPR58HWc0OwUNzXhfn8VYd7Xw13X-r5o,4902
deepspeed/ops/sparse_attention/__init__.py,sha256=qhwanCYxLO9eaH1cx52E4LNe_An8Nrja_8WyyoYk42I,467
deepspeed/ops/sparse_attention/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/sparse_attention/__pycache__/bert_sparse_self_attention.cpython-310.pyc,,
deepspeed/ops/sparse_attention/__pycache__/matmul.cpython-310.pyc,,
deepspeed/ops/sparse_attention/__pycache__/softmax.cpython-310.pyc,,
deepspeed/ops/sparse_attention/__pycache__/sparse_attention_utils.cpython-310.pyc,,
deepspeed/ops/sparse_attention/__pycache__/sparse_self_attention.cpython-310.pyc,,
deepspeed/ops/sparse_attention/__pycache__/sparsity_config.cpython-310.pyc,,
deepspeed/ops/sparse_attention/bert_sparse_self_attention.py,sha256=YfQPUqCgd3XmgT7HVb4wXkdZRDBfHinxx0mwQssILww,3463
deepspeed/ops/sparse_attention/matmul.py,sha256=RYTMwGqaYRUTR7DC60Ek7PNWUc5n9sKJNgcTpzmiZfo,32948
deepspeed/ops/sparse_attention/softmax.py,sha256=B_q4n2TP29iPfMLTnkRqgHxJ8y5cBjsTixPOO7zrrMw,11322
deepspeed/ops/sparse_attention/sparse_attention_utils.py,sha256=XlNgnI3CdG8Kur-Ac1O5qJVQRX_lgexTzXtfarMAHb4,12300
deepspeed/ops/sparse_attention/sparse_self_attention.py,sha256=Mgs_4NaoBoHIghomqaqnKtjbAgYs1xWlZOlZAoSaMao,6746
deepspeed/ops/sparse_attention/sparsity_config.py,sha256=eUCYxt5QPHWXdot-Myu206H9Y5WV8HrqxvO7LPKHHKg,42463
deepspeed/ops/sparse_attention/trsrc/__init__.py,sha256=2G0yT0H4-bH6beifS-DoGK8lQcfZ2aIqt7IFekVRxP0,1032
deepspeed/ops/sparse_attention/trsrc/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/sparse_attention/trsrc/matmul.tr,sha256=nM6iQGzBXyKFzHqmCDjpr8xmykwEls4Te6PWVJg22Dg,6628
deepspeed/ops/sparse_attention/trsrc/softmax_bwd.tr,sha256=C--cFHczPdwJA7wm0s66p9f_nkzDMatqu3-iBQoAlog,1923
deepspeed/ops/sparse_attention/trsrc/softmax_fwd.tr,sha256=t4bvel9w4ilPsn24DQgzIqGNG08Pt4pfCBFua8rj9fc,4047
deepspeed/ops/transformer/__init__.py,sha256=ADlxWPujgq5jT_XL8yuRtTg9jdQiBK-5ydzgnNwap-o,413
deepspeed/ops/transformer/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/transformer/__pycache__/transformer.cpython-310.pyc,,
deepspeed/ops/transformer/inference/__init__.py,sha256=ENKx17yjSD2dIqD6kPq-zxY-Xae-O84-ZPGGNeFq064,315
deepspeed/ops/transformer/inference/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/transformer/inference/__pycache__/bias_add.cpython-310.pyc,,
deepspeed/ops/transformer/inference/__pycache__/config.cpython-310.pyc,,
deepspeed/ops/transformer/inference/__pycache__/diffusers_2d_transformer.cpython-310.pyc,,
deepspeed/ops/transformer/inference/__pycache__/diffusers_attention.cpython-310.pyc,,
deepspeed/ops/transformer/inference/__pycache__/diffusers_transformer_block.cpython-310.pyc,,
deepspeed/ops/transformer/inference/__pycache__/ds_attention.cpython-310.pyc,,
deepspeed/ops/transformer/inference/__pycache__/ds_mlp.cpython-310.pyc,,
deepspeed/ops/transformer/inference/__pycache__/moe_inference.cpython-310.pyc,,
deepspeed/ops/transformer/inference/__pycache__/triton_ops.cpython-310.pyc,,
deepspeed/ops/transformer/inference/bias_add.py,sha256=x1gk_iN4pmFNsSt6jwtBXil-3no4yDhTMbi_6yrs7gY,876
deepspeed/ops/transformer/inference/config.py,sha256=pC5G7YBIXt63_wVWabGrqwHqNU5ur36qynwLqw1wnl8,6158
deepspeed/ops/transformer/inference/diffusers_2d_transformer.py,sha256=JVSE9B60n6XHeg37zE26W3-jzKOhxiOtJkmkCiM1nDU,236
deepspeed/ops/transformer/inference/diffusers_attention.py,sha256=PCWEGMJj4M74TGD-uRKUiTnk1Nl9gBRSNjRsP-2iaVU,9922
deepspeed/ops/transformer/inference/diffusers_transformer_block.py,sha256=sbwG5Dw6I-ePwBCY84S2dQE2u83NgEpky3S6s-MRdwE,4857
deepspeed/ops/transformer/inference/ds_attention.py,sha256=Td6oYBDum8296Lv2BOIiIszGwxcHq1cm70mVpRU6YIg,14521
deepspeed/ops/transformer/inference/ds_mlp.py,sha256=Lr5q6pk3MrAx8D36wXhTNDlV7MsFMkFFaHb7CiKzfzo,6294
deepspeed/ops/transformer/inference/moe_inference.py,sha256=_jmvwS6t1V6EftS2mRAnvUA_kKHRiNauCes56-w2_mI,18555
deepspeed/ops/transformer/inference/op_binding/__init__.py,sha256=JhdbkMuKVwM62BEU4aI4OscG3GGkxFKsvnttFx9vc9k,382
deepspeed/ops/transformer/inference/op_binding/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/transformer/inference/op_binding/__pycache__/base.cpython-310.pyc,,
deepspeed/ops/transformer/inference/op_binding/__pycache__/gelu_gemm.cpython-310.pyc,,
deepspeed/ops/transformer/inference/op_binding/__pycache__/linear.cpython-310.pyc,,
deepspeed/ops/transformer/inference/op_binding/__pycache__/mlp_gemm.cpython-310.pyc,,
deepspeed/ops/transformer/inference/op_binding/__pycache__/qkv_gemm.cpython-310.pyc,,
deepspeed/ops/transformer/inference/op_binding/__pycache__/residual_add.cpython-310.pyc,,
deepspeed/ops/transformer/inference/op_binding/__pycache__/softmax.cpython-310.pyc,,
deepspeed/ops/transformer/inference/op_binding/__pycache__/softmax_context.cpython-310.pyc,,
deepspeed/ops/transformer/inference/op_binding/__pycache__/vector_matmul.cpython-310.pyc,,
deepspeed/ops/transformer/inference/op_binding/base.py,sha256=YN1s4f-7BKDmvCyKkVW3DOaXz40Xxk8FrIeDU2BfmJA,536
deepspeed/ops/transformer/inference/op_binding/gelu_gemm.py,sha256=gY_aA6ni_ckPawkM-PHPeS2dvqcH3Vvarv8ao5sv3ug,1867
deepspeed/ops/transformer/inference/op_binding/linear.py,sha256=vIYuAmj9CX0KERm9_IE7pdmf4XG4UPXkanBqqtlnhgc,2719
deepspeed/ops/transformer/inference/op_binding/mlp_gemm.py,sha256=j8XYzjGoY0WL1bLxgksx9FQgDZencRfSIsnUK4Xngy8,4680
deepspeed/ops/transformer/inference/op_binding/qkv_gemm.py,sha256=hCtzmUtJxloLpOVgMM005wRt6MN3YYLnId04urbKGDs,4425
deepspeed/ops/transformer/inference/op_binding/residual_add.py,sha256=3_KAcHwhNCnPcH6wjds3tjzTB9pVGBdRCr8JI2z1wQM,2708
deepspeed/ops/transformer/inference/op_binding/softmax.py,sha256=QO9JLpF7VurtMMX6wNDqvBtVekh8AqrwMI2S586Q3lg,2460
deepspeed/ops/transformer/inference/op_binding/softmax_context.py,sha256=jCekTFEGhztLY7NjeavZmdQDefbixwuK1to86Opa7Vw,2238
deepspeed/ops/transformer/inference/op_binding/vector_matmul.py,sha256=NxbnOLMAouYkGjJc0R6-PmUmGQ11r64ioZG1Do57YPU,2750
deepspeed/ops/transformer/inference/triton/__init__.py,sha256=FVRc5cfjotwsFtcB7gkAm09lXFbNl01gaoet67x4B_k,350
deepspeed/ops/transformer/inference/triton/__pycache__/__init__.cpython-310.pyc,,
deepspeed/ops/transformer/inference/triton/__pycache__/attention.cpython-310.pyc,,
deepspeed/ops/transformer/inference/triton/__pycache__/gelu.cpython-310.pyc,,
deepspeed/ops/transformer/inference/triton/__pycache__/layer_norm.cpython-310.pyc,,
deepspeed/ops/transformer/inference/triton/__pycache__/matmul_ext.cpython-310.pyc,,
deepspeed/ops/transformer/inference/triton/__pycache__/mlp.cpython-310.pyc,,
deepspeed/ops/transformer/inference/triton/__pycache__/ops.cpython-310.pyc,,
deepspeed/ops/transformer/inference/triton/__pycache__/residual_add.cpython-310.pyc,,
deepspeed/ops/transformer/inference/triton/__pycache__/softmax.cpython-310.pyc,,
deepspeed/ops/transformer/inference/triton/__pycache__/triton_matmul_kernel.cpython-310.pyc,,
deepspeed/ops/transformer/inference/triton/attention.py,sha256=fZ8lN_UuLntv1fsSGItfRwIWKbemJeAAY8tDAkvVgo0,15922
deepspeed/ops/transformer/inference/triton/gelu.py,sha256=OAViD-qU6B50KhjQgg-Y3leuEKP6gW2OkX7MX_VGDko,1152
deepspeed/ops/transformer/inference/triton/layer_norm.py,sha256=q5Xt2ov4z5DonYRBnWevaUYTFwJU5qIrJc6RVDtLaPw,7512
deepspeed/ops/transformer/inference/triton/matmul_ext.py,sha256=zcJB-q7D-hB1L-hrvAhx3pMbVsg3rCbAML8SkcK20Rs,15897
deepspeed/ops/transformer/inference/triton/mlp.py,sha256=c4yRQv3GPzEKcaxMFwkms4q2eF47ewemkYcnuhu0iec,4225
deepspeed/ops/transformer/inference/triton/ops.py,sha256=KXcto03GshM-0UcYtRQK7Yo8LR68Um3LX1zpVoPvkw0,4125
deepspeed/ops/transformer/inference/triton/residual_add.py,sha256=PYcAlDC8yz00LzJ8eXsJKoMmxjD_nMM0X5PbK8w_J70,3118
deepspeed/ops/transformer/inference/triton/softmax.py,sha256=2q_qNmffFM4l2UgsAFKrJmAVrID95KK1rcg5dEzBmco,3208
deepspeed/ops/transformer/inference/triton/triton_matmul_kernel.py,sha256=8pa5U6ZJphYxJ3oX2cPdxdYilmuKf8w_lvt1A8-pjbg,12570
deepspeed/ops/transformer/inference/triton_ops.py,sha256=lR82VswoMZ9b2Axbck_eF5GCOczUmvKTDAT1W8bVH1w,5487
deepspeed/ops/transformer/transformer.py,sha256=1UMs9JNjlK3mD5PJi3iER9QARUO2_a5h0sZAY0Rmo24,20600
deepspeed/pipe/__init__.py,sha256=ddCnO6IbTpGzlAIgfEbGg6RPCz70HNN-MwTjCpTsqLI,164
deepspeed/pipe/__pycache__/__init__.cpython-310.pyc,,
deepspeed/profiling/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/profiling/__pycache__/__init__.cpython-310.pyc,,
deepspeed/profiling/__pycache__/config.cpython-310.pyc,,
deepspeed/profiling/__pycache__/constants.cpython-310.pyc,,
deepspeed/profiling/config.py,sha256=OzJuTtxVvm62T1iA5eXocvP8xQQXHNtzcCdqISLTTKM,1959
deepspeed/profiling/constants.py,sha256=QaZFzozkotv71aArhiwtQJ13DNDGDU1xC0Jm0KrVH08,1243
deepspeed/profiling/flops_profiler/__init__.py,sha256=NCZv_Ktz4sFmmfpBwGKVVbbKrI36VQHwPapvLlWUUxE,120
deepspeed/profiling/flops_profiler/__pycache__/__init__.cpython-310.pyc,,
deepspeed/profiling/flops_profiler/__pycache__/profiler.cpython-310.pyc,,
deepspeed/profiling/flops_profiler/profiler.py,sha256=wnOWearI4j6ormmB4QcpCfwED7w0xgl8atMYuDo637s,50350
deepspeed/pydantic_v1.py,sha256=p6m4eFg-1jIrEkzbpehZ-DFy2b_JvtZbuORSTnPWPYQ,547
deepspeed/runtime/__init__.py,sha256=4I9UpQ5vMRU5SYSF_dW9FJDEnBq4m_0SuwtVQ92lGaA,95
deepspeed/runtime/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/__pycache__/base_optimizer.cpython-310.pyc,,
deepspeed/runtime/__pycache__/bf16_optimizer.cpython-310.pyc,,
deepspeed/runtime/__pycache__/compiler.cpython-310.pyc,,
deepspeed/runtime/__pycache__/config.cpython-310.pyc,,
deepspeed/runtime/__pycache__/config_utils.cpython-310.pyc,,
deepspeed/runtime/__pycache__/constants.cpython-310.pyc,,
deepspeed/runtime/__pycache__/dataloader.cpython-310.pyc,,
deepspeed/runtime/__pycache__/eigenvalue.cpython-310.pyc,,
deepspeed/runtime/__pycache__/engine.cpython-310.pyc,,
deepspeed/runtime/__pycache__/hybrid_engine.cpython-310.pyc,,
deepspeed/runtime/__pycache__/lr_schedules.cpython-310.pyc,,
deepspeed/runtime/__pycache__/progressive_layer_drop.cpython-310.pyc,,
deepspeed/runtime/__pycache__/quantize.cpython-310.pyc,,
deepspeed/runtime/__pycache__/sparse_tensor.cpython-310.pyc,,
deepspeed/runtime/__pycache__/state_dict_factory.cpython-310.pyc,,
deepspeed/runtime/__pycache__/utils.cpython-310.pyc,,
deepspeed/runtime/__pycache__/weight_quantizer.cpython-310.pyc,,
deepspeed/runtime/activation_checkpointing/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/runtime/activation_checkpointing/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/activation_checkpointing/__pycache__/checkpointing.cpython-310.pyc,,
deepspeed/runtime/activation_checkpointing/__pycache__/config.cpython-310.pyc,,
deepspeed/runtime/activation_checkpointing/checkpointing.py,sha256=tj2YRdQ1dzc6mO7G_WPp2CHlxdPjH73Du_dGrqu_njw,45266
deepspeed/runtime/activation_checkpointing/config.py,sha256=YQkhHYT6Qr2GJl_KusLU0pUJ-rbLLKKj0Zo7LkeZkAg,3988
deepspeed/runtime/base_optimizer.py,sha256=Qhsb2zfDErq82uLhagrBBwtrGlRlb50j1UQtr9uQpfg,2640
deepspeed/runtime/bf16_optimizer.py,sha256=gCKKy5AhbSrSC9mx8guFGBN4joLlSY30Wgu8d4L1Oa8,25424
deepspeed/runtime/checkpoint_engine/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/runtime/checkpoint_engine/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/checkpoint_engine/__pycache__/checkpoint_engine.cpython-310.pyc,,
deepspeed/runtime/checkpoint_engine/__pycache__/nebula_checkpoint_engine.cpython-310.pyc,,
deepspeed/runtime/checkpoint_engine/__pycache__/torch_checkpoint_engine.cpython-310.pyc,,
deepspeed/runtime/checkpoint_engine/checkpoint_engine.py,sha256=rPBAEEhDvuvoB1FRmBT5LdG95-lx4KdVd83wwxC1nc4,653
deepspeed/runtime/checkpoint_engine/nebula_checkpoint_engine.py,sha256=uDA0iiprvyJZszXG98MV4RM48aWUHAnhLgRVFuxOp8s,4975
deepspeed/runtime/checkpoint_engine/torch_checkpoint_engine.py,sha256=amrlwu4rI_RLGjYBtw11tasdUWQsgbSeURU4QTkmW2Q,1060
deepspeed/runtime/comm/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/runtime/comm/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/comm/__pycache__/coalesced_collectives.cpython-310.pyc,,
deepspeed/runtime/comm/__pycache__/hccl.cpython-310.pyc,,
deepspeed/runtime/comm/__pycache__/mpi.cpython-310.pyc,,
deepspeed/runtime/comm/__pycache__/nccl.cpython-310.pyc,,
deepspeed/runtime/comm/coalesced_collectives.py,sha256=1E5b3LRMvfZOOY6LDw2vVwEaZrgCXhxKsEnWCESNcuY,7004
deepspeed/runtime/comm/hccl.py,sha256=hAkDzjlwVwCu9QNN6mLoKMGNHklfvErUKTRIOZ4P6BY,5169
deepspeed/runtime/comm/mpi.py,sha256=m3-XVeEL6F5ln5krGaExKcBj1Qlxg7y_XwvvVo8APcU,10076
deepspeed/runtime/comm/nccl.py,sha256=kAvrk95McM3accR11f2Grg1spe1kwP8UmC000zemsJw,7584
deepspeed/runtime/compiler.py,sha256=--Jrt_ZCYBeZah2kb1pJoj8Dz6YOXDL6T0UHrPYUXec,5510
deepspeed/runtime/compression/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/runtime/compression/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/compression/__pycache__/cupy.cpython-310.pyc,,
deepspeed/runtime/compression/cupy.py,sha256=5k1odBHWg9awK2tY1jsLmUDC22VE8hucw_MV0YgnABs,701
deepspeed/runtime/config.py,sha256=FwQPfai7GzhmnE8TMQvUooyMIEANg48JSXpA_HSl6xE,41581
deepspeed/runtime/config_utils.py,sha256=UNoTCKdYWkIozMFxydzOSLbvwPPto09B_cL37TSvrV4,8212
deepspeed/runtime/constants.py,sha256=rQ3-MjNZKBZ_Ym1GG0GOF0koX9BaxuvJFrpDzw36lAc,14373
deepspeed/runtime/data_pipeline/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/runtime/data_pipeline/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/__pycache__/config.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/__pycache__/constants.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/__pycache__/curriculum_scheduler.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/config.py,sha256=bqUnATEvLWDh7XfQ2RU38QolO6OKgLqLWfoNl9aQoGc,6081
deepspeed/runtime/data_pipeline/constants.py,sha256=iigt5xCqvkuIccraNU4y22wipAGdviEMRZEI_imVa_o,4701
deepspeed/runtime/data_pipeline/curriculum_scheduler.py,sha256=g6Heo7I5L8mpyKJXSH8t1SwO2Lxk9jMfOIMbYDksV00,10025
deepspeed/runtime/data_pipeline/data_routing/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/runtime/data_pipeline/data_routing/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/data_routing/__pycache__/basic_layer.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/data_routing/__pycache__/helper.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/data_routing/__pycache__/scheduler.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/data_routing/__pycache__/utils.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/data_routing/basic_layer.py,sha256=QvJuYJRbFTZSkKovN5Z7PVQP5o_fNC-2QgYqrWGy8ns,5638
deepspeed/runtime/data_pipeline/data_routing/helper.py,sha256=mFWBiepSdAAejDPSaIV6sXeKpRlfXoWFGF0CUmiDACY,1282
deepspeed/runtime/data_pipeline/data_routing/scheduler.py,sha256=8ddlbZJ3RO0btPKmurh3288Lk1CIoMxRWHcrxlVMZkY,4638
deepspeed/runtime/data_pipeline/data_routing/utils.py,sha256=ZGrHeImPXbVhLwOMQGWowXEK5YpIGXfC8i3RHVF4NAI,955
deepspeed/runtime/data_pipeline/data_sampling/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/runtime/data_pipeline/data_sampling/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/data_sampling/__pycache__/data_analyzer.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/data_sampling/__pycache__/data_sampler.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/data_sampling/__pycache__/indexed_dataset.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/data_sampling/__pycache__/utils.cpython-310.pyc,,
deepspeed/runtime/data_pipeline/data_sampling/data_analyzer.py,sha256=SHc-wCYVkdxJ56rKbsXLA3MTMriPtUYjXxZuNnQ2x38,48741
deepspeed/runtime/data_pipeline/data_sampling/data_sampler.py,sha256=PJNw94Zc0L31DRk4DH9vkXXGvXPMqtisnbF79gFY7UY,19894
deepspeed/runtime/data_pipeline/data_sampling/indexed_dataset.py,sha256=QQdA40qmTehZqhBDX2oGeJI8m1r8c6Jc5Hx-ygPa-j8,21449
deepspeed/runtime/data_pipeline/data_sampling/utils.py,sha256=5QtKBV_54fKL9I7ydqF40uBaLkUpcX5LFeYVz2KY_r4,1690
deepspeed/runtime/dataloader.py,sha256=rNiiaHzYOU3hp8OAiC2g9tFWPwACJTMhJ_EVrxvP5OU,6977
deepspeed/runtime/eigenvalue.py,sha256=Zk0AjdzV_v43BHceDkY4h35WyUvmuRgS-j-SzlIQ3r8,5625
deepspeed/runtime/engine.py,sha256=YTNRZO7Pld-bmFJ8G7CrXxKfGrRbfIf80BnHxOCcvGI,169755
deepspeed/runtime/fp16/__init__.py,sha256=Dzme9x1YQb-Ru4gzsCNsv8r19zkGyqwPwPwPgZzief4,140
deepspeed/runtime/fp16/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/fp16/__pycache__/fused_optimizer.cpython-310.pyc,,
deepspeed/runtime/fp16/__pycache__/loss_scaler.cpython-310.pyc,,
deepspeed/runtime/fp16/__pycache__/unfused_optimizer.cpython-310.pyc,,
deepspeed/runtime/fp16/fused_optimizer.py,sha256=KvYAtBtzQdWjvFRDMXt4ujKAmtTRjrGWxjUGBog6Shs,21958
deepspeed/runtime/fp16/loss_scaler.py,sha256=3SjPPKSeRImsKZ-fIQWqoj3Wl7hK3Nz2JnGLGnQaRRI,11492
deepspeed/runtime/fp16/onebit/__init__.py,sha256=8hnDOpk71O_EekeWgWIJ4CnbtYt67dre4flu5hoES-4,186
deepspeed/runtime/fp16/onebit/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/fp16/onebit/__pycache__/adam.cpython-310.pyc,,
deepspeed/runtime/fp16/onebit/__pycache__/lamb.cpython-310.pyc,,
deepspeed/runtime/fp16/onebit/__pycache__/zoadam.cpython-310.pyc,,
deepspeed/runtime/fp16/onebit/adam.py,sha256=ZFLUhZXHWJ-dsILzqMgH7t24taLVmymdupu3TioUeuA,15392
deepspeed/runtime/fp16/onebit/lamb.py,sha256=ffrxtLi4lszU8LwcS8iF7IaZhM56n8rFPpy202PWMNg,23247
deepspeed/runtime/fp16/onebit/zoadam.py,sha256=k0FVPbR40XpvjRqalgiNjQdARShpM41RDvUgxtx96Y8,19246
deepspeed/runtime/fp16/unfused_optimizer.py,sha256=qB2H34wcs1q05TqhyrA5wPYmC-RJduqjxswGA1rs3Ng,18029
deepspeed/runtime/hybrid_engine.py,sha256=GbXn3APoVhS8rcHvShYNsiA5Rq3ypPF32pIqFVZYDwc,20663
deepspeed/runtime/lr_schedules.py,sha256=tTm4L_qnlbgiqwZmU9PjCeXED9j-I0XLY_5HlqgjBa4,38923
deepspeed/runtime/pipe/__init__.py,sha256=4Xc534VEOCSKYVc-ImMFLx5eTmPB_URVgWqweehFsVI,195
deepspeed/runtime/pipe/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/pipe/__pycache__/engine.cpython-310.pyc,,
deepspeed/runtime/pipe/__pycache__/module.cpython-310.pyc,,
deepspeed/runtime/pipe/__pycache__/p2p.cpython-310.pyc,,
deepspeed/runtime/pipe/__pycache__/schedule.cpython-310.pyc,,
deepspeed/runtime/pipe/__pycache__/topology.cpython-310.pyc,,
deepspeed/runtime/pipe/engine.py,sha256=Q2aHov_qM3nCni6ZqJCvkDzPo8dFdm-cFNlAV06dvDo,64565
deepspeed/runtime/pipe/module.py,sha256=X3DuMn5oK9Ge6xC0q92q6P_F-lQ3NzlLWz0R01Xim04,27819
deepspeed/runtime/pipe/p2p.py,sha256=LsTCO2E3jGWITLisAwvAJRow6fipQrgQnVbMEicO0hs,5326
deepspeed/runtime/pipe/schedule.py,sha256=ZDzAeTPZTaYt77Wi2KSkycfuwneBGFsa7V6afQAzHo4,15546
deepspeed/runtime/pipe/topology.py,sha256=SLrWqLHSANPy4LnRS-RzvzhV45l_G0HhG_NGq7sQ6ws,17167
deepspeed/runtime/progressive_layer_drop.py,sha256=5zb3-BrMbRxYZL5lk1FtvaOknMi31xT1refDkgSMQbQ,1353
deepspeed/runtime/quantize.py,sha256=OmimrTJV_RoVG8TLVdjcQR91kSatQCNdFkPf3U39fsk,7699
deepspeed/runtime/sparse_tensor.py,sha256=QBoplFkxSYLqE9Jfj8mN2qNLMMpVpEgpP3nuIsobpRg,2466
deepspeed/runtime/state_dict_factory.py,sha256=Zp2mJbIySbYgp2cl5pFvLkpROOOqX9eqGnG1b3d0kD0,18177
deepspeed/runtime/swap_tensor/__init__.py,sha256=4I9UpQ5vMRU5SYSF_dW9FJDEnBq4m_0SuwtVQ92lGaA,95
deepspeed/runtime/swap_tensor/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/swap_tensor/__pycache__/aio_config.cpython-310.pyc,,
deepspeed/runtime/swap_tensor/__pycache__/async_swapper.cpython-310.pyc,,
deepspeed/runtime/swap_tensor/__pycache__/constants.cpython-310.pyc,,
deepspeed/runtime/swap_tensor/__pycache__/optimizer_utils.cpython-310.pyc,,
deepspeed/runtime/swap_tensor/__pycache__/partitioned_optimizer_swapper.cpython-310.pyc,,
deepspeed/runtime/swap_tensor/__pycache__/partitioned_param_swapper.cpython-310.pyc,,
deepspeed/runtime/swap_tensor/__pycache__/pipelined_optimizer_swapper.cpython-310.pyc,,
deepspeed/runtime/swap_tensor/__pycache__/utils.cpython-310.pyc,,
deepspeed/runtime/swap_tensor/aio_config.py,sha256=wqpT7zvdGuXq-FNPdoBsT-OdHfZ2R30VFRGD1vaePeQ,1172
deepspeed/runtime/swap_tensor/async_swapper.py,sha256=RlwlqBbkHa3nviVT4nrYzZ4s05nPch_dNMHTZcFOdxw,6349
deepspeed/runtime/swap_tensor/constants.py,sha256=5vyjU9aSe4vzmi7GtDwuayUOSYAfstUfh5-1uZEV17k,596
deepspeed/runtime/swap_tensor/optimizer_utils.py,sha256=1lnRZh_pEq_GbaTeATuIppDRzcY0XsIRp5E2dkSjZqY,19418
deepspeed/runtime/swap_tensor/partitioned_optimizer_swapper.py,sha256=vJzlDsvQQ4mD4lNI6unVB-IZGkt0ZmAuNVS0Dum4kyI,9792
deepspeed/runtime/swap_tensor/partitioned_param_swapper.py,sha256=0tLdMoMYEszj1Dhdfb_o5ePDrgjlCM9WrgIR99HO5ZU,18168
deepspeed/runtime/swap_tensor/pipelined_optimizer_swapper.py,sha256=CpW6WvgdlokpB3q4Joictot1gy1YoY0Rzw5H4lSVXG8,10820
deepspeed/runtime/swap_tensor/utils.py,sha256=7ojbp5Y62PpyV1HxyPqzWwqjo-B9Rj4WDAi58OZw0ug,7778
deepspeed/runtime/utils.py,sha256=Ui3zdiTm9-SDAYsR2oBMVtsNHVvahld_wTUhbDP75FM,41500
deepspeed/runtime/weight_quantizer.py,sha256=-aS6hTZnMcc3ckoYaCI6MiHybwy2NZo5AqEZ8rT7G_o,7027
deepspeed/runtime/zero/__init__.py,sha256=w3MBhv07e9ZvPptWeZdHp2L7E4dehAenVwSxkJ9TPy0,452
deepspeed/runtime/zero/__pycache__/__init__.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/config.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/contiguous_memory_allocator.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/linear.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/mics.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/mics_utils.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/offload_config.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/parameter_offload.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/partition_parameters.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/partitioned_param_coordinator.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/partitioned_param_profiler.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/stage3.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/stage_1_and_2.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/test.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/tiling.cpython-310.pyc,,
deepspeed/runtime/zero/__pycache__/utils.cpython-310.pyc,,
deepspeed/runtime/zero/config.py,sha256=kllEgYp1AYY-I92nGydGyD6sxghn-36XZDyJY1seoyY,12217
deepspeed/runtime/zero/contiguous_memory_allocator.py,sha256=r1J-KRJqO_Aa4Rqlsb1cgtHPa4mpnxm_zrd19FzZVQU,10923
deepspeed/runtime/zero/linear.py,sha256=1AaaHodON4Zp7PZ6XzP14w9efy3j4-oA0LcEdwZ5UvY,7522
deepspeed/runtime/zero/mics.py,sha256=VBkqj9vOG08ofM4gkFdduMgvt-Mdez58W9DEUJCHXeo,22061
deepspeed/runtime/zero/mics_utils.py,sha256=GWmBVESUd699i4YjCKScOwAwlwwFGs0si1dmPAeMz60,7500
deepspeed/runtime/zero/offload_config.py,sha256=yBF288VPaHcWK2xAypWlX64BCZjeaGMz7UEPZjTE8IY,3086
deepspeed/runtime/zero/parameter_offload.py,sha256=91D5D9Bk189hPPKAM65nix9hcTVZjCyr_zqyz-GtDTs,22081
deepspeed/runtime/zero/partition_parameters.py,sha256=RHOYLqtec73AJxNeNphWlNzmCf7vNqg2-sruTC6ayQw,102528
deepspeed/runtime/zero/partitioned_param_coordinator.py,sha256=GCcZ090pGiwnfznvTlPfl4rBb8VJt-velUJoNYUjT6A,27420
deepspeed/runtime/zero/partitioned_param_profiler.py,sha256=xTXgTnu1ew215TluuO2HvP78hZNbqPEhzJBieeOChf8,1801
deepspeed/runtime/zero/stage3.py,sha256=9mVPCHbNHJxBEi8Pn1dPkCfh8hyrOw4JKrJMCUqviL8,132433
deepspeed/runtime/zero/stage_1_and_2.py,sha256=zqWsG0oZGTuK3n6ZCHUMh6WAY8xewAmpKQszTmdtvDc,120795
deepspeed/runtime/zero/test.py,sha256=j7zu8Fnxm5_Wmp-aaUiQicPD1EUL2JGpODeSARFSZVs,2727
deepspeed/runtime/zero/tiling.py,sha256=Uz6IgvJPAQ4GICXj4E509p_BIDvnczWVtBT7EjZU9rg,11727
deepspeed/runtime/zero/utils.py,sha256=B_tddnZmjPofPv5QjWnZM8vIeqNSmID66L2VI3m4EIE,5399
deepspeed/sequence/__init__.py,sha256=4I9UpQ5vMRU5SYSF_dW9FJDEnBq4m_0SuwtVQ92lGaA,95
deepspeed/sequence/__pycache__/__init__.cpython-310.pyc,,
deepspeed/sequence/__pycache__/layer.cpython-310.pyc,,
deepspeed/sequence/layer.py,sha256=3dbXJ2BuCtHNKEf-zfiyvuJp7tmw-1Iaopbl5w15_Go,3791
deepspeed/utils/__init__.py,sha256=N3P4_ojRG3IyRy4lwU2qZ4As-XcGijW5GTuSHjhHl4A,1266
deepspeed/utils/__pycache__/__init__.cpython-310.pyc,,
deepspeed/utils/__pycache__/bwc.cpython-310.pyc,,
deepspeed/utils/__pycache__/comms_logging.cpython-310.pyc,,
deepspeed/utils/__pycache__/debug.cpython-310.pyc,,
deepspeed/utils/__pycache__/exceptions.cpython-310.pyc,,
deepspeed/utils/__pycache__/groups.cpython-310.pyc,,
deepspeed/utils/__pycache__/init_on_device.cpython-310.pyc,,
deepspeed/utils/__pycache__/logging.cpython-310.pyc,,
deepspeed/utils/__pycache__/mixed_precision_linkage.cpython-310.pyc,,
deepspeed/utils/__pycache__/numa.cpython-310.pyc,,
deepspeed/utils/__pycache__/nvtx.cpython-310.pyc,,
deepspeed/utils/__pycache__/tensor_fragment.cpython-310.pyc,,
deepspeed/utils/__pycache__/timer.cpython-310.pyc,,
deepspeed/utils/__pycache__/torch.cpython-310.pyc,,
deepspeed/utils/__pycache__/types.cpython-310.pyc,,
deepspeed/utils/__pycache__/z3_leaf_module.cpython-310.pyc,,
deepspeed/utils/__pycache__/zero_to_fp32.cpython-310.pyc,,
deepspeed/utils/bwc.py,sha256=EbdKjvtp_6XwUc3XhCyPxYfIqe9v5I23ynXGG39aAcY,3970
deepspeed/utils/comms_logging.py,sha256=NGCs6SHN9msKDBP4MBlXOspajebMMXDzmZhpZ5Ff1XE,7846
deepspeed/utils/debug.py,sha256=1GS4Yww4u-lNxIT1XxptUPsK_ND4DGBnTVURyBKvqxw,4504
deepspeed/utils/exceptions.py,sha256=h4J_9uk3HmKG8LdiSO8DKYzOmIYrD1MGwtZD1nA-Q3g,144
deepspeed/utils/groups.py,sha256=1_tjXTsyHEY5znwIheHNoOjYgO0c8EzJaCfiQ5jk5cw,24175
deepspeed/utils/init_on_device.py,sha256=Q4RFeRMi7PYGBHfTKh3E63X8IZquheDWYKbT56aOuuQ,3004
deepspeed/utils/logging.py,sha256=Qnc75V0u249sIgFZ3972cr67vVxhJuCQi00Upu0_bFU,4375
deepspeed/utils/mixed_precision_linkage.py,sha256=1dX8RSE1fHWwiY0gK7_pJHEYFiJHoJcN4K3W-i0eQZ8,2385
deepspeed/utils/numa.py,sha256=SRRwiHFlvZ7HZ4EfPxMUOh4F9aCovmgIxDSggfqmYe8,7170
deepspeed/utils/nvtx.py,sha256=rjO0SfexpacB1rAN9BiGtw-Xvj9WK74jA-9UZhBJ2M4,499
deepspeed/utils/tensor_fragment.py,sha256=KDmgLd-N-TJs8Z8vJUKnOmjd0GhfgqGJMmtaluuzNe4,12578
deepspeed/utils/timer.py,sha256=keS1ZsnipWMdMK5sK8-1rzQLSzll14XMiNdrFR1174w,10404
deepspeed/utils/torch.py,sha256=SlvCyKSgPjL-u0DCOovWxpb-6vT9oYtPGhoKnTPb4C4,584
deepspeed/utils/types.py,sha256=IcEwFod7RqLTRCGf7lr2sbKhFRf1an1KvtQ98nswwNw,434
deepspeed/utils/z3_leaf_module.py,sha256=qexuDAkm_-pcsONXdu7EpTjvtH3QzsbeB3L5WiNMtqk,4344
deepspeed/utils/zero_to_fp32.py,sha256=R-IsPi6cVXBf9yEC_9EUa2B-gIgPIBHO1byWnRHqewU,25314

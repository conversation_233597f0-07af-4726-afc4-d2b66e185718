langdetect-1.0.9.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
langdetect-1.0.9.dist-info/METADATA,sha256=DAMEft85XjmLIqHOwg4dbyOCPMjUSY6oJ-aeVXAqqfI,4564
langdetect-1.0.9.dist-info/RECORD,,
langdetect-1.0.9.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
langdetect-1.0.9.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
langdetect-1.0.9.dist-info/licenses/LICENSE,sha256=nFOf3Ef6ThlbiO-qZJXxFGPeJx9ZyN6W_ri8nIq6sas,599
langdetect-1.0.9.dist-info/licenses/NOTICE,sha256=vCK61ZBzOeEoNNkfeAnsNcPET6S4EvJ06_76ON4ICGk,655
langdetect-1.0.9.dist-info/top_level.txt,sha256=LvrLnEzJSvBcvH6N_EyWBZh2qqP7XvpnVQEzZSpFWr8,11
langdetect/__init__.py,sha256=-PWd_KOAfDOFbg0VvhRXlsIUBQhoHuiE_js4zaSTO8k,143
langdetect/__pycache__/__init__.cpython-310.pyc,,
langdetect/__pycache__/detector.cpython-310.pyc,,
langdetect/__pycache__/detector_factory.cpython-310.pyc,,
langdetect/__pycache__/lang_detect_exception.cpython-310.pyc,,
langdetect/__pycache__/language.cpython-310.pyc,,
langdetect/detector.py,sha256=XE37Ljm1QoT7NMbt-L1saiT1yF-FH1pBWrBQs7Hn_xI,8658
langdetect/detector_factory.py,sha256=tC3yHy2LppBG6k2gid02A76A1KUkdJzk8tUOA8S4nOI,4368
langdetect/lang_detect_exception.py,sha256=Nf6m4StZRubREdx3hDFxtkwNhM1_3JV8RwopMOoK_l4,519
langdetect/language.py,sha256=bMhYktIJZQe-sT2xwP1nmdXdNpalB6PGmbNZU9Ef9gs,430
langdetect/profiles/af,sha256=R_KhZPKdsCLCOskssj5e3AhYoO1c2jYaYyAzP_wsoOc,21532
langdetect/profiles/ar,sha256=-DuS1np_ivpoeJAYlmkulm6IJHvy94tBdcjIIcUccko,35182
langdetect/profiles/bg,sha256=sjCjH2DoBy1wFAldQUrATl575tj1NhEO9TVK1fTxERo,33520
langdetect/profiles/bn,sha256=XR1U94BV5Zv7MyCKXm6YqVJC90qJlRQ3n1uTb91q8ik,40800
langdetect/profiles/ca,sha256=u3cXnd3xaQKMc577Z5_slAcs6yNsvE4vyXYqj-8QqGw,69365
langdetect/profiles/cs,sha256=yG6kFoz7MNH6agmJLs8j8HpbNs4rtmyrKbXoytDE20E,33176
langdetect/profiles/cy,sha256=qCaiaZsUa4AAlCbAlxT17optXqmms7kfIbPlwRfTSMM,55195
langdetect/profiles/da,sha256=xlWdr48yWutsAHhRLUkwPbeO61CBhYsGy_Glu-FReSQ,25478
langdetect/profiles/de,sha256=m5z-FobEzjpIe5hFoWy9HDH77992hDS5WFTDUPsccqo,27502
langdetect/profiles/el,sha256=gvIoGs9ed3CCOKaKLHsU7W2GT7JhtZyebho_y8IKMAA,35031
langdetect/profiles/en,sha256=hX3MrlFJOSPd_c3b91cXXcA9fMq1Erm9IcW9gZEBa6Q,28154
langdetect/profiles/es,sha256=FzWiKwinlI_rAd_Oa35cNLwAjV9oe0Crd0Z63496Zn8,25211
langdetect/profiles/et,sha256=kOqLMxtQIUzQl2PQgi2bPS7zDJMHG853lWdeTs-cPuI,70548
langdetect/profiles/fa,sha256=V_ScsbIOwdxyIIIP25WC4b_NpnpiySbK0LwBIngvJw0,28603
langdetect/profiles/fi,sha256=kp0qYD4jikCTTjvbxHy7fkG6x4XNfST9s1oNZ0_WUF4,25539
langdetect/profiles/fr,sha256=ZPsWX0xUsp9giq3GJirQi6asCcj_giciWIkcH3eFwQI,26303
langdetect/profiles/gu,sha256=X6b-ZyiUTuzUgNkSWSr2NZPd6kq5q4EETN4F_nypfPs,21484
langdetect/profiles/he,sha256=xC8P6DSMwT8kPZh7XamDBFpPkAnC2IRL5hL1v4dSMEU,36134
langdetect/profiles/hi,sha256=x41iox2Pj4UkWjX1Sijdv_rufDLGJbLWBE35X6tiSsg,35727
langdetect/profiles/hr,sha256=sjn7bIsgJilzfo00o4sHfGtmpJW-LnoHOuuuw5yC1pI,25807
langdetect/profiles/hu,sha256=lB0gDqqBzLTj1648ktpM7y1HTOBNn43GggW09wAc4DI,31466
langdetect/profiles/id,sha256=ba3mnT1xh7TPZGPXk_OfpsOHnDCFcPkFMfHaajNlUgQ,21559
langdetect/profiles/it,sha256=7xB9gIwPmbQ6N6aop7xC1Ro2pOoTOiIkIp3tXgl8jHc,22799
langdetect/profiles/ja,sha256=CozKjrOh2OcPNJPUdJYYrb5RiVSdA7-BlvGhTyRT61A,25792
langdetect/profiles/kn,sha256=oE1TqNgoaj4sxD1x_6MT_GaHiTzcXwGJHMLiOu6-SgM,39903
langdetect/profiles/ko,sha256=datiYixz1Ym9A0kdkTvvQt4tk0CRv52-Az4dQCF9be4,307193
langdetect/profiles/lt,sha256=TDaVnxyYxNTmCNAvtCW60sa4TYMaVSgW1wgNYfctQfM,67770
langdetect/profiles/lv,sha256=tR8RuNG9jWMk40DUoIF_iSp_7DFKPvK5lFgaritNMiY,74760
langdetect/profiles/mk,sha256=nqPNjGuBMP1CbpYZ45_vWuREdh2akdXkI03CF-qVGhs,30026
langdetect/profiles/ml,sha256=f_PX8TSCx0Tm9PqXyHmvdK44-9fFJunydk1Rzg77FUY,39692
langdetect/profiles/mr,sha256=LdB14o1_-lRMOIuAeoQrc9XyxCkxKHVPiIdIDgI0xVA,36522
langdetect/profiles/ne,sha256=L0NJ8eO6kmKvZrsYf0qMZ2pA0dN2prTLS1wHh4kELLc,33856
langdetect/profiles/nl,sha256=tlDta2Oo4lX9yI7oAJMTGBmJTzjlKmm4L3t6XPLnM78,25049
langdetect/profiles/no,sha256=R2u5lufR4gr-Xo9g8vFhzdcMyMAT1TAuZ2wZNX4v7w4,25502
langdetect/profiles/pa,sha256=DrdTzoCZn6psP3Kq8vim6DdQ3wUfgAKYQHquqd9V3Pw,29336
langdetect/profiles/pl,sha256=ez3sOUBkEx0jFMjlOkctAIEsqYpjIp6MHhrCOSjoI_E,30392
langdetect/profiles/pt,sha256=jVEttZUSqcPjmCsdcgqyA5FUQOBgn7dlJOGI4y85v-k,25054
langdetect/profiles/ro,sha256=qcyG2IAXbcEgj1sI8ziBaX8cm_iJZhP3-mQg-U48y1E,62779
langdetect/profiles/ru,sha256=Qes9OcEukCJQ2sg91wZxEZzRXIKSm_cvv52n2cGaQ18,37609
langdetect/profiles/sk,sha256=nhm39mmiorXls5YLY1KiM62pinQu_QiFYm64Xnxu9m4,30756
langdetect/profiles/sl,sha256=ZOfXelP0NwvvIVE3Ps6fSmEXndMNDFc9X40ADqT1hTo,64837
langdetect/profiles/so,sha256=rB5ucB-u207ZLCe_B_hKtBjnugi5SCwVpyyKjSfI-u4,16786
langdetect/profiles/sq,sha256=pchluNoTeAFQixuDpGsUrngnrZNLBgE7nZUdOTWfgo4,23469
langdetect/profiles/sv,sha256=A8N50G4b1yJclBESgpCOHTr_I23rv34Wu1YaOCMbseU,27347
langdetect/profiles/sw,sha256=dC1vwEuEcx4Og6VtZKFQu7THrbrDADKXTRSlPrbgvHU,17714
langdetect/profiles/ta,sha256=L3wkwPoj7PyQ39O47TrkI23BOagqNvc8kweRGLbu_8s,33182
langdetect/profiles/te,sha256=wCXXTgivSlJBPexArdCFlQx-YCsaqZ5Tm3dRcJoc7PU,39696
langdetect/profiles/th,sha256=iitCt8TTmHGMT87owtNKR23aKsrJu5WDvTD1CKvldkc,51598
langdetect/profiles/tl,sha256=qT7pCw81_RTDUFDN2Y2exd_AP8sFU5N-Ca2TgAYIBs0,19009
langdetect/profiles/tr,sha256=IY8zKLIONZ8x9dQL_zwOgIdvoe9I_qiBJF7kzhAluNY,28752
langdetect/profiles/uk,sha256=yphYroYNbDWQDzeyRs_iP8HSzVCvXVRK5zOXaT8NeC4,33663
langdetect/profiles/ur,sha256=MaH8gCCbNbdWS5ATgF5yIMYLrmoMlss-RlSXED66eKw,28532
langdetect/profiles/vi,sha256=U3TzN_YKKATjPHEXzNnnbvfXKWwBE7o_YS8RKSjfKZA,67736
langdetect/profiles/zh-cn,sha256=2hRtAvoiN2ZoGedupfmfG28NW2WI-cX4dSynmAVIa4c,27340
langdetect/profiles/zh-tw,sha256=OypUIuhOpdUc_wFiSwhPv0pA9cZRxVB4wiE82uG51xo,27184
langdetect/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
langdetect/tests/__pycache__/__init__.cpython-310.pyc,,
langdetect/tests/__pycache__/test_detector.cpython-310.pyc,,
langdetect/tests/__pycache__/test_language.cpython-310.pyc,,
langdetect/tests/test_detector.py,sha256=H-lpXWmfZr2ndFAALKtmPMnhxq7s6tfPRvnOdj_Ob8Q,2359
langdetect/tests/test_language.py,sha256=dj_0RrKR_-CbQvKgVEk5hdCnDkiOgY2u3sRW8qSIIGs,680
langdetect/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
langdetect/utils/__pycache__/__init__.cpython-310.pyc,,
langdetect/utils/__pycache__/lang_profile.cpython-310.pyc,,
langdetect/utils/__pycache__/messages.cpython-310.pyc,,
langdetect/utils/__pycache__/ngram.cpython-310.pyc,,
langdetect/utils/__pycache__/unicode_block.cpython-310.pyc,,
langdetect/utils/lang_profile.py,sha256=FV9ffbbjz-EUuYMXYsfof6lutFuni9HVNTeZiwWKMcQ,2118
langdetect/utils/messages.properties,sha256=CyHI1zKeUKzz3cs5BvpBLNwPfatrRJBaJ3Ft_ghu9ew,43334
langdetect/utils/messages.py,sha256=Kyz7qFzVjO1TscjcHxL0R7DWUJMn_fMxWuna-MtIPqg,635
langdetect/utils/ngram.py,sha256=M9AegNxoCzB7nRcY98qB7NCi1zzqgiDE-TnYkzqn9GY,10431
langdetect/utils/unicode_block.py,sha256=bxqMT6mxRca_qQsZLr0tLGHsvE99BtrfOwvqYCy3UVg,17954

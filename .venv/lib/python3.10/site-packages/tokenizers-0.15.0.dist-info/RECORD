tokenizers-0.15.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
tokenizers-0.15.0.dist-info/METADATA,sha256=TLzU-rJkZkieb1ysKrejNwMMwN4QVw2j9DUPYmcY5cs,6678
tokenizers-0.15.0.dist-info/RECORD,,
tokenizers-0.15.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tokenizers-0.15.0.dist-info/WHEEL,sha256=_-Til0Sjq1maeEaMnjSgOqOpMrSiqXD0TSBhCcMTM9k,105
tokenizers/__init__.py,sha256=ZE5ZagUvobBScrHBQdEobhx4wqM0bsq9F9aLYkBNjYQ,2615
tokenizers/__init__.pyi,sha256=-dy7_9zPzRoco_GT7aK2kiyDNeTYVC1Pey8KUUwb_g0,38167
tokenizers/__pycache__/__init__.cpython-310.pyc,,
tokenizers/decoders/__init__.py,sha256=lGp32h8qerE0F48gyZL8wGmeQVlmjVpeIsRb1SM9kf4,335
tokenizers/decoders/__init__.pyi,sha256=qF5dcAK7Hiqdj_eXCTaG_-AdC_mvLy-E96_HzovIzgI,7041
tokenizers/decoders/__pycache__/__init__.cpython-310.pyc,,
tokenizers/implementations/__init__.py,sha256=VzAsplaIo7rl4AFO8Miu7ig7MfZjvonwVblZw01zR6M,310
tokenizers/implementations/__pycache__/__init__.cpython-310.pyc,,
tokenizers/implementations/__pycache__/base_tokenizer.cpython-310.pyc,,
tokenizers/implementations/__pycache__/bert_wordpiece.cpython-310.pyc,,
tokenizers/implementations/__pycache__/byte_level_bpe.cpython-310.pyc,,
tokenizers/implementations/__pycache__/char_level_bpe.cpython-310.pyc,,
tokenizers/implementations/__pycache__/sentencepiece_bpe.cpython-310.pyc,,
tokenizers/implementations/__pycache__/sentencepiece_unigram.cpython-310.pyc,,
tokenizers/implementations/base_tokenizer.py,sha256=2TFZhLupaJiMDYGJuUNmxYJv-cnR8bDHmbMzaYpFROs,14206
tokenizers/implementations/bert_wordpiece.py,sha256=sKCum0FKPYdSgJFJN8LDerVBoTDRSqyqSdrcm-lvQqI,5520
tokenizers/implementations/byte_level_bpe.py,sha256=OA_jyy3EQmYTa6hnf-EKwLOFuyroqFYOJz25ysM2BUk,4289
tokenizers/implementations/char_level_bpe.py,sha256=Q2ZEAW0xMQHF7YCUtmplwaxbU-J0P2NK4PJGMxUb-_c,5466
tokenizers/implementations/sentencepiece_bpe.py,sha256=syNXoQZX1JtI8U1A9XSDcoihlF3bIGVTVN53YJ83pV4,3679
tokenizers/implementations/sentencepiece_unigram.py,sha256=iCf9NKPAxTWfNGmwx0AooA1ubN44bAgTdvjU7LhnvhM,7462
tokenizers/models/__init__.py,sha256=eJZ4HTAQZpxnKILNylWaTFqxXy-Ba6OKswWN47feeV8,176
tokenizers/models/__init__.pyi,sha256=2RefLTJlKOF1o0ZI6wpBHDGIRMsXvn25-26yWCYavPY,16749
tokenizers/models/__pycache__/__init__.cpython-310.pyc,,
tokenizers/normalizers/__init__.py,sha256=hKOwnqWM-IlcVv7HDWT9SYhlczevuCNDQJY05ZFxkzk,808
tokenizers/normalizers/__init__.pyi,sha256=pR7NwcZDyFncmyZa9Wn1gjqzopaq5HyiPkRNHYuhzZk,19585
tokenizers/normalizers/__pycache__/__init__.cpython-310.pyc,,
tokenizers/pre_tokenizers/__init__.py,sha256=wd6KYQA_RsGSQK-HeG9opTRhv4ttSRkyno2dk6az-PM,557
tokenizers/pre_tokenizers/__init__.pyi,sha256=KI36GgHlR3p9GtLtheqNdFmt6jm9PVzNKq4awcy1dKU,23042
tokenizers/pre_tokenizers/__pycache__/__init__.cpython-310.pyc,,
tokenizers/processors/__init__.py,sha256=xM2DEKwKtHIumHsszM8AMkq-AlaqvBZFXWgLU8SNhOY,307
tokenizers/processors/__init__.pyi,sha256=AZXH9ZDWb0jvzp0DKC7lKjCeOwli70_ZvR3zqMKf7v4,11352
tokenizers/processors/__pycache__/__init__.cpython-310.pyc,,
tokenizers/tokenizers.cpython-310-darwin.so,sha256=IJxhYa6DP2kKMyGADQp2MOHIpx6tkiUVX0Gl8_PIXkk,7575584
tokenizers/tools/__init__.py,sha256=xG8caB9OHC8cbB01S5vYV14HZxhO6eWbLehsb70ppio,55
tokenizers/tools/__pycache__/__init__.cpython-310.pyc,,
tokenizers/tools/__pycache__/visualizer.cpython-310.pyc,,
tokenizers/tools/visualizer-styles.css,sha256=zAydq1oGWD8QEll4-eyL8Llw0B1sty_hpIE3tYxL02k,4850
tokenizers/tools/visualizer.py,sha256=0KUrLhkBLhPvg3GAkvsiBokb517bMCMSN-vuYY1qmEo,14621
tokenizers/trainers/__init__.py,sha256=UTu22AGcp76IvpW45xLRbJWET04NxPW6NfCb2YYz0EM,248
tokenizers/trainers/__init__.pyi,sha256=q5gsMXnp2UBO5-gHBlajb9lhOo8FOeKBCwVGGSA23Ws,5384
tokenizers/trainers/__pycache__/__init__.cpython-310.pyc,,

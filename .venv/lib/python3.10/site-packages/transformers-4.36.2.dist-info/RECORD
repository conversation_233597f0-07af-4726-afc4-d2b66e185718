../../../bin/transformers-cli,sha256=onAooWxpm_b7LDQ3HqRwAOG8yEMrqxJ5M4TbTWZjT2Q,280
transformers-4.36.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
transformers-4.36.2.dist-info/LICENSE,sha256=d_1HEN757DwPYiWADgI18VpCWr1KiwNVkSf814JhIEk,11418
transformers-4.36.2.dist-info/METADATA,sha256=B6KIzpm3mzFgiWsxZtDVhgeQYv0qcXZNY3layZG_fCk,126755
transformers-4.36.2.dist-info/RECORD,,
transformers-4.36.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers-4.36.2.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
transformers-4.36.2.dist-info/entry_points.txt,sha256=kgdW_0F_tXNrWKSZXKWKeUD_LqVgcji9j7atGXve8z4,81
transformers-4.36.2.dist-info/top_level.txt,sha256=GLBaeTo_CSdhnHvbxQ0kzpEHdlLuA_33foIogaWxntI,13
transformers/__init__.py,sha256=B3pEULALAKwmc4EZBLRZy2zWyURh73yN4X7kxMFkhLM,312017
transformers/__pycache__/__init__.cpython-310.pyc,,
transformers/__pycache__/activations.cpython-310.pyc,,
transformers/__pycache__/activations_tf.cpython-310.pyc,,
transformers/__pycache__/audio_utils.cpython-310.pyc,,
transformers/__pycache__/cache_utils.cpython-310.pyc,,
transformers/__pycache__/configuration_utils.cpython-310.pyc,,
transformers/__pycache__/convert_graph_to_onnx.cpython-310.pyc,,
transformers/__pycache__/convert_pytorch_checkpoint_to_tf2.cpython-310.pyc,,
transformers/__pycache__/convert_slow_tokenizer.cpython-310.pyc,,
transformers/__pycache__/convert_slow_tokenizers_checkpoints_to_fast.cpython-310.pyc,,
transformers/__pycache__/convert_tf_hub_seq_to_seq_bert_to_pytorch.cpython-310.pyc,,
transformers/__pycache__/debug_utils.cpython-310.pyc,,
transformers/__pycache__/deepspeed.cpython-310.pyc,,
transformers/__pycache__/dependency_versions_check.cpython-310.pyc,,
transformers/__pycache__/dependency_versions_table.cpython-310.pyc,,
transformers/__pycache__/dynamic_module_utils.cpython-310.pyc,,
transformers/__pycache__/feature_extraction_sequence_utils.cpython-310.pyc,,
transformers/__pycache__/feature_extraction_utils.cpython-310.pyc,,
transformers/__pycache__/file_utils.cpython-310.pyc,,
transformers/__pycache__/generation_flax_utils.cpython-310.pyc,,
transformers/__pycache__/generation_tf_utils.cpython-310.pyc,,
transformers/__pycache__/generation_utils.cpython-310.pyc,,
transformers/__pycache__/hf_argparser.cpython-310.pyc,,
transformers/__pycache__/hyperparameter_search.cpython-310.pyc,,
transformers/__pycache__/image_processing_utils.cpython-310.pyc,,
transformers/__pycache__/image_transforms.cpython-310.pyc,,
transformers/__pycache__/image_utils.cpython-310.pyc,,
transformers/__pycache__/keras_callbacks.cpython-310.pyc,,
transformers/__pycache__/modelcard.cpython-310.pyc,,
transformers/__pycache__/modeling_attn_mask_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_flax_outputs.cpython-310.pyc,,
transformers/__pycache__/modeling_flax_pytorch_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_flax_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_outputs.cpython-310.pyc,,
transformers/__pycache__/modeling_tf_outputs.cpython-310.pyc,,
transformers/__pycache__/modeling_tf_pytorch_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_tf_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_utils.cpython-310.pyc,,
transformers/__pycache__/optimization.cpython-310.pyc,,
transformers/__pycache__/optimization_tf.cpython-310.pyc,,
transformers/__pycache__/processing_utils.cpython-310.pyc,,
transformers/__pycache__/pytorch_utils.cpython-310.pyc,,
transformers/__pycache__/safetensors_conversion.cpython-310.pyc,,
transformers/__pycache__/testing_utils.cpython-310.pyc,,
transformers/__pycache__/tf_utils.cpython-310.pyc,,
transformers/__pycache__/time_series_utils.cpython-310.pyc,,
transformers/__pycache__/tokenization_utils.cpython-310.pyc,,
transformers/__pycache__/tokenization_utils_base.cpython-310.pyc,,
transformers/__pycache__/tokenization_utils_fast.cpython-310.pyc,,
transformers/__pycache__/trainer.cpython-310.pyc,,
transformers/__pycache__/trainer_callback.cpython-310.pyc,,
transformers/__pycache__/trainer_pt_utils.cpython-310.pyc,,
transformers/__pycache__/trainer_seq2seq.cpython-310.pyc,,
transformers/__pycache__/trainer_tf.cpython-310.pyc,,
transformers/__pycache__/trainer_utils.cpython-310.pyc,,
transformers/__pycache__/training_args.cpython-310.pyc,,
transformers/__pycache__/training_args_seq2seq.cpython-310.pyc,,
transformers/__pycache__/training_args_tf.cpython-310.pyc,,
transformers/activations.py,sha256=EMN-kVzitS1TmltS7Kr2ROKwxW0oLbAHeAmNdDQuvu4,8177
transformers/activations_tf.py,sha256=7a5YnB453-8r-gcxZjTt1RIEZJeZnhTHNpSznDsDgUA,4313
transformers/audio_utils.py,sha256=YqyGk1aKglfF1vpCI9t2QYTEtLXhYNAuo--asf1rJiE,32360
transformers/benchmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/benchmark/__pycache__/__init__.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark_args.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark_args_tf.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark_args_utils.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark_tf.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark_utils.cpython-310.pyc,,
transformers/benchmark/benchmark.py,sha256=q2Jk1RyHOtzNe7vDSVjkL9Kf1jkMiGZsJPDmsACnxxY,10752
transformers/benchmark/benchmark_args.py,sha256=YcLN181cLE8KTNxPFIaZx1qQEi839I5bQkJFb6FSaN8,3890
transformers/benchmark/benchmark_args_tf.py,sha256=bAcsgf7bOUyoo8AGFSiQhciR8S5wMJqnL5iVlvbQzow,4735
transformers/benchmark/benchmark_args_utils.py,sha256=CdoaDLVnLd3rpI-wJOh4bd1cZOFPglm3q0C5CvJOdk4,6475
transformers/benchmark/benchmark_tf.py,sha256=aEjclKepsQhn6vjxVJ5l2ho0ptUJuvaSYfuP4rJE6MQ,13251
transformers/benchmark/benchmark_utils.py,sha256=f9fv_EF1GwfK6A9wS6O-AYDrjI_cBflTbffL32iFTY0,37600
transformers/cache_utils.py,sha256=9tI-UJ8pZpoGpHCMvMRSiJuHr284uOAgHrdqlYwtD0w,15075
transformers/commands/__init__.py,sha256=aFO3I7C6G9OLA9JZSc_yMaZl0glOQtjNPjqMFfu9wfQ,923
transformers/commands/__pycache__/__init__.cpython-310.pyc,,
transformers/commands/__pycache__/add_new_model.cpython-310.pyc,,
transformers/commands/__pycache__/add_new_model_like.cpython-310.pyc,,
transformers/commands/__pycache__/convert.cpython-310.pyc,,
transformers/commands/__pycache__/download.cpython-310.pyc,,
transformers/commands/__pycache__/env.cpython-310.pyc,,
transformers/commands/__pycache__/lfs.cpython-310.pyc,,
transformers/commands/__pycache__/pt_to_tf.cpython-310.pyc,,
transformers/commands/__pycache__/run.cpython-310.pyc,,
transformers/commands/__pycache__/serving.cpython-310.pyc,,
transformers/commands/__pycache__/train.cpython-310.pyc,,
transformers/commands/__pycache__/transformers_cli.cpython-310.pyc,,
transformers/commands/__pycache__/user.cpython-310.pyc,,
transformers/commands/add_new_model.py,sha256=H8_UkJ8TYyC8sEMqE4Iu-Izq3lJi93l813oU-LI2XyY,11062
transformers/commands/add_new_model_like.py,sha256=IxCk-5HuqO9RBPugiIfFMdr6V_iV4-dg_l9pRmAfa2I,72929
transformers/commands/convert.py,sha256=lHz2sQti9HubMNwObLCc_sw9Y7L-IPcaYJMSJR_AVWM,7068
transformers/commands/download.py,sha256=GKPadx-YGBL7dHJSEcUp-QNOP3R2L71-gPGP0z6NNQI,2395
transformers/commands/env.py,sha256=q21O011lwdgGX862xAxH1Pjhd53uuxgB3g6C8cfNGV4,5316
transformers/commands/lfs.py,sha256=4QDGBbJxBcRpgmhHXvigZQUsXuTPwrRY60t1qGjzfWU,8001
transformers/commands/pt_to_tf.py,sha256=qVXHzdjjik3n_y8Ci8A6Wg6ag0eX0T6Dj36-sSv18Xg,20540
transformers/commands/run.py,sha256=nyEe2lOoj6e0EOxjKeF08hdW9WVWa101r9hWXl9v3Jo,4249
transformers/commands/serving.py,sha256=CnNHFVM_SK_-aNxEJnq7vJK5dBqDBw7bxxQiv5truEU,8027
transformers/commands/train.py,sha256=_Pp7unAWcFiNDAoXpVGe1D-OdFDjeZmbnkemACaCauQ,6329
transformers/commands/transformers_cli.py,sha256=QimzKwJXAzZ9da0NDFrupqnATqP8MQ7upoj9TspwnKA,2047
transformers/commands/user.py,sha256=t35-l945UBen5uYR_KsbhtNOqdHXrfdpHrhTbR3-YXc,7124
transformers/configuration_utils.py,sha256=-CQmoJB_AOA60ZDEqji131YS_IoSlewdLLgmTtT127w,56295
transformers/convert_graph_to_onnx.py,sha256=qHIXclgeJWxKHXUSRzXhNp01UTNWWA6_ifM5C8W59_M,20903
transformers/convert_pytorch_checkpoint_to_tf2.py,sha256=QkU6fSbDMc4MQhNE4cv4xk9MBI2TGnC42ym9d9dAYyo,16631
transformers/convert_slow_tokenizer.py,sha256=iRxnOKjorS2GadMWk58ciQICS-UGce4g_2_cLCeCzaY,52552
transformers/convert_slow_tokenizers_checkpoints_to_fast.py,sha256=mIX3e0r7Dci5lahBf0iO4C2rvj0OzwkJbmw5lmgiG0Q,4982
transformers/convert_tf_hub_seq_to_seq_bert_to_pytorch.py,sha256=LHDZoAwfslnHT74gNbiW2GmYim89tPtKm4HQsxxyeck,2899
transformers/data/__init__.py,sha256=JWIY7GLKedWilK2mpd_qtVeXLQK2ZXki6ISkRUua09Y,1423
transformers/data/__pycache__/__init__.cpython-310.pyc,,
transformers/data/__pycache__/data_collator.cpython-310.pyc,,
transformers/data/data_collator.py,sha256=rqIwvABxZgNFiOe4Dj_F7fQoi6hfVNtgj4VOcqfZdoc,77018
transformers/data/datasets/__init__.py,sha256=PGzUJjdmTPOPMyjV4-Tj3sNrmmh-lspjyxrVbrfJoX8,909
transformers/data/datasets/__pycache__/__init__.cpython-310.pyc,,
transformers/data/datasets/__pycache__/glue.cpython-310.pyc,,
transformers/data/datasets/__pycache__/language_modeling.cpython-310.pyc,,
transformers/data/datasets/__pycache__/squad.cpython-310.pyc,,
transformers/data/datasets/glue.py,sha256=K3h2KxjIg0kWegPCw6ikbOL-lCFbKoQewb7R8wLZoIc,6163
transformers/data/datasets/language_modeling.py,sha256=E-VGwuyb09J4KmV8v37bNH5in90wDPuZHCYsqGdT7W0,23721
transformers/data/datasets/squad.py,sha256=OUTQDd687SQns7HRWDCgAjnuo_ZXihifLS6jF2bhUhc,9219
transformers/data/metrics/__init__.py,sha256=o9t_VTQtqU3lEhqvocDzFMm7OvAKD-uxrjPWy0r74BI,3632
transformers/data/metrics/__pycache__/__init__.cpython-310.pyc,,
transformers/data/metrics/__pycache__/squad_metrics.cpython-310.pyc,,
transformers/data/metrics/squad_metrics.py,sha256=pMwqcTg9KnCvmhLzAy1VJHRgJOEx6lLD105d-JcnWfg,29698
transformers/data/processors/__init__.py,sha256=lvN5mp9mdrr5v6QvZT6VcoZ78zZUvXiumTm6Gdvlgvo,1014
transformers/data/processors/__pycache__/__init__.cpython-310.pyc,,
transformers/data/processors/__pycache__/glue.cpython-310.pyc,,
transformers/data/processors/__pycache__/squad.cpython-310.pyc,,
transformers/data/processors/__pycache__/utils.cpython-310.pyc,,
transformers/data/processors/__pycache__/xnli.cpython-310.pyc,,
transformers/data/processors/glue.py,sha256=hhY12jdX1WnZ3_E3vSv-0rmF53F56c_2gQeW8dTwYb4,23219
transformers/data/processors/squad.py,sha256=_4WNLcZA6TAy7uNZO46948tmL5ngVF0LSB0y8nUn6rs,33153
transformers/data/processors/utils.py,sha256=GSaZbJ--XYq57vqyRVx_5LHSR4tklzFyR7ZKHGWsTAs,13829
transformers/data/processors/xnli.py,sha256=i03-c8vaQVYKpR7r4B8PsF6_CXXHxB7N-YHdzxs-APU,3489
transformers/debug_utils.py,sha256=6q8ArB104GdcIC2qfBQzKLxO7PfXmHEKdYtfL2FOK2w,12907
transformers/deepspeed.py,sha256=6C1uUQ84ImJPYu3WqZ-o6uOGPa7IHzD0MkP7DgnQxJY,1478
transformers/dependency_versions_check.py,sha256=6HbgtT2Wp-QZGOAdyUOklHvNA4rOVITGHrX34dtMOqg,2115
transformers/dependency_versions_table.py,sha256=E2skUNh4ZSnOGiW0uWDGYvTCWZd-oCV_fw0TKZh-jjI,3184
transformers/dynamic_module_utils.py,sha256=BtObcAPPKF5XXupR5074KResTaPip2QHdMmEwBx3KQM,27485
transformers/feature_extraction_sequence_utils.py,sha256=dPKvTC29tNn8xK_dxZSeDbhNRK2s8VHu2EZIEKesEAs,18307
transformers/feature_extraction_utils.py,sha256=t0oprvQFyqHGXHSqniRnMSaHvC8msHYeYv4CHdb-SFY,29616
transformers/file_utils.py,sha256=KYUQbFCY0gglJDUzCys0Mf0Mj0SjPKy4xp_s3mudl3U,3628
transformers/generation/__init__.py,sha256=2Da8Cn-iWSqqnUDZCgCyojpOZHtRFPOj_4ge2jAVEfY,10557
transformers/generation/__pycache__/__init__.cpython-310.pyc,,
transformers/generation/__pycache__/beam_constraints.cpython-310.pyc,,
transformers/generation/__pycache__/beam_search.cpython-310.pyc,,
transformers/generation/__pycache__/configuration_utils.cpython-310.pyc,,
transformers/generation/__pycache__/flax_logits_process.cpython-310.pyc,,
transformers/generation/__pycache__/flax_utils.cpython-310.pyc,,
transformers/generation/__pycache__/logits_process.cpython-310.pyc,,
transformers/generation/__pycache__/stopping_criteria.cpython-310.pyc,,
transformers/generation/__pycache__/streamers.cpython-310.pyc,,
transformers/generation/__pycache__/tf_logits_process.cpython-310.pyc,,
transformers/generation/__pycache__/tf_utils.cpython-310.pyc,,
transformers/generation/__pycache__/utils.cpython-310.pyc,,
transformers/generation/beam_constraints.py,sha256=GefqriO2jWruyhdZI9pyGz4yZ-W9AYmzZueSWITgok4,19105
transformers/generation/beam_search.py,sha256=d6ZduwortYoRu6d0uCWfz1ivHqeQAxdA_lDrRA0kUOU,48812
transformers/generation/configuration_utils.py,sha256=7O30utpVh-JsD2fmzuhIbTpltXVIlUeZ2x5LcgFqFYg,52415
transformers/generation/flax_logits_process.py,sha256=fuuAEM_Bo7vRUY1OhfYJozuQBhE8QmM0X1ZxJvelJ-Y,19196
transformers/generation/flax_utils.py,sha256=N5gL_hMNmtMCSMTvAIZVbwmEYSJe87PFwsOnbPBld5k,49855
transformers/generation/logits_process.py,sha256=vb2Hvh2tzadAncdI90Dy-28KSCjBJl2ybYO4bieQSPg,102050
transformers/generation/stopping_criteria.py,sha256=j8bioEpgxcyBHRxRsBHPVqtpduZQe-2-oeh-BviZwrE,6683
transformers/generation/streamers.py,sha256=MjBAILDIQW_cxzPTikEK2q2MAdrafqia8P5Dsn82WaI,9145
transformers/generation/tf_logits_process.py,sha256=ZsIBDrFJ3egkk8aWYKtCvqH4M7INnlBa2zoCAIT5MR0,28114
transformers/generation/tf_utils.py,sha256=G3QWwMJyfPIvZnO7TN4T-FxxBNAo9DcbRh1nZFi9cIs,178238
transformers/generation/utils.py,sha256=z-HNlZ_ML5vauzezbR8dauKNYUwCZ5f_g-uDl-Uu7pk,271971
transformers/generation_flax_utils.py,sha256=4UcpPzqxiAT7GmkxIYbjZ99K1t6A4-7qvRUHWQWk0co,1118
transformers/generation_tf_utils.py,sha256=0yPmKpgmNyo2RLMbn_gorpinY2H1sOeEZ4GiDhyMPqk,1109
transformers/generation_utils.py,sha256=JYoVUAmmI1ubdJkRGODnJAE8q3oVt2RGDMl5MXlFt4Q,1126
transformers/hf_argparser.py,sha256=Ah91LzoTwRPuWxONLFmPxDC8DGcUK_ebghoCQV2xNu8,19745
transformers/hyperparameter_search.py,sha256=wmfAWk_NTUQj3MezO_6CaDaJyUt9pbARcs-tbo_BdeM,4171
transformers/image_processing_utils.py,sha256=KrPtA8QbZDWcLuctVLdw-OB8MCMm9fsNTLDfBZiHBKM,34668
transformers/image_transforms.py,sha256=Ag4YGN3gKHH10Z8acNpPxsjV43cyv9Ak5u3CwXTboZM,34154
transformers/image_utils.py,sha256=IBue41Fhc0gA3SbirfQiwJFdFpZSnP_-LQ76lYrFdlI,25600
transformers/integrations/__init__.py,sha256=S-ZwZOuzV6832VPZO_SDiHvzM4YOsTCeZ5Hpv29A3ps,4518
transformers/integrations/__pycache__/__init__.cpython-310.pyc,,
transformers/integrations/__pycache__/awq.cpython-310.pyc,,
transformers/integrations/__pycache__/bitsandbytes.cpython-310.pyc,,
transformers/integrations/__pycache__/deepspeed.cpython-310.pyc,,
transformers/integrations/__pycache__/integration_utils.cpython-310.pyc,,
transformers/integrations/__pycache__/peft.cpython-310.pyc,,
transformers/integrations/awq.py,sha256=1LKS8EW6GOHVd6v8ttsTiH5AIHbIbQFAQU8g8UO-j4I,14271
transformers/integrations/bitsandbytes.py,sha256=IkNdY65DdtKYZNNy40G22H2bWJanRiqEJIGny7lPVw0,13569
transformers/integrations/deepspeed.py,sha256=vUc6rRq9-akF8JSk8k2GwofXxD89JP7Q06QC7bXBR0M,17727
transformers/integrations/integration_utils.py,sha256=uGiL7IAoDqK4v9T4dzIiLCZRLBlf7ec-Jx8ogqa1op4,76640
transformers/integrations/peft.py,sha256=_1zABToVWSH9U7XoPG5cJVmAT_5jbSbMDUADHvGiAXE,22620
transformers/keras_callbacks.py,sha256=6QryScSbxOYOyRgX-RkNnsdnHa8iNGI_8W2_rRqwNT0,20654
transformers/kernels/deformable_detr/cpu/ms_deform_attn_cpu.cpp,sha256=VcCGm9IrvgVvmyZt0KyP16Q-ONmbeg6bKwccP6KadL0,1255
transformers/kernels/deformable_detr/cpu/ms_deform_attn_cpu.h,sha256=nvVsKj9nabQ7IaNY4di5xVx6u-0lIifQvLg2JCoxiik,1138
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.cu,sha256=M5-bW9g5z-upTFMNPIfnyLAqKTxGMCjAPqBr0GmWHX8,7360
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.cuh,sha256=hygB20Vh3RttOSdCuTFz8V0d3CXNp-Q89x22rYmD258,61433
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.h,sha256=rPWOOMo3QyFdB5kMiexpApLFZ4dnRtx4CluEAGwsfO8,1139
transformers/kernels/deformable_detr/cuda/ms_deform_im2col_cuda.cuh,sha256=BRN8-yfSHY8ChLij8jFl2_z2LL0LEFKuVF6Byi-YLAY,54695
transformers/kernels/deformable_detr/ms_deform_attn.h,sha256=H2bBXGyl0R-v2DqGVz11asoRvxbjZ9iWB9djomZTpgY,1837
transformers/kernels/deformable_detr/vision.cpp,sha256=8RvZy7P_MMx5QEszo_MwNODddJLQ8mKcmmMfgLYC_HA,798
transformers/kernels/mra/cuda_kernel.cu,sha256=LxxRYTymSoBEQpWXHA0PMzwZwpolcwX7mFAjwU8-ZMc,11678
transformers/kernels/mra/cuda_kernel.h,sha256=UJvYq_MDzhcp07bZpYcOBn8ZGFcf_Ax1dynuiVTBvmA,1682
transformers/kernels/mra/cuda_launch.cu,sha256=Ox5MTACriC30CGyn-g1Kb5EgQSMAZSaN6fpit3xLFWc,4072
transformers/kernels/mra/cuda_launch.h,sha256=RVCkN_euasvgPK0zADNRvRYGWd4ah5l9X-7UG_AcdH8,707
transformers/kernels/mra/torch_extension.cpp,sha256=N0YdBLVX0lZabckJzV_RYTHS2atCNvn13E4Ivobt25g,1405
transformers/kernels/rwkv/wkv_cuda.cu,sha256=EvaUrEnw_qr2EjMKP-Pq7VPzFfGlMJnFhdHNLtn1fPU,6219
transformers/kernels/rwkv/wkv_cuda_bf16.cu,sha256=DG9hTtOAlrnpDFahjt-MmnOxjMuhGU55GPsmV21HtrQ,6633
transformers/kernels/rwkv/wkv_op.cpp,sha256=qSExhKdT6p3hyaTv5SypCnH_c7EmaX6HbhTcCntvZWg,4022
transformers/kernels/yoso/common.h,sha256=Tq2rOUtE8Y4DRAUrRISvwIwVI3u8JBf21WgWSAYiDlQ,273
transformers/kernels/yoso/common_cuda.h,sha256=Sji70AuVcuZSotLF7Gotmun9MJuOHo8wEkxizKXLRtc,258
transformers/kernels/yoso/common_cuda_device.h,sha256=y6WUgAiapnMKqthRMS5s-DMSWNVkar_i8g4KPFvqiuk,2063
transformers/kernels/yoso/fast_lsh_cumulation.cu,sha256=LA4LGNgyXT3osIyQtFBcRanSyNQWm8yqmpz7AeLP7cw,19061
transformers/kernels/yoso/fast_lsh_cumulation.h,sha256=1cTWZjOm751HGiEB5P-UPJ8SE1VO7XRyXmBgyxYDyjI,1575
transformers/kernels/yoso/fast_lsh_cumulation_cuda.cu,sha256=HKGLWl-WFz5BXjaAPHTNTbG6IUkJjhBdvFf2K7hrDVQ,32870
transformers/kernels/yoso/fast_lsh_cumulation_cuda.h,sha256=_KGI8HQbVFtCN5KAcSGpyiJ2foGi26RKen138CUc2fY,5490
transformers/kernels/yoso/fast_lsh_cumulation_torch.cpp,sha256=-Rh7o39Z3rtOPwNnEM-c51TCqywpVdK0WVaA7VRrXbQ,3154
transformers/modelcard.py,sha256=Wbj5hMqhzXm3LFRVAq2p0fnQOjdtEpr_oFFmJdwIzlo,35322
transformers/modeling_attn_mask_utils.py,sha256=L94ZahVePEphMrTwRGCQKtcvFRSS1GpjiqIgl3CYaLc,20463
transformers/modeling_flax_outputs.py,sha256=wXse1g9VyQyVOZ9DrbPALeoZBdS45fsBA9fNrGnwaZc,41961
transformers/modeling_flax_pytorch_utils.py,sha256=aBUEufISaRi0iQG7UjNNPb7wWN70ves8LzbFyhqJ53o,21100
transformers/modeling_flax_utils.py,sha256=xzVE02r-38GSrJccfte0PQniHpRa-GiHIH1ZuTUxahA,61431
transformers/modeling_outputs.py,sha256=COiZiOKuX33yq80IJT1Fio88nh2_LCzq9oEaXBcqvdY,112002
transformers/modeling_tf_outputs.py,sha256=nXCMOmFZ7IZFVuiQr7EU2ciV9QqwOYPYld_r2jBxVpE,56074
transformers/modeling_tf_pytorch_utils.py,sha256=p32_1AFJBm0HykOp97cZbgeJfyd7ntGZtvKR9KSNTjg,25423
transformers/modeling_tf_utils.py,sha256=xYCYxfe0DG1o92uElcXuAnvDGgD45vJPhdBRDUhfjyw,162767
transformers/modeling_utils.py,sha256=lys2HoTfHpakerxGUgZ8J-v03asEJFDqjsi3VKbQK7s,237437
transformers/models/__init__.py,sha256=MI3vfrImZS9OGbgN3JAhZ5emKOUdrMayfZ_IIoxJeds,3867
transformers/models/__pycache__/__init__.cpython-310.pyc,,
transformers/models/albert/__init__.py,sha256=eXW8msH9V8No-Tb5R28tdpXQbOnnSG77L_TVEwCRf9o,5482
transformers/models/albert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/albert/__pycache__/configuration_albert.cpython-310.pyc,,
transformers/models/albert/__pycache__/convert_albert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/albert/__pycache__/modeling_albert.cpython-310.pyc,,
transformers/models/albert/__pycache__/modeling_flax_albert.cpython-310.pyc,,
transformers/models/albert/__pycache__/modeling_tf_albert.cpython-310.pyc,,
transformers/models/albert/__pycache__/tokenization_albert.cpython-310.pyc,,
transformers/models/albert/__pycache__/tokenization_albert_fast.cpython-310.pyc,,
transformers/models/albert/configuration_albert.py,sha256=XQdPWatHqInNsHfSOdMOLR6wJkw9nJzhNx0KlqwtfdY,8847
transformers/models/albert/convert_albert_original_tf_checkpoint_to_pytorch.py,sha256=nTwtVg0AZgG4QnG9K361HM37gxGegQvD-ymZWuhic7s,2162
transformers/models/albert/modeling_albert.py,sha256=W9h59Qulyqo0dTojA059YbaE9kAArNdWVwLvdF9KZ44,60694
transformers/models/albert/modeling_flax_albert.py,sha256=YTwzs9cmyj2Acte2IE-YEsohSd3G6yVFQP_sAxQsKsg,40718
transformers/models/albert/modeling_tf_albert.py,sha256=CRwq_JKNBsPf6kT7nt-MleFp9jl-BV-agEvdr1BMvbQ,61857
transformers/models/albert/tokenization_albert.py,sha256=sxF1tX-MSAZVhic7qLKFgq58hTjanhOfHrgLtjIwZ4M,15651
transformers/models/albert/tokenization_albert_fast.py,sha256=hOSCOqCoRbE6sTLJOHD4UyyIraKbkRre7ri8URCZQN0,10876
transformers/models/align/__init__.py,sha256=DWtMJsXbmRuoSAwLLOy6aXKY65IT1TDV4ifwBmApkM0,2064
transformers/models/align/__pycache__/__init__.cpython-310.pyc,,
transformers/models/align/__pycache__/configuration_align.cpython-310.pyc,,
transformers/models/align/__pycache__/convert_align_tf_to_hf.cpython-310.pyc,,
transformers/models/align/__pycache__/modeling_align.cpython-310.pyc,,
transformers/models/align/__pycache__/processing_align.cpython-310.pyc,,
transformers/models/align/configuration_align.py,sha256=hv_Qla2NBHxk-zqK7B1inpcACzuzXN6_8oK_Gw9XB8w,18242
transformers/models/align/convert_align_tf_to_hf.py,sha256=ZQwIg6-U1lyyQkpXgyJzz4QUi5r0brLJC7OmEHdvxeY,15524
transformers/models/align/modeling_align.py,sha256=Ncsm3PefFCPcNvZJWnOtpJjTKcL8EP8pixcPLYe16f4,71836
transformers/models/align/processing_align.py,sha256=xuKyyR9ouKXWtDOr2VDspp_ciDVAhVUMgCwbBi_T0i0,6216
transformers/models/altclip/__init__.py,sha256=bvOH6rQhnWm4shjpJ51SPs0uxlDdPrViBxQqTt3gRik,2126
transformers/models/altclip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/altclip/__pycache__/configuration_altclip.cpython-310.pyc,,
transformers/models/altclip/__pycache__/modeling_altclip.cpython-310.pyc,,
transformers/models/altclip/__pycache__/processing_altclip.cpython-310.pyc,,
transformers/models/altclip/configuration_altclip.py,sha256=w3shM1-n7WVZ_e9GUIImHot9vDmUr8cUEq5r1GXO8yI,19914
transformers/models/altclip/modeling_altclip.py,sha256=OT-GM_4zx1mgZ-tvGEPcBsvZqPv28CXC7lPaMxUvc9c,78293
transformers/models/altclip/processing_altclip.py,sha256=uaRYuV0f_EaU-_b6-zkjG1_EYYXwBCa7dgI9XMCvIho,6502
transformers/models/audio_spectrogram_transformer/__init__.py,sha256=-LyBP9am8Di97o7CZupQyqD1-2bYHKLcUqVWTZBHVs8,2159
transformers/models/audio_spectrogram_transformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/configuration_audio_spectrogram_transformer.cpython-310.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/convert_audio_spectrogram_transformer_original_to_pytorch.cpython-310.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/feature_extraction_audio_spectrogram_transformer.cpython-310.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/modeling_audio_spectrogram_transformer.cpython-310.pyc,,
transformers/models/audio_spectrogram_transformer/configuration_audio_spectrogram_transformer.py,sha256=JOl78E7J3sK4jPlLBi7AuzEdMu1pkTnbL31lNMHeNy4,5649
transformers/models/audio_spectrogram_transformer/convert_audio_spectrogram_transformer_original_to_pytorch.py,sha256=Csn0NnGlPMLUehRWvgU1cW49EzTNZ7p0COxWNIqQIp8,11052
transformers/models/audio_spectrogram_transformer/feature_extraction_audio_spectrogram_transformer.py,sha256=CLMcdUUk8ehA2PC9wEBwvWd68tIMFtZswNhVbVwXWc8,9908
transformers/models/audio_spectrogram_transformer/modeling_audio_spectrogram_transformer.py,sha256=SxXZUboxV5dpnCT_5zjgh1lJLj-LukgwCAydxy8cwYs,26013
transformers/models/auto/__init__.py,sha256=OU8_fzxvhi-Z3SDSsGMBZ6QZKGUjLAjTIIYOo7HmtHg,16586
transformers/models/auto/__pycache__/__init__.cpython-310.pyc,,
transformers/models/auto/__pycache__/auto_factory.cpython-310.pyc,,
transformers/models/auto/__pycache__/configuration_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/feature_extraction_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/image_processing_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/modeling_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/modeling_flax_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/modeling_tf_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/processing_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/tokenization_auto.cpython-310.pyc,,
transformers/models/auto/auto_factory.py,sha256=kYknkrhJa5uLtIMVhLZiXHI17fJ4S6N7wrBEfeQG3fQ,43296
transformers/models/auto/configuration_auto.py,sha256=U-kUHOviq7UpshdkEgo8DMmYb3TYMy65XcRLKEkbrno,48880
transformers/models/auto/feature_extraction_auto.py,sha256=IX73exqEjjZZ58xPjCFO1U3DTAJUMGGTO2AVhZVfMbI,19775
transformers/models/auto/image_processing_auto.py,sha256=M26YflRy6Lzf3zk-0EXsMcp4-r-iApcrdNCTjkWcJAY,21355
transformers/models/auto/modeling_auto.py,sha256=mvNNujMC4JUWt-U8ghF6BaKvwgPjZq8MiJew3bahua0,62826
transformers/models/auto/modeling_flax_auto.py,sha256=pj2ZHy9v33OxzOcs4CSYmA4kMUJglJDMf9s9enph0RA,14288
transformers/models/auto/modeling_tf_auto.py,sha256=-SXK90oQqatjtdk8dbAGMgTrIZ67frkfZk2FGcUqwpM,28058
transformers/models/auto/processing_auto.py,sha256=-19nzXvqfH8JPvRYIkoiCF_tKdYpvlkVFUcbnCvWLW8,16191
transformers/models/auto/tokenization_auto.py,sha256=MOsioeE-0tOfBMRmlHkY9aR6hnDp9QqaVGOtn2ikuA0,43859
transformers/models/autoformer/__init__.py,sha256=wNFDMEr-Yo9Bt33bP5qqiC5dWKXOnWQPFg4C_ewyfGU,1914
transformers/models/autoformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/autoformer/__pycache__/configuration_autoformer.cpython-310.pyc,,
transformers/models/autoformer/__pycache__/modeling_autoformer.cpython-310.pyc,,
transformers/models/autoformer/configuration_autoformer.py,sha256=6aYNqme_BQL0dvImtD0gt88OUnkmGPURBBrjI_GGWZk,12326
transformers/models/autoformer/modeling_autoformer.py,sha256=l89x3e1h8QuHFuLnbhqtfLyCsIElUG2Ndqoo2ghbfv4,107214
transformers/models/bark/__init__.py,sha256=o6hWj_LrFLp-JSNY04tbWewQyrA44B0mhLUDpyv4jVw,2212
transformers/models/bark/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bark/__pycache__/configuration_bark.cpython-310.pyc,,
transformers/models/bark/__pycache__/convert_suno_to_hf.cpython-310.pyc,,
transformers/models/bark/__pycache__/generation_configuration_bark.cpython-310.pyc,,
transformers/models/bark/__pycache__/modeling_bark.cpython-310.pyc,,
transformers/models/bark/__pycache__/processing_bark.cpython-310.pyc,,
transformers/models/bark/configuration_bark.py,sha256=JQxdKx552_IozCxuGbWrT99B8F8-T86GDd68QNeV_Uc,13046
transformers/models/bark/convert_suno_to_hf.py,sha256=O1OYzKyTr-9snPYUAw09GmVwb76UmiQGi3C2WfEIwTw,9373
transformers/models/bark/generation_configuration_bark.py,sha256=80ZI8x5r8JH26siXfm_c8NkuaRTUUzcxiMrtfIKDoSg,14992
transformers/models/bark/modeling_bark.py,sha256=K1nsVT8XKjuVBOr_xWJeKdyOLEyi1W75aLtXQ6Rj8eE,86623
transformers/models/bark/processing_bark.py,sha256=bkn-RZH10cuX7ZMKgerDN3SFFT4sSPyDxfZFklYEEqc,13492
transformers/models/bart/__init__.py,sha256=FH8iETt_U4YAIIjo-Oap-WtQsBZqsaxGr9028KnrDEQ,4397
transformers/models/bart/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bart/__pycache__/configuration_bart.cpython-310.pyc,,
transformers/models/bart/__pycache__/convert_bart_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/bart/__pycache__/modeling_bart.cpython-310.pyc,,
transformers/models/bart/__pycache__/modeling_flax_bart.cpython-310.pyc,,
transformers/models/bart/__pycache__/modeling_tf_bart.cpython-310.pyc,,
transformers/models/bart/__pycache__/tokenization_bart.cpython-310.pyc,,
transformers/models/bart/__pycache__/tokenization_bart_fast.cpython-310.pyc,,
transformers/models/bart/configuration_bart.py,sha256=FC2xQItsO2mqpk9EHA3US6_2j3bjSPOFG9zlDaj5cSw,18994
transformers/models/bart/convert_bart_original_pytorch_checkpoint_to_pytorch.py,sha256=VIRm-jWP4PNWN0Japr8yCJAJAAPVkJpJmEzYnHexU88,6055
transformers/models/bart/modeling_bart.py,sha256=b9NCOdofKAJs-5SUzWP6y1g8cvFTu1DigWHVb51VVUQ,109169
transformers/models/bart/modeling_flax_bart.py,sha256=JH4YXctmpkynng1wP-50Vn4t8vEuhEmFfsfQZu1-lFI,82707
transformers/models/bart/modeling_tf_bart.py,sha256=o3hpMEfph07CV8jPqgtaM3gf7ZZCbmYIU6m4BNx2zyY,74926
transformers/models/bart/tokenization_bart.py,sha256=eDtvUiWOfG4-3gXrfrZC7O0GGVq02DDT5goW_7GA91k,17981
transformers/models/bart/tokenization_bart_fast.py,sha256=trWcch-2mM_kwUpefMCurfYNhlHpCS2xPtyIdzxjTrg,14139
transformers/models/barthez/__init__.py,sha256=7IXg6okZoJ10NCYRWn0GvoWWUvGUN27eIw7CzJ5CVGA,1848
transformers/models/barthez/__pycache__/__init__.cpython-310.pyc,,
transformers/models/barthez/__pycache__/tokenization_barthez.cpython-310.pyc,,
transformers/models/barthez/__pycache__/tokenization_barthez_fast.cpython-310.pyc,,
transformers/models/barthez/tokenization_barthez.py,sha256=d-vtDWs4WnzUvLrit6CS5ysV5ovyyhm77Yt7jSOAlWo,12691
transformers/models/barthez/tokenization_barthez_fast.py,sha256=HuYoq_rMfA1TmOfUppFdG4CEowDeGbBUFc-4JGYxlkY,8961
transformers/models/bartpho/__init__.py,sha256=Q0mAOPJGQaHHigdajLg5-2TPOw9NWw5uIRQlmfhh8Ds,1362
transformers/models/bartpho/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bartpho/__pycache__/tokenization_bartpho.cpython-310.pyc,,
transformers/models/bartpho/tokenization_bartpho.py,sha256=NWvZzH9O_aBN3QcK7nSu_VrYjP3LT-wZWSEbutQH40I,14052
transformers/models/beit/__init__.py,sha256=T88Lwe4Y0tQmdrOpVnewjuHJoW_DZEbRmbTZDU2oAR0,3339
transformers/models/beit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/beit/__pycache__/configuration_beit.cpython-310.pyc,,
transformers/models/beit/__pycache__/convert_beit_unilm_to_pytorch.cpython-310.pyc,,
transformers/models/beit/__pycache__/feature_extraction_beit.cpython-310.pyc,,
transformers/models/beit/__pycache__/image_processing_beit.cpython-310.pyc,,
transformers/models/beit/__pycache__/modeling_beit.cpython-310.pyc,,
transformers/models/beit/__pycache__/modeling_flax_beit.cpython-310.pyc,,
transformers/models/beit/configuration_beit.py,sha256=YwjNqn1Z4fJxkgCt5zUXSsrY9xx5LOoxKUWb2G7htPc,11703
transformers/models/beit/convert_beit_unilm_to_pytorch.py,sha256=CndMgSTJoOik5LPH3YVLnQ6IR7IqfCsEN0KPUR43jHA,16578
transformers/models/beit/feature_extraction_beit.py,sha256=C9wchKLt3K__wzqOkDWsbK0hMPzVn9HZtm5KPI5Oq2s,1172
transformers/models/beit/image_processing_beit.py,sha256=sfOeIx2uSXirRiQ210ZnfwQqjYlf7u3l3kNz1MdJJio,24342
transformers/models/beit/modeling_beit.py,sha256=nWBzj3J5fx2dstCF-WyM3_Ts_Hj6iEp1z4b3n74ieUA,59847
transformers/models/beit/modeling_flax_beit.py,sha256=9_xkFN7xtiLrxbShhpX8EgpY8kuOKIui-OlRidmNUAI,36996
transformers/models/bert/__init__.py,sha256=Tj3tueT-1FoWBmNNZXGGnytzeoLeEcjviP32uyfU1rw,6057
transformers/models/bert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bert/__pycache__/configuration_bert.cpython-310.pyc,,
transformers/models/bert/__pycache__/convert_bert_original_tf2_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/bert/__pycache__/convert_bert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/bert/__pycache__/convert_bert_pytorch_checkpoint_to_original_tf.cpython-310.pyc,,
transformers/models/bert/__pycache__/convert_bert_token_dropping_original_tf2_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/bert/__pycache__/modeling_bert.cpython-310.pyc,,
transformers/models/bert/__pycache__/modeling_flax_bert.cpython-310.pyc,,
transformers/models/bert/__pycache__/modeling_tf_bert.cpython-310.pyc,,
transformers/models/bert/__pycache__/tokenization_bert.cpython-310.pyc,,
transformers/models/bert/__pycache__/tokenization_bert_fast.cpython-310.pyc,,
transformers/models/bert/__pycache__/tokenization_bert_tf.cpython-310.pyc,,
transformers/models/bert/configuration_bert.py,sha256=UHXeN0yAg39YZ7IEqVPHhDWB74Og4L56Bs-xFloi6xs,10151
transformers/models/bert/convert_bert_original_tf2_checkpoint_to_pytorch.py,sha256=niQmTMwlmUA0aII1Zzg2OiJSpFljzwLCeJYotJ4tKOY,10490
transformers/models/bert/convert_bert_original_tf_checkpoint_to_pytorch.py,sha256=Hq-TMOnQnfpZOh0m9GHoykkogg0-HgLAmSiFvK8E6K4,2159
transformers/models/bert/convert_bert_pytorch_checkpoint_to_original_tf.py,sha256=x3RYywp9Za8rgBAfcoLHFSwOcuhd4NM0RamrgXYCCj8,4098
transformers/models/bert/convert_bert_token_dropping_original_tf2_checkpoint_to_pytorch.py,sha256=5kYqUUc-RGck4D0OUTlLDnyIPb_OIJ1NWboYRJ-7H0c,7606
transformers/models/bert/modeling_bert.py,sha256=rCE89FUeJGyN3t4X0xJ5hrtxvtd_TkcPrlWLcM6AUVo,84063
transformers/models/bert/modeling_flax_bert.py,sha256=5Josa2ZROAKYKE8RXru64VloSuvNwmzqqQzJTW3ytns,63607
transformers/models/bert/modeling_tf_bert.py,sha256=9rJy3j9AS8wFR4fLbLFVDhPMGkzDmM2lJ7LBZvKuXiI,85690
transformers/models/bert/tokenization_bert.py,sha256=b-NJd8I2QcBy6uQly6NpjLLD4cs6FLDYhiduW7YNY7E,25175
transformers/models/bert/tokenization_bert_fast.py,sha256=5RTDCGRYXLQrnd712in7NbZnJxwJurCKSYuqluJE1g0,14883
transformers/models/bert/tokenization_bert_tf.py,sha256=HvE7LbdCNET-q1psKZP5w0_Zgipg1Fqz1HtoaOjjDgw,11757
transformers/models/bert_generation/__init__.py,sha256=2XUvSVePne5Hspjzn6l_PonKfZ9WXjRBub9bevOv8R4,2275
transformers/models/bert_generation/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bert_generation/__pycache__/configuration_bert_generation.cpython-310.pyc,,
transformers/models/bert_generation/__pycache__/modeling_bert_generation.cpython-310.pyc,,
transformers/models/bert_generation/__pycache__/tokenization_bert_generation.cpython-310.pyc,,
transformers/models/bert_generation/configuration_bert_generation.py,sha256=DIEAcuNI_Ufp7hPLN-nDuvJLYDYgr9gNphiroKv-4qY,6342
transformers/models/bert_generation/modeling_bert_generation.py,sha256=XwCC1kp-Sr2QssLGXpH4wds3Y8J80Xz8MNHHj2_w9j4,48087
transformers/models/bert_generation/tokenization_bert_generation.py,sha256=NTwSl4_Ia_UYJNtV4k0X1RMaKoi8wk1iVXAbcKv9aUE,7499
transformers/models/bert_japanese/__init__.py,sha256=6prQNXS2J4cWXqAqkqDyxNmzx-vaFQtOjJQio-ZUc4g,1053
transformers/models/bert_japanese/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bert_japanese/__pycache__/tokenization_bert_japanese.cpython-310.pyc,,
transformers/models/bert_japanese/tokenization_bert_japanese.py,sha256=8jvFCbJgKwAB2xk9Y_Ak3nFRbHYz4LT5wW9WYmRWlis,40186
transformers/models/bertweet/__init__.py,sha256=sXE2NweoWp8UIaJkuSaLSw4EaSEzpWwBe3pegec_Kj0,959
transformers/models/bertweet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bertweet/__pycache__/tokenization_bertweet.cpython-310.pyc,,
transformers/models/bertweet/tokenization_bertweet.py,sha256=RKgK5ohzBE_RnsgL04k4l2k5shQcM3392GsbAf3vpL8,27482
transformers/models/big_bird/__init__.py,sha256=XaBDMkK9Dhqc9pVSqqn2xFCNYInFMsBpPOP8GZ0F04Q,4574
transformers/models/big_bird/__pycache__/__init__.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/configuration_big_bird.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/convert_bigbird_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/modeling_big_bird.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/modeling_flax_big_bird.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/tokenization_big_bird.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/tokenization_big_bird_fast.cpython-310.pyc,,
transformers/models/big_bird/configuration_big_bird.py,sha256=k3Be1t0z_NMYfb8avfAjbOPiF811xV6_BAc0_NFT58I,8307
transformers/models/big_bird/convert_bigbird_original_tf_checkpoint_to_pytorch.py,sha256=Y75oSwtX-d2wwOSwLo6LlUlZ9uzSEVtWwzwiJYcrXyg,2493
transformers/models/big_bird/modeling_big_bird.py,sha256=Leu6pqsCfXNkNJ-gyguZIIA9M5h3-HXvAsp6n0T08No,142453
transformers/models/big_bird/modeling_flax_big_bird.py,sha256=ePVW-6VwD8sgJYIlX4eWv0EVNaInVosJW_CtqlyzpGs,109510
transformers/models/big_bird/tokenization_big_bird.py,sha256=orqmmJAd34V5vernWRRUbjzWuBxI1wu6VxH5yPqaviI,14885
transformers/models/big_bird/tokenization_big_bird_fast.py,sha256=Cy8VpqLv0nnW3jnfsRvALVh35CS9blyljQwurBvmzAw,11416
transformers/models/bigbird_pegasus/__init__.py,sha256=lTnaYtQ3nRjYYND5G3wilFyh6VOOWlKjNXbsmJTo-A4,2316
transformers/models/bigbird_pegasus/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bigbird_pegasus/__pycache__/configuration_bigbird_pegasus.cpython-310.pyc,,
transformers/models/bigbird_pegasus/__pycache__/convert_bigbird_pegasus_tf_to_pytorch.cpython-310.pyc,,
transformers/models/bigbird_pegasus/__pycache__/modeling_bigbird_pegasus.cpython-310.pyc,,
transformers/models/bigbird_pegasus/configuration_bigbird_pegasus.py,sha256=wQe2DFenXFvHnhh8gITYxOnI1OqV1P-hcyv5Bj7-i_8,19803
transformers/models/bigbird_pegasus/convert_bigbird_pegasus_tf_to_pytorch.py,sha256=Wc7aoNvtzxt-DPi655Kl30CgDgq_hp08psISb8dWpLU,6288
transformers/models/bigbird_pegasus/modeling_bigbird_pegasus.py,sha256=t4rc-DDM7d5AUcgpezVNMyedJah2QuPtjmrZsOBjBwk,146069
transformers/models/biogpt/__init__.py,sha256=dV4wh5lT3U-EYdvjCy6b9lI4Lr2zIN1RqSs6Rsuc6Sg,2058
transformers/models/biogpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/biogpt/__pycache__/configuration_biogpt.cpython-310.pyc,,
transformers/models/biogpt/__pycache__/convert_biogpt_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/biogpt/__pycache__/modeling_biogpt.cpython-310.pyc,,
transformers/models/biogpt/__pycache__/tokenization_biogpt.cpython-310.pyc,,
transformers/models/biogpt/configuration_biogpt.py,sha256=22dQFzvwpQfcB6Va9E2OAHx9_P7y-h7mstztcdzGXgo,6391
transformers/models/biogpt/convert_biogpt_original_pytorch_checkpoint_to_pytorch.py,sha256=5zNYzaEy7QPc99LCHTcofXSCI3tr0pzlIpFpwT1ZgN0,10578
transformers/models/biogpt/modeling_biogpt.py,sha256=WB0LRYtFXokRJujjW9CsWoT8YIx4CHqjJGnBwLQfnM4,41152
transformers/models/biogpt/tokenization_biogpt.py,sha256=keQm4qzy2m4SZVgg4E29wCn-iin25o1Z0TyfADTG04E,13723
transformers/models/bit/__init__.py,sha256=g9Upc1daCF75FealBk9SK9FMQ-wkJMQxtjoN5mDk4cI,2244
transformers/models/bit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bit/__pycache__/configuration_bit.cpython-310.pyc,,
transformers/models/bit/__pycache__/convert_bit_to_pytorch.cpython-310.pyc,,
transformers/models/bit/__pycache__/image_processing_bit.cpython-310.pyc,,
transformers/models/bit/__pycache__/modeling_bit.cpython-310.pyc,,
transformers/models/bit/configuration_bit.py,sha256=qksjepO1KM0winDXkYseK_oFfEDhQ1TFgq8-Ct9W0TA,6235
transformers/models/bit/convert_bit_to_pytorch.py,sha256=Z50gXtfe6Tj44cPdIvrFRqjHPdWHdeka5oAqsTuK_ig,5955
transformers/models/bit/image_processing_bit.py,sha256=xTQXv3SmNNtbOhQkW5Vha6UwzTG3-82u0LAUTv8SL50,15945
transformers/models/bit/modeling_bit.py,sha256=Pj41T4OQaSepqZgJwO32OVqKA-vmSNuG-k908qptUWw,31850
transformers/models/blenderbot/__init__.py,sha256=nB9V1KQEetB0dazUyJ_KWDJscltclpJ6fJ746wy6zuU,4031
transformers/models/blenderbot/__pycache__/__init__.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/configuration_blenderbot.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/convert_blenderbot_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/modeling_blenderbot.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/modeling_flax_blenderbot.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/modeling_tf_blenderbot.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/tokenization_blenderbot.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/tokenization_blenderbot_fast.cpython-310.pyc,,
transformers/models/blenderbot/configuration_blenderbot.py,sha256=m2YojN7IYReFcsxg-sjlI6SDKqZbzH6oo1BIRFYnorA,19017
transformers/models/blenderbot/convert_blenderbot_original_pytorch_checkpoint_to_pytorch.py,sha256=86QBWYTeyJvxMUOfxqmGHwpDneadfqbEGSujMYw3yuU,3702
transformers/models/blenderbot/modeling_blenderbot.py,sha256=7uuNQt4sMVEpfQrgS07ogKPGSco71YDRIUSVWVH_6MU,75745
transformers/models/blenderbot/modeling_flax_blenderbot.py,sha256=-2C6LxBSnWTRtoaOHDJrt9pGPLqo-7nGwCYQkJdQ4Js,64985
transformers/models/blenderbot/modeling_tf_blenderbot.py,sha256=t2mu_m6Wje3mV5EBoJGXnvtITFd0wJBCiYGBM2UEDTc,67823
transformers/models/blenderbot/tokenization_blenderbot.py,sha256=aFPN6m3CGzp4fAL5GQDDaTxyi60fEa2O-2SGDZcaLh4,19704
transformers/models/blenderbot/tokenization_blenderbot_fast.py,sha256=JjC9TmCN6hmqsF1aAWmHrC1XETZtTo9CoVD0xJ5bzws,14506
transformers/models/blenderbot_small/__init__.py,sha256=O-iMMZ9xZdyvP2PV4QYvxFcCaY6jEpKt5iyDzI_mrfM,4263
transformers/models/blenderbot_small/__pycache__/__init__.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/configuration_blenderbot_small.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_blenderbot_small.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_flax_blenderbot_small.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_tf_blenderbot_small.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/tokenization_blenderbot_small.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/tokenization_blenderbot_small_fast.cpython-310.pyc,,
transformers/models/blenderbot_small/configuration_blenderbot_small.py,sha256=BVfaMDmCo0zlZ06hmmjGUm84_gQvFNPH9MMPXSqh-hc,18480
transformers/models/blenderbot_small/modeling_blenderbot_small.py,sha256=Wwpz6wm_i21fgZzfSjp3t98z8bKpeU5NATcBJv2sRvg,74629
transformers/models/blenderbot_small/modeling_flax_blenderbot_small.py,sha256=7S4Aw5OKwRuUErJrna1O5LNERPCtclQ4p_bFbApnLOI,65946
transformers/models/blenderbot_small/modeling_tf_blenderbot_small.py,sha256=yknzZaN7pXBaE7vvXquRr7nT83GiByp_eugBjt9Ff0w,67121
transformers/models/blenderbot_small/tokenization_blenderbot_small.py,sha256=gGoLNRXM8zCX3eZxjWFIk9rxmNxmT7roP003t7XpjQE,9641
transformers/models/blenderbot_small/tokenization_blenderbot_small_fast.py,sha256=4fakxK-TssZxZhOY5VlF8wyaT-rhOuM-GMEsJt4rVok,5046
transformers/models/blip/__init__.py,sha256=1OJOhjlrdGG1mkS-46qni8DdTosNMNVWZlR9QTe1K2I,3692
transformers/models/blip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/blip/__pycache__/configuration_blip.cpython-310.pyc,,
transformers/models/blip/__pycache__/convert_blip_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/blip/__pycache__/image_processing_blip.cpython-310.pyc,,
transformers/models/blip/__pycache__/modeling_blip.cpython-310.pyc,,
transformers/models/blip/__pycache__/modeling_blip_text.cpython-310.pyc,,
transformers/models/blip/__pycache__/modeling_tf_blip.cpython-310.pyc,,
transformers/models/blip/__pycache__/modeling_tf_blip_text.cpython-310.pyc,,
transformers/models/blip/__pycache__/processing_blip.cpython-310.pyc,,
transformers/models/blip/configuration_blip.py,sha256=B7h0lXieyw54YO0HD_euOWY7nx3yfw3fCtMG8UnwOGA,16555
transformers/models/blip/convert_blip_original_pytorch_to_hf.py,sha256=_pDCFuQdBCmPXUJz-PL-WK8BAAmlIwY68iHNA206Jvo,6980
transformers/models/blip/image_processing_blip.py,sha256=9_v5OQ5tzrVCr6ktlWxfHFqw27g22t67--q4Gazy3yw,15270
transformers/models/blip/modeling_blip.py,sha256=Isw9XQF9lHmAwHsIE0u3_6q78Bh3yr2jO2mpsin9zt8,61771
transformers/models/blip/modeling_blip_text.py,sha256=G9TrRkLphnDkJDYAA9PkRRG7pr1dxmg_xgjhQMVk8Pk,43710
transformers/models/blip/modeling_tf_blip.py,sha256=jrvAmodSujD1tWIkPuaKhtqqeJfdgsOLbz_i2xxWEw8,65592
transformers/models/blip/modeling_tf_blip_text.py,sha256=uQwgD3VNIjQnZI7rRYVDNpVpqB1kHWD2_ZQoPYhI5us,42783
transformers/models/blip/processing_blip.py,sha256=oU2XUYUq7FZy_9TiJFzlsojF0P-hTd9o93f4TNtSxxo,6205
transformers/models/blip_2/__init__.py,sha256=uEo0Z9nF4AxtGnnMSZPEvbdImyy24KR_F1YtOJj_mvY,2153
transformers/models/blip_2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/blip_2/__pycache__/configuration_blip_2.cpython-310.pyc,,
transformers/models/blip_2/__pycache__/convert_blip_2_original_to_pytorch.cpython-310.pyc,,
transformers/models/blip_2/__pycache__/modeling_blip_2.cpython-310.pyc,,
transformers/models/blip_2/__pycache__/processing_blip_2.cpython-310.pyc,,
transformers/models/blip_2/configuration_blip_2.py,sha256=LsMNegy1IW5wYUhuBuQSjgsFLIq0aCWJYwI6MIBISW4,16643
transformers/models/blip_2/convert_blip_2_original_to_pytorch.py,sha256=0343xouUoM4JqP29bgDyCbNIJfSl8BO-e278133ytSA,12276
transformers/models/blip_2/modeling_blip_2.py,sha256=Zpnf1Vb2H1mIlj6QGMvPfRTqn6wLuQn7XrzTCQ8Um-E,81576
transformers/models/blip_2/processing_blip_2.py,sha256=4HnjqBRHKwuEH6NKGv0s27Tx3-alA0DYoWXJtM2gZ2I,6699
transformers/models/bloom/__init__.py,sha256=21dUYJI8_NttCwbHTXqYSl6VcqLj_PoHPPr5NRRu49E,3098
transformers/models/bloom/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bloom/__pycache__/configuration_bloom.cpython-310.pyc,,
transformers/models/bloom/__pycache__/convert_bloom_original_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/bloom/__pycache__/modeling_bloom.cpython-310.pyc,,
transformers/models/bloom/__pycache__/modeling_flax_bloom.cpython-310.pyc,,
transformers/models/bloom/__pycache__/tokenization_bloom_fast.cpython-310.pyc,,
transformers/models/bloom/configuration_bloom.py,sha256=95E05m3wMv77Tyl7xfSrFfU4jWutXboiL83oa4PQpTQ,10758
transformers/models/bloom/convert_bloom_original_checkpoint_to_pytorch.py,sha256=WvxNS5YRu84Ek1ieKkyHRKcakRbZFJr5989nEjI6qQs,10302
transformers/models/bloom/modeling_bloom.py,sha256=Ot0aJVHgM7uGJXdSSUg0C6LqSJh2V1ZAotcDsMF7vxo,54911
transformers/models/bloom/modeling_flax_bloom.py,sha256=zBWwHZI6OBs9S1h9JSSAaEnskPKpa8jHn5AROhbLXpw,30092
transformers/models/bloom/tokenization_bloom_fast.py,sha256=bPeMw1CeJ066JpaYDBP6RtAjRutt0MMU4rCV31vghPQ,7878
transformers/models/bridgetower/__init__.py,sha256=hqrBKe3gtOVATPn1QP5BEpqSVNhJZ2x_Cg11t0Bv-lc,2864
transformers/models/bridgetower/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bridgetower/__pycache__/configuration_bridgetower.cpython-310.pyc,,
transformers/models/bridgetower/__pycache__/image_processing_bridgetower.cpython-310.pyc,,
transformers/models/bridgetower/__pycache__/modeling_bridgetower.cpython-310.pyc,,
transformers/models/bridgetower/__pycache__/processing_bridgetower.cpython-310.pyc,,
transformers/models/bridgetower/configuration_bridgetower.py,sha256=U4Ha4Gds3fgLzmi-TQQGkkXzgY99nHdVQl9YtjLExmQ,16517
transformers/models/bridgetower/image_processing_bridgetower.py,sha256=1jaRiAgWqP2w3rc25ZR86j2YjNi88E5TLglM3zyvWdk,25099
transformers/models/bridgetower/modeling_bridgetower.py,sha256=VQvdDi-r0nh3nJvV4nh9-VRnjsVpmRPx5oA4YcLTIV4,88294
transformers/models/bridgetower/processing_bridgetower.py,sha256=FriChYR6CPgyDBUwOJrDlCJBuHo9RBIWXwN_NxgSGN8,5057
transformers/models/bros/__init__.py,sha256=T1UKhF6X3-gs8q9-oIzspFbX-kmnMVirfNN1yZyCT2o,2445
transformers/models/bros/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bros/__pycache__/configuration_bros.cpython-310.pyc,,
transformers/models/bros/__pycache__/convert_bros_to_pytorch.cpython-310.pyc,,
transformers/models/bros/__pycache__/modeling_bros.cpython-310.pyc,,
transformers/models/bros/__pycache__/processing_bros.cpython-310.pyc,,
transformers/models/bros/configuration_bros.py,sha256=V0OrFMvH3MHoFaKnQdG7pL1elgJ-PBuZghbGHI9E_0s,6658
transformers/models/bros/convert_bros_to_pytorch.py,sha256=kxZDGzvIYxz9hbIzzJOfOj5tixji5efb2884rqwoY6A,4871
transformers/models/bros/modeling_bros.py,sha256=0GhM7g6oz7qvMX2dnQl4Rbi9hGskHfLa73JxlhfGPso,58023
transformers/models/bros/processing_bros.py,sha256=FQUu5czHHvQzZ1P5N9GhfjZu4cmZw_mYKuX0VNjrB54,4193
transformers/models/byt5/__init__.py,sha256=06YhQd8TFNbc9lU5qzERZUdcSWIFxOeBOaqQh6S4WC4,942
transformers/models/byt5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/byt5/__pycache__/convert_byt5_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/byt5/__pycache__/tokenization_byt5.cpython-310.pyc,,
transformers/models/byt5/convert_byt5_original_tf_checkpoint_to_pytorch.py,sha256=83tKCwYRSRW7zXtm9cmszqtPhpw44cH8Cj0SWUSBgN0,2120
transformers/models/byt5/tokenization_byt5.py,sha256=DF8GtvaS6EpR1UqaQEh6IRaT0lRQD3CKineT6ngRy_4,10031
transformers/models/camembert/__init__.py,sha256=UBlxBknmDgdOkelwnQSGkAejq1meoGd2CgmQtGayhII,4443
transformers/models/camembert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/camembert/__pycache__/configuration_camembert.cpython-310.pyc,,
transformers/models/camembert/__pycache__/modeling_camembert.cpython-310.pyc,,
transformers/models/camembert/__pycache__/modeling_tf_camembert.cpython-310.pyc,,
transformers/models/camembert/__pycache__/tokenization_camembert.cpython-310.pyc,,
transformers/models/camembert/__pycache__/tokenization_camembert_fast.cpython-310.pyc,,
transformers/models/camembert/configuration_camembert.py,sha256=qH_2t28SJy8mOiGVNIgfxJRht7_1fjWWYLAF8BG7P1s,7735
transformers/models/camembert/modeling_camembert.py,sha256=Isj71t8X9e__13UPPKmsTbGhXEgC_0FqslmtQLM7dN4,72595
transformers/models/camembert/modeling_tf_camembert.py,sha256=EldbXllGdvdBbdv4qwvadYNmU8qpQ6cZizc0SkO7I1I,73463
transformers/models/camembert/tokenization_camembert.py,sha256=KvORubpOfaNOZF5vhG-OZcD07JRhC5bNB_ydDE3CnDg,14341
transformers/models/camembert/tokenization_camembert_fast.py,sha256=x0RadbSDx6CGCKl0mwYEHvrhIifgKLvTdbc3YJl-6uY,8764
transformers/models/canine/__init__.py,sha256=7AYQEAa5qVyCZ73fkPg0yXl5-YpLg55i3RpY1J3KulM,2272
transformers/models/canine/__pycache__/__init__.cpython-310.pyc,,
transformers/models/canine/__pycache__/configuration_canine.cpython-310.pyc,,
transformers/models/canine/__pycache__/convert_canine_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/canine/__pycache__/modeling_canine.cpython-310.pyc,,
transformers/models/canine/__pycache__/tokenization_canine.cpython-310.pyc,,
transformers/models/canine/configuration_canine.py,sha256=CNZxaxHlOKXhJ8pRl82K6tBG4U-u6rcDaW4_94Lw-sw,6766
transformers/models/canine/convert_canine_original_tf_checkpoint_to_pytorch.py,sha256=vGfFFo49PfyXtZdgIQHRcqMPcbmF8aMEC9DiHMyEsn0,2117
transformers/models/canine/modeling_canine.py,sha256=H9pgMkzEChdPLhRTfNdUiHaTTHJD9r8Yk0oBt7tdfMY,73565
transformers/models/canine/tokenization_canine.py,sha256=jr6XQv3grxNmVHn_p4M_mlxE8jh0kz1gDji_OC1xEOY,9430
transformers/models/chinese_clip/__init__.py,sha256=SNfgqh2dGAcoNXXZx-8XFNO3UDriK_yV7vf-M23Qnfk,2919
transformers/models/chinese_clip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/configuration_chinese_clip.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/convert_chinese_clip_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/feature_extraction_chinese_clip.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/image_processing_chinese_clip.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/modeling_chinese_clip.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/processing_chinese_clip.cpython-310.pyc,,
transformers/models/chinese_clip/configuration_chinese_clip.py,sha256=lgz7Qdo6FIoeeRapg2PxUzrCL7Tsy8G5CROpT1Jb9rI,22542
transformers/models/chinese_clip/convert_chinese_clip_original_pytorch_to_hf.py,sha256=-0bnVcdXxStmygkyj6S1hIGCVbpEbe3cM7AoshHH5ZE,5069
transformers/models/chinese_clip/feature_extraction_chinese_clip.py,sha256=znduyOyJ-Qdx4MC5CPb6MFZ-Wrb5PLgHWRh0xfoULR0,1247
transformers/models/chinese_clip/image_processing_chinese_clip.py,sha256=JtobI7mbW4qbodR6CSmu4SbK1hUIaV0sw12dzx5YPRU,15545
transformers/models/chinese_clip/modeling_chinese_clip.py,sha256=fjw2AhKhgYGyn7jfDL_t7rRJoYW5juHCMQwfbVr9rW0,73159
transformers/models/chinese_clip/processing_chinese_clip.py,sha256=qBYQRHQFIeCIzagJfn4KbH9qSodPmR4llukXWZx5oj8,6812
transformers/models/clap/__init__.py,sha256=MOoheQt_0P8KCRlN4QiWyzrskH9dUUfSSF_pZpJEchw,2322
transformers/models/clap/__pycache__/__init__.cpython-310.pyc,,
transformers/models/clap/__pycache__/configuration_clap.cpython-310.pyc,,
transformers/models/clap/__pycache__/convert_clap_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/clap/__pycache__/feature_extraction_clap.cpython-310.pyc,,
transformers/models/clap/__pycache__/modeling_clap.cpython-310.pyc,,
transformers/models/clap/__pycache__/processing_clap.cpython-310.pyc,,
transformers/models/clap/configuration_clap.py,sha256=hNyeg0H84qq2cdgc4ByVoGnqI6uMEfCEnJr00RPIdx0,20637
transformers/models/clap/convert_clap_original_pytorch_to_hf.py,sha256=FqHoVAYXIzfUY9342azwlm9zfSP7QdS8p-u9Q6RE_K4,5149
transformers/models/clap/feature_extraction_clap.py,sha256=rN5ZDLkqtfddEsT6kcFW2OVe7nehoPUE4HM7T3ua5us,18692
transformers/models/clap/modeling_clap.py,sha256=vnW1PeTgAHng560tX3-A25rDxi9-aI9SM8_Tsy8910g,104751
transformers/models/clap/processing_clap.py,sha256=QpXK1vA69fFLzQesu-qetj22YiV_BiO-0cpatq8ViKo,5705
transformers/models/clip/__init__.py,sha256=VfYFTfEeZ3idrCEQ62QyLkgqZxD5-gBFJ7Gd72ecAh8,5115
transformers/models/clip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/clip/__pycache__/configuration_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/convert_clip_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/clip/__pycache__/feature_extraction_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/image_processing_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/modeling_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/modeling_flax_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/modeling_tf_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/processing_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/tokenization_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/tokenization_clip_fast.cpython-310.pyc,,
transformers/models/clip/configuration_clip.py,sha256=yxF4RwlpcsyBHYwSwcOI7iKiwXtGbmwB_ILCAd5TetM,21129
transformers/models/clip/convert_clip_original_pytorch_to_hf.py,sha256=3_eKm-gpqB5DNvL8b3OKSUrjG7YFxqrQl1DBdL_IboA,5306
transformers/models/clip/feature_extraction_clip.py,sha256=hgRfD-s9DoI7tzDLAJ0EW3rSbkY9dOiGqoGClOiRiBM,1172
transformers/models/clip/image_processing_clip.py,sha256=v4s4tPLNA4wPQKnM7l0cNfleaWO3ZHh0cKQJqtCOucA,16110
transformers/models/clip/modeling_clip.py,sha256=xeFRth3Ium_1yboniEeOpzOKflh5ozqquRzqVshSVBA,56645
transformers/models/clip/modeling_flax_clip.py,sha256=lI6nXNoS-52xVNdleuId-PVAuyr7sDsrtikCHCTmg4A,50507
transformers/models/clip/modeling_tf_clip.py,sha256=wQIWYXtdmUTHhVw8lo_NNYeBwY_FiSqNdISck6FFXRg,54435
transformers/models/clip/processing_clip.py,sha256=aZe5MxUDCc5GuxjeV7nJq22G6KRsjvhdbNjAr3nI0OU,6879
transformers/models/clip/tokenization_clip.py,sha256=EddC7lDEThyvOs3KueGoZF5cHBSJKBEttvRKgrYmRX8,21202
transformers/models/clip/tokenization_clip_fast.py,sha256=97dwKorumSFNRgCrs-EQLJJUEJPn8yNx3IgilD_4aT4,7273
transformers/models/clipseg/__init__.py,sha256=XmEjQiZo2l7fQvPX8Tm_rsd3wItyBrBg3gtvDAkOTZM,2179
transformers/models/clipseg/__pycache__/__init__.cpython-310.pyc,,
transformers/models/clipseg/__pycache__/configuration_clipseg.cpython-310.pyc,,
transformers/models/clipseg/__pycache__/convert_clipseg_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/clipseg/__pycache__/modeling_clipseg.cpython-310.pyc,,
transformers/models/clipseg/__pycache__/processing_clipseg.cpython-310.pyc,,
transformers/models/clipseg/configuration_clipseg.py,sha256=2Bq2JYq5xZMc3gp89vk8JfLDHpzhnnEhT_nbtd51DZQ,21077
transformers/models/clipseg/convert_clipseg_original_pytorch_to_hf.py,sha256=kYyPxdpdtt6nSxD65tXUTMbN0xPyyzjfTOOMbQ8OL0Y,11114
transformers/models/clipseg/modeling_clipseg.py,sha256=VVZ3FM1Eq4IKSdtagzZo5QR07ckFC3uYUeOUaXSb_oQ,64570
transformers/models/clipseg/processing_clipseg.py,sha256=TjJdEr9E-Pfn9cum23gcgpMgHgCD5riXATrtPbSTpTk,7896
transformers/models/clvp/__init__.py,sha256=VUtmHMpw33TwZIXIYxV_ImQSKobm9ItMAZnw87Ke4Dg,2396
transformers/models/clvp/__pycache__/__init__.cpython-310.pyc,,
transformers/models/clvp/__pycache__/configuration_clvp.cpython-310.pyc,,
transformers/models/clvp/__pycache__/convert_clvp_to_hf.cpython-310.pyc,,
transformers/models/clvp/__pycache__/feature_extraction_clvp.cpython-310.pyc,,
transformers/models/clvp/__pycache__/modeling_clvp.cpython-310.pyc,,
transformers/models/clvp/__pycache__/number_normalizer.cpython-310.pyc,,
transformers/models/clvp/__pycache__/processing_clvp.cpython-310.pyc,,
transformers/models/clvp/__pycache__/tokenization_clvp.cpython-310.pyc,,
transformers/models/clvp/configuration_clvp.py,sha256=s5f6PchOKe-DJOXcnq7IU7PxNdshSKL8aoETxfovFZs,21067
transformers/models/clvp/convert_clvp_to_hf.py,sha256=1WYf_vwj1CeQ_VU9iMqu7Grr_MmlAsaKEK1Lojk6yM4,9326
transformers/models/clvp/feature_extraction_clvp.py,sha256=rq0Ygr1pCT1DK4mMzv6f4b06zgXeAwT29GYSzu1Fprw,10935
transformers/models/clvp/modeling_clvp.py,sha256=meW3E0NAqgsGwqAesbA9FhZVYzLxfRbnNzZ-fr7aZgU,91235
transformers/models/clvp/number_normalizer.py,sha256=gJb8KFEdsDWgzubs6cTn1i2q2R1fHCYs9C3k2hBoCyU,8857
transformers/models/clvp/processing_clvp.py,sha256=zn13cG8abp5_ZFhoL_QQxcoTRS57rLKXBh9H5KAUBxk,3605
transformers/models/clvp/tokenization_clvp.py,sha256=YXEc0bqH1AD54UNXFXvWQib_9l21cMhCIlDFDRbqeW8,15252
transformers/models/code_llama/__init__.py,sha256=S1xpVZ6cLZxN1ADmRNp7dCsoKQKnb3-Tw-HkHjHcnBY,1882
transformers/models/code_llama/__pycache__/__init__.cpython-310.pyc,,
transformers/models/code_llama/__pycache__/tokenization_code_llama.cpython-310.pyc,,
transformers/models/code_llama/__pycache__/tokenization_code_llama_fast.cpython-310.pyc,,
transformers/models/code_llama/tokenization_code_llama.py,sha256=JFhJvXNKhjlf6DXXnahNo6qWvN0K-umV4CdeFcZ0xkw,23568
transformers/models/code_llama/tokenization_code_llama_fast.py,sha256=REf6FgNg7WbBovoDFKJey0VekXWMFVJGMVfOwHUCZaU,19758
transformers/models/codegen/__init__.py,sha256=Zb96Hyd6W5WaIc7l-psLnEhYjANmwxzZlAR-g37xKkI,2443
transformers/models/codegen/__pycache__/__init__.cpython-310.pyc,,
transformers/models/codegen/__pycache__/configuration_codegen.cpython-310.pyc,,
transformers/models/codegen/__pycache__/modeling_codegen.cpython-310.pyc,,
transformers/models/codegen/__pycache__/tokenization_codegen.cpython-310.pyc,,
transformers/models/codegen/__pycache__/tokenization_codegen_fast.cpython-310.pyc,,
transformers/models/codegen/configuration_codegen.py,sha256=h38yq3btP-MUu7eP3PQmj16JbvV-ZR3YtFJcpTnMy1o,10892
transformers/models/codegen/modeling_codegen.py,sha256=RBM0dtfnvMXYU4KhFbMCA2M3YynaklW028tv5rxXg2E,31712
transformers/models/codegen/tokenization_codegen.py,sha256=1Hz54w863L9XRd5FI--L1GpbRAHLwtqvdTRZhvFn9oY,15455
transformers/models/codegen/tokenization_codegen_fast.py,sha256=XycmVo4GJpMt04VXJ1SbvzTExQajuDvISyoshK9ElBw,10467
transformers/models/conditional_detr/__init__.py,sha256=aFyaZb6RKCOPPf_kPK83WhyaDO5NFiox70ZbMe5gxvw,2828
transformers/models/conditional_detr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/conditional_detr/__pycache__/configuration_conditional_detr.cpython-310.pyc,,
transformers/models/conditional_detr/__pycache__/convert_conditional_detr_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/conditional_detr/__pycache__/feature_extraction_conditional_detr.cpython-310.pyc,,
transformers/models/conditional_detr/__pycache__/image_processing_conditional_detr.cpython-310.pyc,,
transformers/models/conditional_detr/__pycache__/modeling_conditional_detr.cpython-310.pyc,,
transformers/models/conditional_detr/configuration_conditional_detr.py,sha256=lK_fb57ocsKTPfsjtYIdGkD-x-LF8NeDB2f8MdxyAvY,12441
transformers/models/conditional_detr/convert_conditional_detr_original_pytorch_checkpoint_to_pytorch.py,sha256=O0da9fOwcPhpQSaa0Ci34txn-9YF9fAMGvRHK0dCk3Q,15930
transformers/models/conditional_detr/feature_extraction_conditional_detr.py,sha256=opHXZebd-6cMJnO6RbrAdmVYmnkNzK1up_fPlHTSLrk,1553
transformers/models/conditional_detr/image_processing_conditional_detr.py,sha256=xypyIu7XKpeG7q5lk8bQdCpF_VC97qeiFOthQmq68do,76769
transformers/models/conditional_detr/modeling_conditional_detr.py,sha256=qv5qcXwHoiLEkKpvQ17Zo1kGetIRjwL3GAUedR5d_Rk,132069
transformers/models/convbert/__init__.py,sha256=wkLfe2pjkQmfQ0sd28ixnL1__YYimYDtT5FP1bRD0YE,4069
transformers/models/convbert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/convbert/__pycache__/configuration_convbert.cpython-310.pyc,,
transformers/models/convbert/__pycache__/convert_convbert_original_tf1_checkpoint_to_pytorch_and_tf2.cpython-310.pyc,,
transformers/models/convbert/__pycache__/modeling_convbert.cpython-310.pyc,,
transformers/models/convbert/__pycache__/modeling_tf_convbert.cpython-310.pyc,,
transformers/models/convbert/__pycache__/tokenization_convbert.cpython-310.pyc,,
transformers/models/convbert/__pycache__/tokenization_convbert_fast.cpython-310.pyc,,
transformers/models/convbert/configuration_convbert.py,sha256=tw0ktT-WaowJclQaEp69f_hnFJUAwZL78pCajQ4mFYE,7312
transformers/models/convbert/convert_convbert_original_tf1_checkpoint_to_pytorch_and_tf2.py,sha256=vTZyGhG9v7o4rDuP9-xM26gX1EzlCda7Sn_ELT9n3Gk,2108
transformers/models/convbert/modeling_convbert.py,sha256=xDmRwAQZSY7aSJVCz_HEwEvYcCwR6B_3i6OU8_Mc_1Q,58465
transformers/models/convbert/modeling_tf_convbert.py,sha256=TRVpw-MqvNI_ajhOFq0dag3UrlBDy5G8_vc-B2AQZmg,52625
transformers/models/convbert/tokenization_convbert.py,sha256=PZWQ5SRiN8OlpN8TFW9SwBGmgwzLdtcLj5FmepOKiWQ,21967
transformers/models/convbert/tokenization_convbert_fast.py,sha256=OyqpBEU-CsCMDUFFuoYjCGOaAX1AHmlxF5lIT58_C9M,8777
transformers/models/convnext/__init__.py,sha256=K8TKvIQuVogfZPifZjZeCwGJKA_vnASMr7LWx4CggqA,3150
transformers/models/convnext/__pycache__/__init__.cpython-310.pyc,,
transformers/models/convnext/__pycache__/configuration_convnext.cpython-310.pyc,,
transformers/models/convnext/__pycache__/convert_convnext_to_pytorch.cpython-310.pyc,,
transformers/models/convnext/__pycache__/feature_extraction_convnext.cpython-310.pyc,,
transformers/models/convnext/__pycache__/image_processing_convnext.cpython-310.pyc,,
transformers/models/convnext/__pycache__/modeling_convnext.cpython-310.pyc,,
transformers/models/convnext/__pycache__/modeling_tf_convnext.cpython-310.pyc,,
transformers/models/convnext/configuration_convnext.py,sha256=D-pxxR9QZjjmm_jSydtSJZgPxT7fU2Kgh-hs0FFzIrc,6202
transformers/models/convnext/convert_convnext_to_pytorch.py,sha256=6QenssUB5Op--7nvPTPjRUEozX-4kljweJvc-blSpnQ,10220
transformers/models/convnext/feature_extraction_convnext.py,sha256=TyFMochXYlN3vKH7Ud0nXagzxGhio2Bfma4ofceR_zA,1200
transformers/models/convnext/image_processing_convnext.py,sha256=FgS4uuAULrth-hspPVCKaFtSe1axtrCzmIjZ-e8f7Pc,16021
transformers/models/convnext/modeling_convnext.py,sha256=ASWQlDANTUrtDyYv3QszasjqF388eQjb3NPvCFrGbEA,21942
transformers/models/convnext/modeling_tf_convnext.py,sha256=1jO4EFEIatr7BW7upyJqHUVFHEqC6X6ZQap-49z20mk,23528
transformers/models/convnextv2/__init__.py,sha256=JmOrlR6-q7yFZqSG7obPonJSuSpLVhTOIax7X-3FDwY,2825
transformers/models/convnextv2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/convnextv2/__pycache__/configuration_convnextv2.cpython-310.pyc,,
transformers/models/convnextv2/__pycache__/convert_convnextv2_to_pytorch.cpython-310.pyc,,
transformers/models/convnextv2/__pycache__/modeling_convnextv2.cpython-310.pyc,,
transformers/models/convnextv2/__pycache__/modeling_tf_convnextv2.cpython-310.pyc,,
transformers/models/convnextv2/configuration_convnextv2.py,sha256=2N3oMxlWsyyL-rCUOTD9ExeTlIJ4ybvYevMeq5PVq8s,5431
transformers/models/convnextv2/convert_convnextv2_to_pytorch.py,sha256=Yswl5UwLP0t0tC8O2b8wix2beNaMtPy7areKFCuEccg,12473
transformers/models/convnextv2/modeling_convnextv2.py,sha256=C5n4A_CE38KLzWgvdH_SDlH5elenPo4J0O3YQi0NoNo,23723
transformers/models/convnextv2/modeling_tf_convnextv2.py,sha256=0luNKbn-emET_Xm-YbDMn_f6cxsM330QSt-23B8wdNc,23981
transformers/models/cpm/__init__.py,sha256=9SmT0nL5DgGjXxmPaQFi9GGPXWuhFic2DX2GsF-BynQ,1816
transformers/models/cpm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/cpm/__pycache__/tokenization_cpm.cpython-310.pyc,,
transformers/models/cpm/__pycache__/tokenization_cpm_fast.cpython-310.pyc,,
transformers/models/cpm/tokenization_cpm.py,sha256=fnIP4gBquryMnWxmi6waP6TFoOD-PQGnW0A8QRkYkhk,15257
transformers/models/cpm/tokenization_cpm_fast.py,sha256=K7za8eoExrXm8ZsH_TOY1IXnZ7PwzJeGrS_seXBY39c,10741
transformers/models/cpmant/__init__.py,sha256=5hTyJtQwoONrf9-BMvt_nT_bovkj9avoSk9UdLCvW4w,2117
transformers/models/cpmant/__pycache__/__init__.cpython-310.pyc,,
transformers/models/cpmant/__pycache__/configuration_cpmant.cpython-310.pyc,,
transformers/models/cpmant/__pycache__/modeling_cpmant.cpython-310.pyc,,
transformers/models/cpmant/__pycache__/tokenization_cpmant.cpython-310.pyc,,
transformers/models/cpmant/configuration_cpmant.py,sha256=NO34Xj5AsTq64_L6ELH7zJew6_4IDTEbvKLEMXFif-Y,5331
transformers/models/cpmant/modeling_cpmant.py,sha256=g6RiojhjKMr6NE9z8SmehocOeV8FBC9wAA8hBtAsPkc,37560
transformers/models/cpmant/tokenization_cpmant.py,sha256=ESWl2D6v9STC9QArkFX-SvnFFj9qjyZKNK-5fBO_xxU,10075
transformers/models/ctrl/__init__.py,sha256=-Sa7nUQv3Cxj4KLXFaBtnkG_r3uIdpbU_Q_TmMl1lKM,2688
transformers/models/ctrl/__pycache__/__init__.cpython-310.pyc,,
transformers/models/ctrl/__pycache__/configuration_ctrl.cpython-310.pyc,,
transformers/models/ctrl/__pycache__/modeling_ctrl.cpython-310.pyc,,
transformers/models/ctrl/__pycache__/modeling_tf_ctrl.cpython-310.pyc,,
transformers/models/ctrl/__pycache__/tokenization_ctrl.cpython-310.pyc,,
transformers/models/ctrl/configuration_ctrl.py,sha256=nQu7QgY0XgPDa3yHewO9YnX1aSLzv-YS3Z8gpPIloRk,4789
transformers/models/ctrl/modeling_ctrl.py,sha256=vXCnYFksr-I7gEZXrIRWr2E0tNq9Avqz0CFMXUjx8d4,35515
transformers/models/ctrl/modeling_tf_ctrl.py,sha256=o-2DzYxZO41A2VN8Y6m3zP-JrGXNMC4vkaI_HZ_yYJA,35965
transformers/models/ctrl/tokenization_ctrl.py,sha256=79kPiPr4SNZDzU49tmTJLe-BvN4TuJOwf9_j0soyrDY,8490
transformers/models/cvt/__init__.py,sha256=dk1C0zaBDT0dl7BYLe1mRb85Dp_a_IHomekjOjYPHJ8,2434
transformers/models/cvt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/cvt/__pycache__/configuration_cvt.cpython-310.pyc,,
transformers/models/cvt/__pycache__/convert_cvt_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/cvt/__pycache__/modeling_cvt.cpython-310.pyc,,
transformers/models/cvt/__pycache__/modeling_tf_cvt.cpython-310.pyc,,
transformers/models/cvt/configuration_cvt.py,sha256=8KFh8F1jxe9EFX1Ke0jfjLEvJMG4_S5LgGKBfGkXXog,6861
transformers/models/cvt/convert_cvt_original_pytorch_checkpoint_to_pytorch.py,sha256=miqNzPWIAjwl5rtkWOmRUJl-18X-9cRXXWb9M3ScHI4,13570
transformers/models/cvt/modeling_cvt.py,sha256=EVdSh7C6hfYanzIQITg8YX9MIZvp6zsAKWopjADebuQ,28943
transformers/models/cvt/modeling_tf_cvt.py,sha256=r34I56oMvqjG7Fuuw6aM86lva6YuNk9EOsCTqkH_wac,36051
transformers/models/data2vec/__init__.py,sha256=1Pq8n8wNccLQ76e8oNDwOemqh-E0eMKpr6tdt2ata8w,4933
transformers/models/data2vec/__pycache__/__init__.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_audio.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_text.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_vision.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/convert_data2vec_audio_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/convert_data2vec_text_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/convert_data2vec_vision_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_audio.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_text.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_vision.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/modeling_tf_data2vec_vision.cpython-310.pyc,,
transformers/models/data2vec/configuration_data2vec_audio.py,sha256=FdLRyMjwHwpQAdOi6BqWF_u-wBAT9lO1vpFf4XZi-1s,16584
transformers/models/data2vec/configuration_data2vec_text.py,sha256=cX0pr2HTyus3TYjJpu4dmdF1jibViHQqpve8d2pJP5M,7421
transformers/models/data2vec/configuration_data2vec_vision.py,sha256=sohmtbN8PqkgDgCLeCqsXfi8d8oImLLlhx0wcXrt7fI,9433
transformers/models/data2vec/convert_data2vec_audio_original_pytorch_checkpoint_to_pytorch.py,sha256=czYaA_tlF-uCDMFV1RFaL5g8QJRozBiVUCu9nuhLcZU,10858
transformers/models/data2vec/convert_data2vec_text_original_pytorch_checkpoint_to_pytorch.py,sha256=4scSS9J1m1xG6sy_BLvjbCeEL8Ke2RhNtNqsVt2zUCI,9580
transformers/models/data2vec/convert_data2vec_vision_original_pytorch_checkpoint_to_pytorch.py,sha256=qKjV-jqIgL-6i17m4yQLW_93SbPpGxQnvHjuy1xVxQU,15340
transformers/models/data2vec/modeling_data2vec_audio.py,sha256=4cQrB8jtpVHgDttD9qRHzKBSfYKzOCNTtQG2EDAbyMM,65114
transformers/models/data2vec/modeling_data2vec_text.py,sha256=01ZGcQFC2kUgZlgZLQ4E7M3X3AYSDBhouPRoEHKbnFI,71344
transformers/models/data2vec/modeling_data2vec_vision.py,sha256=b77WwEGzhbZY0gt8CO3sYjCghi6HUV21sgzfDM4n4ak,53838
transformers/models/data2vec/modeling_tf_data2vec_vision.py,sha256=w9ZRpwvv7yFar2iGnBFihlMduaXmd4TtRPu8P0GosCA,60423
transformers/models/deberta/__init__.py,sha256=azYcZaZso6o7T3SDyUrczkAZ4ZzgDh4hcPoT0bgPRSE,3677
transformers/models/deberta/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deberta/__pycache__/configuration_deberta.cpython-310.pyc,,
transformers/models/deberta/__pycache__/modeling_deberta.cpython-310.pyc,,
transformers/models/deberta/__pycache__/modeling_tf_deberta.cpython-310.pyc,,
transformers/models/deberta/__pycache__/tokenization_deberta.cpython-310.pyc,,
transformers/models/deberta/__pycache__/tokenization_deberta_fast.cpython-310.pyc,,
transformers/models/deberta/configuration_deberta.py,sha256=FSPMuu2tGc5qSZm_bTxpDyo2A2mPQJeyFtvs9g6pPJw,9394
transformers/models/deberta/modeling_deberta.py,sha256=TIV-j31lWPgCHVhuucTs4GAiG1Yp-bL9TiBe2hZQvSg,58066
transformers/models/deberta/modeling_tf_deberta.py,sha256=qwHusyX4MD_W3tu2bppxnXguc3JZrux9JTotbNXH6kg,60370
transformers/models/deberta/tokenization_deberta.py,sha256=SHsmOHhItaBAHByQNQM3_r0E78uufAHbuXB8qvWCzps,19111
transformers/models/deberta/tokenization_deberta_fast.py,sha256=p9GyZ8E1-JRQ7Ap5UVZGGKF6B0yXtkOGTWkt8N1nMgo,12781
transformers/models/deberta_v2/__init__.py,sha256=afG1pzu0TIczwpL6vPJXnwkO5Sn9R5qrMvjaTzysH1U,3981
transformers/models/deberta_v2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deberta_v2/__pycache__/configuration_deberta_v2.cpython-310.pyc,,
transformers/models/deberta_v2/__pycache__/modeling_deberta_v2.cpython-310.pyc,,
transformers/models/deberta_v2/__pycache__/modeling_tf_deberta_v2.cpython-310.pyc,,
transformers/models/deberta_v2/__pycache__/tokenization_deberta_v2.cpython-310.pyc,,
transformers/models/deberta_v2/__pycache__/tokenization_deberta_v2_fast.cpython-310.pyc,,
transformers/models/deberta_v2/configuration_deberta_v2.py,sha256=7WyxCnBsJ1Kf3grwN19m6SHia_9onZl9WADAO0F7Xss,9180
transformers/models/deberta_v2/modeling_deberta_v2.py,sha256=Oglza986w7VrrR_SHXC7gxJILLJluZ3mz4gNOb34nMw,67591
transformers/models/deberta_v2/modeling_tf_deberta_v2.py,sha256=_n9AWW9In14z0FgT69SeP5fZe1AM6Z9n5U06iD3SBYg,71217
transformers/models/deberta_v2/tokenization_deberta_v2.py,sha256=u7NFCDR9H0fryg2ddJu7IXAEadufJYCn3TdmMh9dQ5w,22003
transformers/models/deberta_v2/tokenization_deberta_v2_fast.py,sha256=Jz-3J-FOx0kp0aMT5EhtHCnZCLI4_LPL5ABTMfIn0A0,11058
transformers/models/decision_transformer/__init__.py,sha256=geVmBybTFepK0keGuRrLYl6hwZhT5I2BK4dfeYFDqWw,2124
transformers/models/decision_transformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/decision_transformer/__pycache__/configuration_decision_transformer.cpython-310.pyc,,
transformers/models/decision_transformer/__pycache__/modeling_decision_transformer.cpython-310.pyc,,
transformers/models/decision_transformer/configuration_decision_transformer.py,sha256=lI5Yh4GNB4nG-96XfywcCwKVm-yzvPHme_EndgkfJCQ,7321
transformers/models/decision_transformer/modeling_decision_transformer.py,sha256=6jf14SIfEBnCA2PObcLRdfFbZk3PhCLX7xc8er0VkYs,43153
transformers/models/deformable_detr/__init__.py,sha256=jwNDOMAnuD5Efvu3FYvA1H9JJB9QBb6NpoaoCCJU1Ns,2599
transformers/models/deformable_detr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/configuration_deformable_detr.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/convert_deformable_detr_to_pytorch.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/feature_extraction_deformable_detr.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/image_processing_deformable_detr.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/load_custom.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/modeling_deformable_detr.cpython-310.pyc,,
transformers/models/deformable_detr/configuration_deformable_detr.py,sha256=U1DYmxOVMotxmz-LiLsaSgGTji0z12Qo6_ndpLrhuTg,13712
transformers/models/deformable_detr/convert_deformable_detr_to_pytorch.py,sha256=264dW2XMu4QcgO6IaMa4eOjrIHErz-RLw_9FLD6C46Q,9477
transformers/models/deformable_detr/feature_extraction_deformable_detr.py,sha256=GwYaT6B6-Fu2Jbl8CALodb7Lz4gr9jSRfq01QfLQc7Y,1546
transformers/models/deformable_detr/image_processing_deformable_detr.py,sha256=BYQEilW-JvjHMxaAKi9zoGVHqozWkNp458tFcBWt06I,64236
transformers/models/deformable_detr/load_custom.py,sha256=0jENX1Mkz0bYlyUYYgp1YYEpQ8r32degzoL4CmVGe3w,1559
transformers/models/deformable_detr/modeling_deformable_detr.py,sha256=bVaeIuGL3eW7LLYzfEF_BSwUhr64ihyEhZVD9_LOAjI,119548
transformers/models/deit/__init__.py,sha256=ZVWuhflGzxt-AZ2wcCTX0JfXBY3puVD_O9WkNqfOH1A,3486
transformers/models/deit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deit/__pycache__/configuration_deit.cpython-310.pyc,,
transformers/models/deit/__pycache__/convert_deit_timm_to_pytorch.cpython-310.pyc,,
transformers/models/deit/__pycache__/feature_extraction_deit.cpython-310.pyc,,
transformers/models/deit/__pycache__/image_processing_deit.cpython-310.pyc,,
transformers/models/deit/__pycache__/modeling_deit.cpython-310.pyc,,
transformers/models/deit/__pycache__/modeling_tf_deit.cpython-310.pyc,,
transformers/models/deit/configuration_deit.py,sha256=VR8zqFGw6kdZF4MQpPeF0v7sBOVxApeewo2hYO2PJOw,5956
transformers/models/deit/convert_deit_timm_to_pytorch.py,sha256=JMCXzccvcbz1euXpqx-pb86V2PVDLKl-OYbFDLvvSZU,9217
transformers/models/deit/feature_extraction_deit.py,sha256=1j_aV0oAZUofSYJGCEFRo0WNd_zVEXjj3SFlTQSuV1E,1172
transformers/models/deit/image_processing_deit.py,sha256=pjuPcUISzZklpBdbMbFEOceiQL4WEtw2CGKPibKpzy8,15335
transformers/models/deit/modeling_deit.py,sha256=1XGb_G7YPpVqtb_0lA4rupdLBK7h6zLCWATO1F6Kgrw,38218
transformers/models/deit/modeling_tf_deit.py,sha256=XhyWU8qD-7bhHx0OWP3kSKEKq1vrPXE8M0-souVo0p0,42417
transformers/models/deprecated/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/deprecated/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/bort/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/deprecated/bort/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/bort/__pycache__/convert_bort_original_gluonnlp_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/bort/convert_bort_original_gluonnlp_checkpoint_to_pytorch.py,sha256=EsdvMpRKbCcbePo7unHMc_6Ueg32D5FPA9TACGYEQiM,14057
transformers/models/deprecated/mctct/__init__.py,sha256=Rbzjcs6HiXhpUeaKRE6Qtj9XsIRLkUrFAiQnbOerMrM,1892
transformers/models/deprecated/mctct/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/mctct/__pycache__/configuration_mctct.cpython-310.pyc,,
transformers/models/deprecated/mctct/__pycache__/feature_extraction_mctct.cpython-310.pyc,,
transformers/models/deprecated/mctct/__pycache__/modeling_mctct.cpython-310.pyc,,
transformers/models/deprecated/mctct/__pycache__/processing_mctct.cpython-310.pyc,,
transformers/models/deprecated/mctct/configuration_mctct.py,sha256=5b5lOcfKzRfId65DNkj8FVU9I1sPK3-ndXeTUH2KiUE,9302
transformers/models/deprecated/mctct/feature_extraction_mctct.py,sha256=JsaSE20NeqBX8Uw-07Y5HdUcQtbYZqCrTN18Wu2B4rI,13460
transformers/models/deprecated/mctct/modeling_mctct.py,sha256=VdH14mNSM0UI3siMNF-_CXGvnj-Z62exIcP1ogvWAFo,32947
transformers/models/deprecated/mctct/processing_mctct.py,sha256=0ejBpQWA6YVuU0A7hrFg797hFZnOO7GexVU5Da7xLP0,5930
transformers/models/deprecated/mmbt/__init__.py,sha256=0CCmesCwGIMNFlf2oDsL0gYaCSpsfAC1_bMOXRcAgF4,1480
transformers/models/deprecated/mmbt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/mmbt/__pycache__/configuration_mmbt.cpython-310.pyc,,
transformers/models/deprecated/mmbt/__pycache__/modeling_mmbt.cpython-310.pyc,,
transformers/models/deprecated/mmbt/configuration_mmbt.py,sha256=agMAOVRnUrMlA8C6adBRLTuLmt8qG4lm4ykjGwS-qs4,1606
transformers/models/deprecated/mmbt/modeling_mmbt.py,sha256=uiE4HNXfJ0DtptRToRMFbWow0NsuqoHI7toI3DwAsr8,18890
transformers/models/deprecated/open_llama/__init__.py,sha256=Mlmat1Ln8JLYZcldnGrMfBdgOwM01CmsoQEFedbJ24g,2788
transformers/models/deprecated/open_llama/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/open_llama/__pycache__/configuration_open_llama.cpython-310.pyc,,
transformers/models/deprecated/open_llama/__pycache__/modeling_open_llama.cpython-310.pyc,,
transformers/models/deprecated/open_llama/configuration_open_llama.py,sha256=6Hj56fiYPIXARvZCHpu-tjkCt1WRaeWsfzt_XcS0IKo,7857
transformers/models/deprecated/open_llama/modeling_open_llama.py,sha256=NwH5PsL5b7mpRfNMm99dx_xljXIjBFw-ZEVVrkGuWIE,43550
transformers/models/deprecated/retribert/__init__.py,sha256=yMGneTgD7_VaMhXG00Liyvt4digAfyQ_j6Ou55p8iEU,2351
transformers/models/deprecated/retribert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/retribert/__pycache__/configuration_retribert.cpython-310.pyc,,
transformers/models/deprecated/retribert/__pycache__/modeling_retribert.cpython-310.pyc,,
transformers/models/deprecated/retribert/__pycache__/tokenization_retribert.cpython-310.pyc,,
transformers/models/deprecated/retribert/__pycache__/tokenization_retribert_fast.cpython-310.pyc,,
transformers/models/deprecated/retribert/configuration_retribert.py,sha256=APvX5UKmf3utR32F5oUr_gYw7vGyxsC-bx87ujRFCE8,5408
transformers/models/deprecated/retribert/modeling_retribert.py,sha256=pT9VHCn8k7_Zrj61lvybMYweOxAlauYO8mz5ErlRhI4,9465
transformers/models/deprecated/retribert/tokenization_retribert.py,sha256=6YMrjNWWQ2SQPE-bPhsvXrb-4JZOVYOXekTUcJqCwiM,22683
transformers/models/deprecated/retribert/tokenization_retribert_fast.py,sha256=_9-xP_fFDrHkeLKUySJCq1BL6hwFJHMCKoEIam6Yw8o,9029
transformers/models/deprecated/tapex/__init__.py,sha256=lQutKYtwbU8ztPva0tyRnnV-zOWw6rxkGyoOUSuvnUo,926
transformers/models/deprecated/tapex/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/tapex/__pycache__/tokenization_tapex.cpython-310.pyc,,
transformers/models/deprecated/tapex/tokenization_tapex.py,sha256=H9iOn3soYK11srHRKhEIbWgYbZpnHxvVBHHClIQQpYE,65004
transformers/models/deprecated/trajectory_transformer/__init__.py,sha256=NZl7qNHOSc-VlOFIvhh4iSpn_fyGHZ8k7a9WXXG5HGg,2077
transformers/models/deprecated/trajectory_transformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/configuration_trajectory_transformer.cpython-310.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/convert_trajectory_transformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/modeling_trajectory_transformer.cpython-310.pyc,,
transformers/models/deprecated/trajectory_transformer/configuration_trajectory_transformer.py,sha256=odKjaQb-ZDN4oSF1R5NDTCKGea4ZX2TbL80nXY4ztx0,7414
transformers/models/deprecated/trajectory_transformer/convert_trajectory_transformer_original_pytorch_checkpoint_to_pytorch.py,sha256=9jmCO1yueIbzUUvOHCl62XDCG4ExTkvsgRVCe-aBG7U,3139
transformers/models/deprecated/trajectory_transformer/modeling_trajectory_transformer.py,sha256=HwPYTZkN3S1h6bYeo3cbiS45Q5IlbPrZk9WdG-7ezyE,25823
transformers/models/deprecated/transfo_xl/__init__.py,sha256=bO5xiMeUsfu9k2nqJ4N2qTGvSniyD9oA8rHEn46ne-0,3183
transformers/models/deprecated/transfo_xl/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/configuration_transfo_xl.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/convert_transfo_xl_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_tf_transfo_xl.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_tf_transfo_xl_utilities.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_transfo_xl.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_transfo_xl_utilities.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/tokenization_transfo_xl.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/configuration_transfo_xl.py,sha256=AdHoUoyFjoqZVFSiCgZ7H0FmJY4hwrB4yXbzQX6vs7k,7993
transformers/models/deprecated/transfo_xl/convert_transfo_xl_original_tf_checkpoint_to_pytorch.py,sha256=cUL10fYCG-kWYI3BHuKto2AIxb0V2pgPQ3Z8JU9G-Sg,4938
transformers/models/deprecated/transfo_xl/modeling_tf_transfo_xl.py,sha256=ymCDEoY_vTtoHT6APkxkusKj4_FhWEPmmVV69n3EwQs,46104
transformers/models/deprecated/transfo_xl/modeling_tf_transfo_xl_utilities.py,sha256=XOyFuBHmtqoZFtqI9yUS94th6uvmfqEahvyi8mdkNu4,7598
transformers/models/deprecated/transfo_xl/modeling_transfo_xl.py,sha256=7sRo5W88FaMvK40IPBigvZspg_sCE0HtFDucNfstnJQ,55790
transformers/models/deprecated/transfo_xl/modeling_transfo_xl_utilities.py,sha256=oZAsrKz41ek-kSV2rvFHyCHfkAM6e5NyqbGCZSxIML4,10861
transformers/models/deprecated/transfo_xl/tokenization_transfo_xl.py,sha256=zaJK8fmsyt61y8OLiWya6zTDbjcpyplScJQZb-BzshU,32307
transformers/models/deprecated/van/__init__.py,sha256=LfVeE-QGxQJS0QZhWPmPD9s2yX5Pk9iA5NK90CkoyQQ,1728
transformers/models/deprecated/van/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/van/__pycache__/configuration_van.cpython-310.pyc,,
transformers/models/deprecated/van/__pycache__/convert_van_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/van/__pycache__/modeling_van.cpython-310.pyc,,
transformers/models/deprecated/van/configuration_van.py,sha256=8h4d2uHBFAiiHAMqtqhAv_fsqdrqxXxFfIcPE3sabgs,4838
transformers/models/deprecated/van/convert_van_to_pytorch.py,sha256=KW-0r4GVcmH_EzxC-qsdUn5TJw4TEl0wmUKPnJPYZaw,10374
transformers/models/deprecated/van/modeling_van.py,sha256=h3u6GcsTPcQMCGe-hZ4B-_XJHId5C8mCEUyHTK31vh4,21450
transformers/models/deta/__init__.py,sha256=eHgP2aY7a0Of2OkxgCPavzEYvqk2etS3aqXD23Zd3Rc,2205
transformers/models/deta/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deta/__pycache__/configuration_deta.cpython-310.pyc,,
transformers/models/deta/__pycache__/convert_deta_resnet_to_pytorch.cpython-310.pyc,,
transformers/models/deta/__pycache__/convert_deta_swin_to_pytorch.cpython-310.pyc,,
transformers/models/deta/__pycache__/image_processing_deta.cpython-310.pyc,,
transformers/models/deta/__pycache__/modeling_deta.cpython-310.pyc,,
transformers/models/deta/configuration_deta.py,sha256=_frP8azyqvrwIa31clSWAE-v8K8gt_vOjgJIiG6Ad3U,11457
transformers/models/deta/convert_deta_resnet_to_pytorch.py,sha256=r-beTAdmCNONvgIPQmIf890KgDQmdi8mRoDkSWoumJg,16833
transformers/models/deta/convert_deta_swin_to_pytorch.py,sha256=WL18erfLKYr7-pmcHC5i5t6it7EnSagPsuHs5VEgLEA,19031
transformers/models/deta/image_processing_deta.py,sha256=8WjyChPhFQM2mLbVfx17Pd6zKo1GyVlftwsr-tQSiOk,48627
transformers/models/deta/modeling_deta.py,sha256=2NMHqHvIO4Y9PpWO9YqSusp937CYFw4bj4-tbyc17ys,135020
transformers/models/detr/__init__.py,sha256=dWemW6cL_QLOXK3i2uoP6ywKNrjVkpw8IXeQYbs0HfA,2438
transformers/models/detr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/detr/__pycache__/configuration_detr.cpython-310.pyc,,
transformers/models/detr/__pycache__/convert_detr_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/detr/__pycache__/convert_detr_to_pytorch.cpython-310.pyc,,
transformers/models/detr/__pycache__/feature_extraction_detr.cpython-310.pyc,,
transformers/models/detr/__pycache__/image_processing_detr.cpython-310.pyc,,
transformers/models/detr/__pycache__/modeling_detr.cpython-310.pyc,,
transformers/models/detr/configuration_detr.py,sha256=IALXaJfghsL2g1Mdz_GdkDEUw-T5y32nwYONDrpsCik,12691
transformers/models/detr/convert_detr_original_pytorch_checkpoint_to_pytorch.py,sha256=_4fQ1N3Zat1x1r-Gr3FosWuV3pW3yFKQQgM9MKujmbY,13561
transformers/models/detr/convert_detr_to_pytorch.py,sha256=_E63l9rWZUfwSHCfJbz-HoIDT4hxAwoHRKXj1Ni03AA,18993
transformers/models/detr/feature_extraction_detr.py,sha256=gMyG16pNJKoimImXOyqi589hGj37OYGWb7ZoTx84d5I,1474
transformers/models/detr/image_processing_detr.py,sha256=St3yJ_k5dpBsEHID8Gqi7cf8oN92150aIIn-bLkrhTk,84738
transformers/models/detr/modeling_detr.py,sha256=DCIpRe6VTx84icQFCb_OF0aNfyUrFe7VmnSax6FEtUY,116398
transformers/models/dialogpt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/dialogpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dialogpt/__pycache__/convert_dialogpt_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/dialogpt/convert_dialogpt_original_pytorch_checkpoint_to_pytorch.py,sha256=Zp59TmLBKEs-x1-quZZeqARhpS3cTnnmgT4nCI0zsHY,1537
transformers/models/dinat/__init__.py,sha256=Jt3EAbCCZcBjJD_sEane9NU0btqsFkOTqz6JkUtmY_4,1812
transformers/models/dinat/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dinat/__pycache__/configuration_dinat.cpython-310.pyc,,
transformers/models/dinat/__pycache__/modeling_dinat.cpython-310.pyc,,
transformers/models/dinat/configuration_dinat.py,sha256=9-ORFxeg1JRU1cvAyWlcPIueN1q6SD7fMhJGDiBuSic,7399
transformers/models/dinat/modeling_dinat.py,sha256=nZvyQYMgow-YITst6L1RLVf7YULGQn5eDzJy-1U-Mxg,41729
transformers/models/dinov2/__init__.py,sha256=vQdLyp1VnVfmx0Vdvwvgvk9bsWCUArt-hPzzoDsA20I,1890
transformers/models/dinov2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dinov2/__pycache__/configuration_dinov2.cpython-310.pyc,,
transformers/models/dinov2/__pycache__/convert_dinov2_to_hf.cpython-310.pyc,,
transformers/models/dinov2/__pycache__/modeling_dinov2.cpython-310.pyc,,
transformers/models/dinov2/configuration_dinov2.py,sha256=EF0wYEug2uQT1M6H9s08ZsjR-vMUnFHGYbtie8XZl90,8024
transformers/models/dinov2/convert_dinov2_to_hf.py,sha256=g4wmiqVdUlNbRoy_GbEws3DQaXfUA1I9Qh6bHhL6yZk,11964
transformers/models/dinov2/modeling_dinov2.py,sha256=IbmyxglWz9OLSjLa-_zFeM6UnR-LRAlbtL168I3OaZ8,36219
transformers/models/distilbert/__init__.py,sha256=64w_AOUP-vupRT6bGlQF7Ak24rJB5AX58n1V8V_aHM0,5167
transformers/models/distilbert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/configuration_distilbert.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/modeling_distilbert.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/modeling_flax_distilbert.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/modeling_tf_distilbert.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/tokenization_distilbert.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/tokenization_distilbert_fast.cpython-310.pyc,,
transformers/models/distilbert/configuration_distilbert.py,sha256=IFyo18RrxX0C9i4aaBKq0_Kexsw-e17XGytZSYcRubo,6979
transformers/models/distilbert/modeling_distilbert.py,sha256=SXRPZo7koWLYsDwSEkT903pFlD80FJXwGbziq9D4erM,61788
transformers/models/distilbert/modeling_flax_distilbert.py,sha256=9kbcM0l7TtMEiE55J1PHwVh5J-JTiXvjlWYZ8zk9bLg,32628
transformers/models/distilbert/modeling_tf_distilbert.py,sha256=mfclq-KxAQlLm9E1kGPQJr-4mEsNmdcpXjrrjIh4mT0,42933
transformers/models/distilbert/tokenization_distilbert.py,sha256=iteMI8vbds3HhchcTDQ8bZ0aRUxMt2TSVODCh2CPIzo,23695
transformers/models/distilbert/tokenization_distilbert_fast.py,sha256=cTVfYemhriohlvB7Z1jFZodwfaKkdaznNj7BBPqpJiQ,10720
transformers/models/dit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/dit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dit/__pycache__/convert_dit_unilm_to_pytorch.cpython-310.pyc,,
transformers/models/dit/convert_dit_unilm_to_pytorch.py,sha256=qoCC3Hm-enjzLj5LoxjbpP8EaIsyhi3U3PERYYeSt7c,9420
transformers/models/donut/__init__.py,sha256=VraCMZ5ZG0WtYvLmZv-B-gIH5joEM_QdAkiH2iDjLls,2455
transformers/models/donut/__pycache__/__init__.cpython-310.pyc,,
transformers/models/donut/__pycache__/configuration_donut_swin.cpython-310.pyc,,
transformers/models/donut/__pycache__/convert_donut_to_pytorch.cpython-310.pyc,,
transformers/models/donut/__pycache__/feature_extraction_donut.cpython-310.pyc,,
transformers/models/donut/__pycache__/image_processing_donut.cpython-310.pyc,,
transformers/models/donut/__pycache__/modeling_donut_swin.cpython-310.pyc,,
transformers/models/donut/__pycache__/processing_donut.cpython-310.pyc,,
transformers/models/donut/configuration_donut_swin.py,sha256=yMrc2WA182D1i1AcfagE2783D9sQiwvotsiqAOCgPLo,5990
transformers/models/donut/convert_donut_to_pytorch.py,sha256=0IgQ3V9hNWPOJ6KtOfowhVMfTh1m4WEVLOAQSMEGjJE,9316
transformers/models/donut/feature_extraction_donut.py,sha256=jBSpDfoiCg_IWr4gcphIcxs7DA760JnH6V6hAfaoYPM,1179
transformers/models/donut/image_processing_donut.py,sha256=VgbNUVKi2AI7qH7JXwCHkJRLab4lufYfZe6ceXaOalk,21737
transformers/models/donut/modeling_donut_swin.py,sha256=20MDGDPI48ak-XpR8rHvXp4BCXeHITq0vtkaYfhUP6E,43366
transformers/models/donut/processing_donut.py,sha256=FQ00liC2m5vvrZN1njJkTM8WQtrPw4Fv_Hv4DYmfoh0,8170
transformers/models/dpr/__init__.py,sha256=qc_Fe-hF94ZxS9cfEXCp9h7-tkmi9Tj4KV9h_wg6yhs,4535
transformers/models/dpr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dpr/__pycache__/configuration_dpr.cpython-310.pyc,,
transformers/models/dpr/__pycache__/convert_dpr_original_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/dpr/__pycache__/modeling_dpr.cpython-310.pyc,,
transformers/models/dpr/__pycache__/modeling_tf_dpr.cpython-310.pyc,,
transformers/models/dpr/__pycache__/tokenization_dpr.cpython-310.pyc,,
transformers/models/dpr/__pycache__/tokenization_dpr_fast.cpython-310.pyc,,
transformers/models/dpr/configuration_dpr.py,sha256=XmTGhGd06PQdN1sde5TNJbxIFrMCJkK0wcEKKwmObWY,7350
transformers/models/dpr/convert_dpr_original_checkpoint_to_pytorch.py,sha256=ZXYKH4nKB0oc-pOj5_6_m2P1Ze4WClabrmEZLaNcyvI,6096
transformers/models/dpr/modeling_dpr.py,sha256=X5bk6QMtcwtP3_z1R64h1HSmf9m_0qJdydI-kMgr-7k,28719
transformers/models/dpr/modeling_tf_dpr.py,sha256=_vQ_RNiL1qDv8BwUzOzyyVbrGAMb4YzIIegBFqJSP7g,32166
transformers/models/dpr/tokenization_dpr.py,sha256=MonNVmQeNBUp328ReY-hNux9hxgY0zghl_2utZLq1Gk,19789
transformers/models/dpr/tokenization_dpr_fast.py,sha256=x4f1UxOzkygehKv6RYr7Gh4_BbDYU7NEzn5s29mSubw,20175
transformers/models/dpt/__init__.py,sha256=WoC0ADjpTTkspHtgIX_TtHXXG-4t8S-NGgJaAUiG-q4,2444
transformers/models/dpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dpt/__pycache__/configuration_dpt.cpython-310.pyc,,
transformers/models/dpt/__pycache__/convert_dinov2_depth_to_hf.cpython-310.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_beit_to_hf.cpython-310.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_hybrid_to_pytorch.cpython-310.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_to_pytorch.cpython-310.pyc,,
transformers/models/dpt/__pycache__/feature_extraction_dpt.cpython-310.pyc,,
transformers/models/dpt/__pycache__/image_processing_dpt.cpython-310.pyc,,
transformers/models/dpt/__pycache__/modeling_dpt.cpython-310.pyc,,
transformers/models/dpt/configuration_dpt.py,sha256=wCNNUeF5J6cx7MQEG5u-Q2JA2YWWUbNRr6e00eOYjtg,12776
transformers/models/dpt/convert_dinov2_depth_to_hf.py,sha256=azN2ivIGa-g5fe6kdkQ0kJbgKitt10k8C2R3x3ff6FI,16935
transformers/models/dpt/convert_dpt_beit_to_hf.py,sha256=FwNe_D_7eyCCPR8qeRAyNCkyijc-r7H_kn_lA1nGCkQ,14247
transformers/models/dpt/convert_dpt_hybrid_to_pytorch.py,sha256=czo2aHnDSZZqv2qwpx48s1dRTg25v-R5giSg4seNebE,12994
transformers/models/dpt/convert_dpt_to_pytorch.py,sha256=-SpPQGZ5tD6g0g5fQpSbMmUDK9xc1OFIInk9yyjkahE,11894
transformers/models/dpt/feature_extraction_dpt.py,sha256=ZgBcSKNDX0_Fstv94sp1r9jpr9zvXCLPwvIek76Fkso,1165
transformers/models/dpt/image_processing_dpt.py,sha256=XEZBDitIdSeCf2lRMS4mKlbgoHXmUvYX3cAjvWbef0Q,22359
transformers/models/dpt/modeling_dpt.py,sha256=AbERwqU36DXLkBvz1dTNSS8ZbrBk-wcWxGTvFCCRDus,57483
transformers/models/efficientformer/__init__.py,sha256=hFVX-KUt3FRIjqb_MzHVif_h8r9FFezpRtRwFKLBKuY,3550
transformers/models/efficientformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/efficientformer/__pycache__/configuration_efficientformer.cpython-310.pyc,,
transformers/models/efficientformer/__pycache__/convert_efficientformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/efficientformer/__pycache__/image_processing_efficientformer.cpython-310.pyc,,
transformers/models/efficientformer/__pycache__/modeling_efficientformer.cpython-310.pyc,,
transformers/models/efficientformer/__pycache__/modeling_tf_efficientformer.cpython-310.pyc,,
transformers/models/efficientformer/configuration_efficientformer.py,sha256=SVqPRlF4ZdcExlycpBVwqsHY7lGEQqDZ9uW3Dk1v0aE,7919
transformers/models/efficientformer/convert_efficientformer_original_pytorch_checkpoint_to_pytorch.py,sha256=1ni0wyhRjTbF8U4BZ_FXU-_9Jzy43HMLKI3vGlyPjFc,9381
transformers/models/efficientformer/image_processing_efficientformer.py,sha256=2PULfqRvhj2U8CpK8naBePzOzObcg7lk4QagJ1xW-Dc,15110
transformers/models/efficientformer/modeling_efficientformer.py,sha256=k3Y_i8gqgYUGMqGkMpQzVfxsxsQid8yIKLOX20WWBKI,33878
transformers/models/efficientformer/modeling_tf_efficientformer.py,sha256=ngCxeEYH9a_rSDBTpK3MF4YLSPbWOi6YsWOCVQoczQI,40462
transformers/models/efficientnet/__init__.py,sha256=mS43eilPqqiySKV0CZ34jg1SPUJa2zc6qyCwwRoJQFM,2670
transformers/models/efficientnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/efficientnet/__pycache__/configuration_efficientnet.cpython-310.pyc,,
transformers/models/efficientnet/__pycache__/convert_efficientnet_to_pytorch.cpython-310.pyc,,
transformers/models/efficientnet/__pycache__/image_processing_efficientnet.cpython-310.pyc,,
transformers/models/efficientnet/__pycache__/modeling_efficientnet.cpython-310.pyc,,
transformers/models/efficientnet/configuration_efficientnet.py,sha256=WmiYtIBwTTa-GSSURQBOh4EVuA6zJ9sCIzVhU-_nQtE,7751
transformers/models/efficientnet/convert_efficientnet_to_pytorch.py,sha256=e2Na1xvNc7z9XvvI7v6v1V2uFWr88MSTN3JPKR5GstM,12756
transformers/models/efficientnet/image_processing_efficientnet.py,sha256=fDtYhiO9Swbmiy1yzck3uY6uI-E93B_P6txx6SjKXDM,18406
transformers/models/efficientnet/modeling_efficientnet.py,sha256=yRM8EcrDPB8H_PSJ38nIdTyQdJzqio6oI_4Q0LnQ1ig,24088
transformers/models/electra/__init__.py,sha256=UVRK4T71rPHmZYRbrQ_-5eu98Gfrkp6I9SA3KVVCcYQ,5257
transformers/models/electra/__pycache__/__init__.cpython-310.pyc,,
transformers/models/electra/__pycache__/configuration_electra.cpython-310.pyc,,
transformers/models/electra/__pycache__/convert_electra_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/electra/__pycache__/modeling_electra.cpython-310.pyc,,
transformers/models/electra/__pycache__/modeling_flax_electra.cpython-310.pyc,,
transformers/models/electra/__pycache__/modeling_tf_electra.cpython-310.pyc,,
transformers/models/electra/__pycache__/tokenization_electra.cpython-310.pyc,,
transformers/models/electra/__pycache__/tokenization_electra_fast.cpython-310.pyc,,
transformers/models/electra/configuration_electra.py,sha256=j0tL2YeX3wByGhIoMbvqrm_fUbV0xSaj12WXHUCG5dw,9928
transformers/models/electra/convert_electra_original_tf_checkpoint_to_pytorch.py,sha256=iwbjp9v26TfI9iIRdR4KWv-zsrxVNbfgkUwn9N1WHaM,2862
transformers/models/electra/modeling_electra.py,sha256=SPbFGZJG5G-7wx388j1Danqn17r8brvM9zt-_Ymcz4E,75975
transformers/models/electra/modeling_flax_electra.py,sha256=S5TkUbjF-9GNOxeiGfXTjc3tnINV18R8CLLFf30A9zU,62268
transformers/models/electra/modeling_tf_electra.py,sha256=u-iVeyNEfIxW2v-B__lEefgVemN7xpEX2l_z1cpUEXE,69406
transformers/models/electra/tokenization_electra.py,sha256=akGAo768alSgBPbqOS1fSiAj58RLwXrWCUqHBtWLhio,22774
transformers/models/electra/tokenization_electra_fast.py,sha256=VWsxtxMuI8yC8ARw6Bs0Hpiu8Hm3aHJGcNv484nMdIE,10507
transformers/models/encodec/__init__.py,sha256=LVz0exnSENNu1jnGsAoPoS7LfXgC-H7s3_lbwNEX_Dw,1910
transformers/models/encodec/__pycache__/__init__.cpython-310.pyc,,
transformers/models/encodec/__pycache__/configuration_encodec.cpython-310.pyc,,
transformers/models/encodec/__pycache__/convert_encodec_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/encodec/__pycache__/feature_extraction_encodec.cpython-310.pyc,,
transformers/models/encodec/__pycache__/modeling_encodec.cpython-310.pyc,,
transformers/models/encodec/configuration_encodec.py,sha256=0cvCzXuIsZxC3o6K_GnSjuZEC9qlraigY5zg1ql2aqY,8750
transformers/models/encodec/convert_encodec_checkpoint_to_pytorch.py,sha256=zF2ZSOCFsiMNvtIvRhjoucoF2G3m0nW-cHXimF_2uwQ,15253
transformers/models/encodec/feature_extraction_encodec.py,sha256=luYd1uGvvQC_mDYlUsnMtSBn_S0dhbazYJ9zYGuQ1Kc,9873
transformers/models/encodec/modeling_encodec.py,sha256=I01RO2cKJx52R8WwHz5kUhVizOeryClbwOkqAj31Xu0,33256
transformers/models/encoder_decoder/__init__.py,sha256=bR1yPbuqKHUYXaxI_QuDz6ccBSWpCr0THhPBM3lnttA,2451
transformers/models/encoder_decoder/__pycache__/__init__.cpython-310.pyc,,
transformers/models/encoder_decoder/__pycache__/configuration_encoder_decoder.cpython-310.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_encoder_decoder.cpython-310.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_flax_encoder_decoder.cpython-310.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_tf_encoder_decoder.cpython-310.pyc,,
transformers/models/encoder_decoder/configuration_encoder_decoder.py,sha256=jcdLCYFn7trHEC9dMfDTT6zttybDL9iCxBP99TML8Q8,4338
transformers/models/encoder_decoder/modeling_encoder_decoder.py,sha256=yPdKaeVs_mXqY_JYM0vyUNnkFU8wjqQGLmkF4AQS3yQ,35714
transformers/models/encoder_decoder/modeling_flax_encoder_decoder.py,sha256=4j907MfHfRpnd1lpHZc9n8TZ7bYjxgJnogwe9q-IpxM,43801
transformers/models/encoder_decoder/modeling_tf_encoder_decoder.py,sha256=BI_pESappYkOiOl5oPZ2aSITAdJhOtHYaHbOcCkqFQw,34034
transformers/models/ernie/__init__.py,sha256=s0oBhpPU0MdftoAKWUbo3VR2D9VPTvjPde4NBylw5qI,2331
transformers/models/ernie/__pycache__/__init__.cpython-310.pyc,,
transformers/models/ernie/__pycache__/configuration_ernie.cpython-310.pyc,,
transformers/models/ernie/__pycache__/modeling_ernie.cpython-310.pyc,,
transformers/models/ernie/configuration_ernie.py,sha256=H2_O0eTQcNBw94TqUhVFyhsKHk7VVWE9jv75sORVYbU,8885
transformers/models/ernie/modeling_ernie.py,sha256=aNyTaMUpc5NW5aLsSoz-cW0gfvwdt6bfNWfTqLVlvEg,84284
transformers/models/ernie_m/__init__.py,sha256=0neb_RuFu2HBnM3QZ5XRTBI9j8jzppR90ssXHH9LpGA,2637
transformers/models/ernie_m/__pycache__/__init__.cpython-310.pyc,,
transformers/models/ernie_m/__pycache__/configuration_ernie_m.cpython-310.pyc,,
transformers/models/ernie_m/__pycache__/modeling_ernie_m.cpython-310.pyc,,
transformers/models/ernie_m/__pycache__/tokenization_ernie_m.cpython-310.pyc,,
transformers/models/ernie_m/configuration_ernie_m.py,sha256=tyqxSGjISEyOWkB5hR7--BuSZedaY4B7HzlN3P6C-Ik,6191
transformers/models/ernie_m/modeling_ernie_m.py,sha256=GAi1w9HQ0jscGJMBIl9NdfmuJ4jpHdXcHRktvrihN9s,48015
transformers/models/ernie_m/tokenization_ernie_m.py,sha256=OzShzVQqwVzqw9ltF1IdkMnc1R0YQbaFEwkeZy8MY10,17115
transformers/models/esm/__init__.py,sha256=IfHOSRyzJHTD8eVSelVu_ijHcYnRp0Umm6hZGsoFYHQ,2978
transformers/models/esm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/esm/__pycache__/configuration_esm.cpython-310.pyc,,
transformers/models/esm/__pycache__/convert_esm.cpython-310.pyc,,
transformers/models/esm/__pycache__/modeling_esm.cpython-310.pyc,,
transformers/models/esm/__pycache__/modeling_esmfold.cpython-310.pyc,,
transformers/models/esm/__pycache__/modeling_tf_esm.cpython-310.pyc,,
transformers/models/esm/__pycache__/tokenization_esm.cpython-310.pyc,,
transformers/models/esm/configuration_esm.py,sha256=h1noIbq3Dp7UAOgDTK0RtqvsRBeMr8baP_Lr7hyYYc8,14559
transformers/models/esm/convert_esm.py,sha256=x0dfu2oexN80cndU3Zn81oVynsRuzfEtJZF20TK1y3k,18470
transformers/models/esm/modeling_esm.py,sha256=vbaewoGX_tS4QoKS_ZmCebc9Aysx8g0Q1vQFscrm0DQ,55758
transformers/models/esm/modeling_esmfold.py,sha256=LlNcof5AsDnYxZXuVwCtskoUs9_yGhMIwTIrgyK02wA,86910
transformers/models/esm/modeling_tf_esm.py,sha256=JNlD6ZFIg_76WWNsRafcYNO6mc7kaa3GL3pLtgaSmMk,60886
transformers/models/esm/openfold_utils/__init__.py,sha256=Xy2uqvFsLC8Ax-OOce5PgoBDiZgEJgJPqs__p5SBWUY,446
transformers/models/esm/openfold_utils/__pycache__/__init__.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/chunk_utils.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/data_transforms.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/feats.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/loss.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/protein.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/residue_constants.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/rigid_utils.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/tensor_utils.cpython-310.pyc,,
transformers/models/esm/openfold_utils/chunk_utils.py,sha256=eyd0NSdGIVBr9gLuI-3VI5cjJr46wYa9hlYBq1L1gCU,14392
transformers/models/esm/openfold_utils/data_transforms.py,sha256=F4wGANRhKLd6MLHrwg2IxpqCxCJEx8aFSxqAdsXsBMo,3764
transformers/models/esm/openfold_utils/feats.py,sha256=dgLcLJriW-eDIBdc0MyKPDT5w0POab9QLuN56qE8wsk,8376
transformers/models/esm/openfold_utils/loss.py,sha256=wY2ONqbuRvWMomjkpfPwfoa7dqCO2vFkM-kmNfhjivo,3705
transformers/models/esm/openfold_utils/protein.py,sha256=x9NK6bryLs9vNi3j8OfOlw0Jb1cFrwMhCi6JdxkDdQw,11490
transformers/models/esm/openfold_utils/residue_constants.py,sha256=KDcdOt5wkJ7cO7p-LtmS8sLIzfQ2ej7p40Re8EsTkv0,37993
transformers/models/esm/openfold_utils/rigid_utils.py,sha256=EF79POBO-abRsdXrfdKLaqJUVIPp4EOMFVt5oOjx504,41122
transformers/models/esm/openfold_utils/tensor_utils.py,sha256=A07D5psNs5lGgWJp_kzJgrY8cmWmaL3odDgKXN1NVAE,4798
transformers/models/esm/tokenization_esm.py,sha256=F25iPXXvV8Kw1ESAEvOjcpn-PAvKFc_VdJMoZx0WYS4,6197
transformers/models/falcon/__init__.py,sha256=Sf4eyG7aJ4pQoqLJXStTSTxP7iEHks73GWe9QjAnU3w,2067
transformers/models/falcon/__pycache__/__init__.cpython-310.pyc,,
transformers/models/falcon/__pycache__/configuration_falcon.cpython-310.pyc,,
transformers/models/falcon/__pycache__/convert_custom_code_checkpoint.cpython-310.pyc,,
transformers/models/falcon/__pycache__/modeling_falcon.cpython-310.pyc,,
transformers/models/falcon/configuration_falcon.py,sha256=Vn_KKSitHhAbfuDDnrfuHHiLbmpyU5D0cOn8AeZ87eQ,9229
transformers/models/falcon/convert_custom_code_checkpoint.py,sha256=XPJ1owRjRno_Y1AD5UeoPE4oo6a-SeQR9w9u-EIUktE,3061
transformers/models/falcon/modeling_falcon.py,sha256=3YFSF0UPLySIHXYeLM-HiScK8L_ZQPP54rwWubCe9tk,75885
transformers/models/flaubert/__init__.py,sha256=neN63qn5CVIfPSr50g0WhbrcKDT7w0qIljyqSCxbqLI,3488
transformers/models/flaubert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/flaubert/__pycache__/configuration_flaubert.cpython-310.pyc,,
transformers/models/flaubert/__pycache__/modeling_flaubert.cpython-310.pyc,,
transformers/models/flaubert/__pycache__/modeling_tf_flaubert.cpython-310.pyc,,
transformers/models/flaubert/__pycache__/tokenization_flaubert.cpython-310.pyc,,
transformers/models/flaubert/configuration_flaubert.py,sha256=9elLZSProrYsw3CwMsyM24Q3bbKCwmWRGg-RMcay9T0,11706
transformers/models/flaubert/modeling_flaubert.py,sha256=RemOdoVh0EZM1WTCg4FFA2Zj42eipmAdaamN7AlGlA8,57659
transformers/models/flaubert/modeling_tf_flaubert.py,sha256=4KBlRIJQQB0fFGwYS55ZoIYzTE4_9HUV-zVVmnmfkh8,52353
transformers/models/flaubert/tokenization_flaubert.py,sha256=McA9rqAL7u2RZhigWbVRd5ls64HMlMGeY7TchEvW55Q,24028
transformers/models/flava/__init__.py,sha256=TtPrEOob3V4Lk_NK3rgacXw0jJ2ABWKPnLP8x4uSs4I,3030
transformers/models/flava/__pycache__/__init__.cpython-310.pyc,,
transformers/models/flava/__pycache__/configuration_flava.cpython-310.pyc,,
transformers/models/flava/__pycache__/convert_dalle_to_flava_codebook.cpython-310.pyc,,
transformers/models/flava/__pycache__/convert_flava_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/flava/__pycache__/feature_extraction_flava.cpython-310.pyc,,
transformers/models/flava/__pycache__/image_processing_flava.cpython-310.pyc,,
transformers/models/flava/__pycache__/modeling_flava.cpython-310.pyc,,
transformers/models/flava/__pycache__/processing_flava.cpython-310.pyc,,
transformers/models/flava/configuration_flava.py,sha256=92OYDv5JSV0GTu70G8fD7Yo4kqKPCfOA_xo0wFXRe0w,37243
transformers/models/flava/convert_dalle_to_flava_codebook.py,sha256=iEJM9W_cKk3HK0gKS6i2ygEMeyymWCMl18LDaQXRAhY,3428
transformers/models/flava/convert_flava_original_pytorch_to_hf.py,sha256=LilQpbe6qeN2P_uXljae6zEPx_KoepoRv4uvCEAo0QA,4372
transformers/models/flava/feature_extraction_flava.py,sha256=mA1uAn29yv9PV7gYXauz0VTAJDgcpl9DPHvH99Ed__s,1201
transformers/models/flava/image_processing_flava.py,sha256=qrnsph73fHt3Nxa3EuoeO5iVQU96WvmMC7387PjlylU,37368
transformers/models/flava/modeling_flava.py,sha256=PkZkwG1tJuSirjEI_HFwp9fYbum7ubm2BVcQV7QRJb0,96429
transformers/models/flava/processing_flava.py,sha256=fj9uFlMerVGFnB9hV1XJ61c3q82qstjPwmWUdMiL46U,6832
transformers/models/fnet/__init__.py,sha256=spzYrdM_-MVYRr6Axeh_adtgX1pCDAsUJEpR-cPdxgE,3179
transformers/models/fnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/fnet/__pycache__/configuration_fnet.cpython-310.pyc,,
transformers/models/fnet/__pycache__/convert_fnet_original_flax_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/fnet/__pycache__/modeling_fnet.cpython-310.pyc,,
transformers/models/fnet/__pycache__/tokenization_fnet.cpython-310.pyc,,
transformers/models/fnet/__pycache__/tokenization_fnet_fast.cpython-310.pyc,,
transformers/models/fnet/configuration_fnet.py,sha256=kokmFmSZFON4StxEQb-TZaQE8j_HcvZD7d2CEJPMDGE,5841
transformers/models/fnet/convert_fnet_original_flax_checkpoint_to_pytorch.py,sha256=bxrdtJbyINwJtiIpagL3Ttkq0D5ujBK1Wi72fIR2vss,6912
transformers/models/fnet/modeling_fnet.py,sha256=89ongdrN1WaUIjIpY4ObBTq5ykYBAX8rQymuYzrNDww,49109
transformers/models/fnet/tokenization_fnet.py,sha256=lkISK_tO2OHuxc4O12JVJPnWoDBmi_Ftf8E3pGwWjWc,14931
transformers/models/fnet/tokenization_fnet_fast.py,sha256=bqRInDENeJ7CEgKyUJ32_kD4t3aXojCsJxMg3rMly9I,8783
transformers/models/focalnet/__init__.py,sha256=RPvCimVzndLWR8r1MfUbrAiQTJEvJ6VGTM1OFmAS9-A,1989
transformers/models/focalnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/focalnet/__pycache__/configuration_focalnet.cpython-310.pyc,,
transformers/models/focalnet/__pycache__/convert_focalnet_to_hf_format.cpython-310.pyc,,
transformers/models/focalnet/__pycache__/modeling_focalnet.cpython-310.pyc,,
transformers/models/focalnet/configuration_focalnet.py,sha256=wrZNWAnzQhhI3V6JblVhoXhpWJbicDR5HPpxN3rUGm0,8017
transformers/models/focalnet/convert_focalnet_to_hf_format.py,sha256=xBoop7K4unfPawCbmlv7BTQHpbJkaUWasrwsw8dW_KI,9450
transformers/models/focalnet/modeling_focalnet.py,sha256=hkG3bN9LH6JjX6SqXaC_8bETpK5xMKzr-4ZPzAk2kWA,43243
transformers/models/fsmt/__init__.py,sha256=e0xh51cBRMFkSYEcmZzyINHoXBKwgonWv3zEPqZuMYE,1675
transformers/models/fsmt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/fsmt/__pycache__/configuration_fsmt.cpython-310.pyc,,
transformers/models/fsmt/__pycache__/convert_fsmt_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/fsmt/__pycache__/modeling_fsmt.cpython-310.pyc,,
transformers/models/fsmt/__pycache__/tokenization_fsmt.cpython-310.pyc,,
transformers/models/fsmt/configuration_fsmt.py,sha256=YfKdrPTsGnom_7jfi9EnUp_HUWAEGH8aRBPJT6MHqN4,10106
transformers/models/fsmt/convert_fsmt_original_pytorch_checkpoint_to_pytorch.py,sha256=BWtn90XQAuWGp8k9zns5St9On_os395ESNgkaXy6y2g,11264
transformers/models/fsmt/modeling_fsmt.py,sha256=4TalpL6Ddew58ApG4xfoOQppdQkuQuH8Ns6brQo6gMA,58386
transformers/models/fsmt/tokenization_fsmt.py,sha256=cboPC7nRumxw5XXKRFOLIw8_TSVpUidqodZwvfkzjjQ,20174
transformers/models/funnel/__init__.py,sha256=QQgGGD4BfFL3j1qtC1oNuuagXUPYWw0KJ4XVKTzMvW0,4126
transformers/models/funnel/__pycache__/__init__.cpython-310.pyc,,
transformers/models/funnel/__pycache__/configuration_funnel.cpython-310.pyc,,
transformers/models/funnel/__pycache__/convert_funnel_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/funnel/__pycache__/modeling_funnel.cpython-310.pyc,,
transformers/models/funnel/__pycache__/modeling_tf_funnel.cpython-310.pyc,,
transformers/models/funnel/__pycache__/tokenization_funnel.cpython-310.pyc,,
transformers/models/funnel/__pycache__/tokenization_funnel_fast.cpython-310.pyc,,
transformers/models/funnel/configuration_funnel.py,sha256=ZFnFPlWkHb1B3d1PZgJGcdmKYtNiPbyCsU0v-zsCKyw,8894
transformers/models/funnel/convert_funnel_original_tf_checkpoint_to_pytorch.py,sha256=fdaL7-j0ZWjCKvvpS_gFYHBthQ8TFbGmkOmfd53enaI,2335
transformers/models/funnel/modeling_funnel.py,sha256=TjQbs9FFktETHdDRTf9A1ZesIY_crGL6abEn3bLmmGU,69995
transformers/models/funnel/modeling_tf_funnel.py,sha256=vj1A2CMT4nF6Pliwe_omdJRFPfQQOQ4WLjIdLw18t-o,73093
transformers/models/funnel/tokenization_funnel.py,sha256=5cHVmhKIPuKJxhVP-iOOWaOemGklG-or8LpPzw7Hmpg,24119
transformers/models/funnel/tokenization_funnel_fast.py,sha256=FjPOy1yZMOSe7J8TIrX4yRzqNeQPsypYiGviGJW6aH0,11806
transformers/models/fuyu/__init__.py,sha256=SLRcFqITZh127We258kiNPRKoegottQTbpuCZ72dTBU,2184
transformers/models/fuyu/__pycache__/__init__.cpython-310.pyc,,
transformers/models/fuyu/__pycache__/configuration_fuyu.cpython-310.pyc,,
transformers/models/fuyu/__pycache__/convert_fuyu_model_weights_to_hf.cpython-310.pyc,,
transformers/models/fuyu/__pycache__/image_processing_fuyu.cpython-310.pyc,,
transformers/models/fuyu/__pycache__/modeling_fuyu.cpython-310.pyc,,
transformers/models/fuyu/__pycache__/processing_fuyu.cpython-310.pyc,,
transformers/models/fuyu/configuration_fuyu.py,sha256=xhAKeF8AxXaTlAGOY_fGk9ujvWZ61z4PBuINMPEqqkU,10208
transformers/models/fuyu/convert_fuyu_model_weights_to_hf.py,sha256=c8A4qiUY47MfPeEG518qofxFdzut0me3EtFNizEHv6Q,4847
transformers/models/fuyu/image_processing_fuyu.py,sha256=ygu327PLP5elq-amUBbMqV3SSeXmg2iCDDxEgB7D76w,33250
transformers/models/fuyu/modeling_fuyu.py,sha256=KBl2Z4qa6NVjc-uzHBOkQDTjiBoFCpymEyeKTMmCFyY,17650
transformers/models/fuyu/processing_fuyu.py,sha256=DUTZ_Bxu5mHxnsnJYYHleKF79cx_XXVcCvZP6VguhZQ,32002
transformers/models/git/__init__.py,sha256=KG0HrIdVgj64GVVUk32IdidJRaC5BcjQZt62oVRL5Eo,1888
transformers/models/git/__pycache__/__init__.cpython-310.pyc,,
transformers/models/git/__pycache__/configuration_git.cpython-310.pyc,,
transformers/models/git/__pycache__/convert_git_to_pytorch.cpython-310.pyc,,
transformers/models/git/__pycache__/modeling_git.cpython-310.pyc,,
transformers/models/git/__pycache__/processing_git.cpython-310.pyc,,
transformers/models/git/configuration_git.py,sha256=3qUPJQ2eelvgWmbiQRNXoJijt3_ldAniAHJU7_JjRnM,11352
transformers/models/git/convert_git_to_pytorch.py,sha256=ymhOSj4zMCocTnsWlryc9KJ-yX92pd0VPlR6z0LqoL0,22364
transformers/models/git/modeling_git.py,sha256=QRLSifpagflc4n41WykEM4WsvOSp5Ejmi787fPmluH0,69145
transformers/models/git/processing_git.py,sha256=oyuFkaTwqpdw_lpKXn4hAz5qfIfyI14Ug441OaK-Smk,5487
transformers/models/glpn/__init__.py,sha256=-5zqCuk1phx-Bjw3Mq-NJmPvusXfEYcNGIrFO27vr3s,2384
transformers/models/glpn/__pycache__/__init__.cpython-310.pyc,,
transformers/models/glpn/__pycache__/configuration_glpn.cpython-310.pyc,,
transformers/models/glpn/__pycache__/convert_glpn_to_pytorch.cpython-310.pyc,,
transformers/models/glpn/__pycache__/feature_extraction_glpn.cpython-310.pyc,,
transformers/models/glpn/__pycache__/image_processing_glpn.cpython-310.pyc,,
transformers/models/glpn/__pycache__/modeling_glpn.cpython-310.pyc,,
transformers/models/glpn/configuration_glpn.py,sha256=yP7RSxhZITNay7YOlT0csI5bTEudOC9YDMc9Gs6r1Z8,6185
transformers/models/glpn/convert_glpn_to_pytorch.py,sha256=dT5q2vCISTu1DjoTkLSyHmlcR75n_CGhXxxknL5KjJQ,8558
transformers/models/glpn/feature_extraction_glpn.py,sha256=S263LFeHVRym_jKt8KkTOjjtA1_BqARnUgbSFExgPN4,1172
transformers/models/glpn/image_processing_glpn.py,sha256=BP-CuyOTw1SVGZ06hE3W0GPh7UT2W_iUiIUL3bMDz-A,10193
transformers/models/glpn/modeling_glpn.py,sha256=3lc1xIpuLhOogdWFB_6b-UFuyxgAkpOTB9JpVdeRK34,31547
transformers/models/gpt2/__init__.py,sha256=d_QyBAIVXohGlkOMWC9r03kE9uS2IHwXwPCsxnMGGkg,4674
transformers/models/gpt2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/configuration_gpt2.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/convert_gpt2_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/modeling_flax_gpt2.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/modeling_gpt2.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/modeling_tf_gpt2.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2_fast.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2_tf.cpython-310.pyc,,
transformers/models/gpt2/configuration_gpt2.py,sha256=el1UktNIr-DwfaDuHN3tdieNK-GC8qx_vILEycgBfU0,12375
transformers/models/gpt2/convert_gpt2_original_tf_checkpoint_to_pytorch.py,sha256=nRAxbikMz9v88rDqfrX8OwPvBKe7fiYC2fg-6BB8Mzk,2532
transformers/models/gpt2/modeling_flax_gpt2.py,sha256=BSikXA8u1hZKeu-rGI09zk0_gsernOzsoPMCpL1d5NU,31997
transformers/models/gpt2/modeling_gpt2.py,sha256=6SKBXIRDSv6dtF1o_XTIfp14inUyb0bU77osFNQEZyc,76656
transformers/models/gpt2/modeling_tf_gpt2.py,sha256=yy3ccpo-Ek7J3NA2zrj4DnR3WCbkokRGtml9xHUdqC8,52159
transformers/models/gpt2/tokenization_gpt2.py,sha256=5gjxjPDAJguuDhr-d8RqXxQu_c4u-AdOWIkG3Gi9efI,15004
transformers/models/gpt2/tokenization_gpt2_fast.py,sha256=lDwE85auVzvNwWzINw1Kdz1L6Tq4P_-f_uBGzqHO3c0,8140
transformers/models/gpt2/tokenization_gpt2_tf.py,sha256=-LQ5vx4bI-Jb8FmJd7TJIdaAxPSpNTV1Hn63RinC7BA,3763
transformers/models/gpt_bigcode/__init__.py,sha256=waW0WeT6jgb8gWpaGmMZBJCYoqKzCbaQbyjHZkuEARE,2037
transformers/models/gpt_bigcode/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt_bigcode/__pycache__/configuration_gpt_bigcode.cpython-310.pyc,,
transformers/models/gpt_bigcode/__pycache__/modeling_gpt_bigcode.cpython-310.pyc,,
transformers/models/gpt_bigcode/configuration_gpt_bigcode.py,sha256=wx2iMdoaWmm8_aAIqyO0fEFNw3m3riQLBXc9sU3R2Sc,6448
transformers/models/gpt_bigcode/modeling_gpt_bigcode.py,sha256=GTCPg2M37r5RHRGGrLFZ5Av_RK8ETRDvgigX8x-Qt88,70090
transformers/models/gpt_neo/__init__.py,sha256=tCBf4wXQijfaRh959WfU7_npuc1na00rwCZCgcxuTOo,2718
transformers/models/gpt_neo/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt_neo/__pycache__/configuration_gpt_neo.cpython-310.pyc,,
transformers/models/gpt_neo/__pycache__/convert_gpt_neo_mesh_tf_to_pytorch.cpython-310.pyc,,
transformers/models/gpt_neo/__pycache__/modeling_flax_gpt_neo.cpython-310.pyc,,
transformers/models/gpt_neo/__pycache__/modeling_gpt_neo.cpython-310.pyc,,
transformers/models/gpt_neo/configuration_gpt_neo.py,sha256=afArf1jVApQMGYnC3MqHc3we14099hbGpieeIP6eH7I,12060
transformers/models/gpt_neo/convert_gpt_neo_mesh_tf_to_pytorch.py,sha256=SSlCsIZmkN010Cu64F4lxwHcQRsqEGbb7a6PqCSWJY0,2589
transformers/models/gpt_neo/modeling_flax_gpt_neo.py,sha256=xgwE5UixFan9wDb9ScOd8DcEH-o1Iu-AX1bNkMWQFEA,28074
transformers/models/gpt_neo/modeling_gpt_neo.py,sha256=VfVOfWxjh3etxDTImFVoVIP_-vXMvbWvjuqxSQgFwgc,58028
transformers/models/gpt_neox/__init__.py,sha256=NETOJyNfZJ1SXJ4jc1heeVs2TMqXjlbminmJQKSnLnA,2595
transformers/models/gpt_neox/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt_neox/__pycache__/configuration_gpt_neox.cpython-310.pyc,,
transformers/models/gpt_neox/__pycache__/modeling_gpt_neox.cpython-310.pyc,,
transformers/models/gpt_neox/__pycache__/tokenization_gpt_neox_fast.cpython-310.pyc,,
transformers/models/gpt_neox/configuration_gpt_neox.py,sha256=BHBIAYrA_Hh7COlDMuXiilFc24W0pGDHnBmbfTSyX9k,8821
transformers/models/gpt_neox/modeling_gpt_neox.py,sha256=2jnyl_fVpka_RFyI1SKGN84TnrZoRytzv_48CUM20PU,64567
transformers/models/gpt_neox/tokenization_gpt_neox_fast.py,sha256=bmE3LOc0kaQVtbI0I2kHnD6690pe4ef7qiavepALHhs,6031
transformers/models/gpt_neox_japanese/__init__.py,sha256=7S5Q5Y8aQPbcoaPjIVo7s9ebHh0GLv3cA1TeAhzvFFA,2154
transformers/models/gpt_neox_japanese/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/configuration_gpt_neox_japanese.cpython-310.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/modeling_gpt_neox_japanese.cpython-310.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/tokenization_gpt_neox_japanese.cpython-310.pyc,,
transformers/models/gpt_neox_japanese/configuration_gpt_neox_japanese.py,sha256=MydoF25igdWh-llKyXwAJYr3-pYCkjuJj-vY_4eKtfc,5730
transformers/models/gpt_neox_japanese/modeling_gpt_neox_japanese.py,sha256=KQd_g4kNVp9aTjQ1jq8PmrFX4e7_yBj3CrbGTmerRRQ,32438
transformers/models/gpt_neox_japanese/tokenization_gpt_neox_japanese.py,sha256=FQmPqik3mKrBdIp87XXppUUWQuhxBGprjkNc9EACPpI,17622
transformers/models/gpt_sw3/__init__.py,sha256=qJj7vF8ES37BwsKbJE1zV2rPUdmM3vx8mckIFuWrJSU,1361
transformers/models/gpt_sw3/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt_sw3/__pycache__/convert_megatron_to_pytorch.cpython-310.pyc,,
transformers/models/gpt_sw3/__pycache__/tokenization_gpt_sw3.cpython-310.pyc,,
transformers/models/gpt_sw3/convert_megatron_to_pytorch.py,sha256=11EGXgi73zwRchm4aMlHE7tCom4_oGLQSWF1YMpBBQA,8156
transformers/models/gpt_sw3/tokenization_gpt_sw3.py,sha256=Ngs6sAfSaGTJmOX3hPg9mfIwslGrB-JZtANi1djClgA,14483
transformers/models/gptj/__init__.py,sha256=wBErGYabUQpzDULOVQSE9vEvefKWJvJFoU9p0t54qDU,3280
transformers/models/gptj/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gptj/__pycache__/configuration_gptj.cpython-310.pyc,,
transformers/models/gptj/__pycache__/modeling_flax_gptj.cpython-310.pyc,,
transformers/models/gptj/__pycache__/modeling_gptj.cpython-310.pyc,,
transformers/models/gptj/__pycache__/modeling_tf_gptj.cpython-310.pyc,,
transformers/models/gptj/configuration_gptj.py,sha256=etH7ja7cRkDCYgLyTeR-OVlt_7ulU1w6OF3bfNvfnZ0,8997
transformers/models/gptj/modeling_flax_gptj.py,sha256=VaYTrxQosqkIqHcbKcDFinT_z3aofwdJLasWAqxjRlM,28525
transformers/models/gptj/modeling_gptj.py,sha256=yW1McEXe6H1Q-QVBQKh3iTaG25o5z-jUjzaAt_xrHA0,50212
transformers/models/gptj/modeling_tf_gptj.py,sha256=WFaM9Nk67xR3nAAT9veBDxtmovFXSylPIozEKTQi9ek,44055
transformers/models/gptsan_japanese/__init__.py,sha256=gkfCyeWUjR_u2kxoe0nD-gLdcFoS4SwjhQBNufTY86w,2294
transformers/models/gptsan_japanese/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gptsan_japanese/__pycache__/configuration_gptsan_japanese.cpython-310.pyc,,
transformers/models/gptsan_japanese/__pycache__/convert_gptsan_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/gptsan_japanese/__pycache__/modeling_gptsan_japanese.cpython-310.pyc,,
transformers/models/gptsan_japanese/__pycache__/tokenization_gptsan_japanese.cpython-310.pyc,,
transformers/models/gptsan_japanese/configuration_gptsan_japanese.py,sha256=3WE1dYGKgyADW5IQH9ga0geXzpzImxpKIMyXB5PoImY,7330
transformers/models/gptsan_japanese/convert_gptsan_tf_checkpoint_to_pytorch.py,sha256=syF4TCbLQByZhm5VqIFgXfzQ4zImmCua8UNjCYJP5t8,9793
transformers/models/gptsan_japanese/modeling_gptsan_japanese.py,sha256=vJbDxqUh3-pzlkbqaCTpJH_y1PwlU3BbYsITekrAmb8,66663
transformers/models/gptsan_japanese/tokenization_gptsan_japanese.py,sha256=UvpwBISnjcxoGdPghSgVCamVBsLA3Mfn8o5EN7aHhIY,24762
transformers/models/graphormer/__init__.py,sha256=SCL3NOPe62lQVk-qWrJD1enP6JNBWyPreg5EGaifjbE,1873
transformers/models/graphormer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/graphormer/__pycache__/collating_graphormer.cpython-310.pyc,,
transformers/models/graphormer/__pycache__/configuration_graphormer.cpython-310.pyc,,
transformers/models/graphormer/__pycache__/modeling_graphormer.cpython-310.pyc,,
transformers/models/graphormer/algos_graphormer.pyx,sha256=b_Qlm1hKCHnAqx6oOLGC9LkivAV0K_AZRGgXT9MmBas,3635
transformers/models/graphormer/collating_graphormer.py,sha256=1r_YqrFzC6uWCaPCsGMqNkvHNKs6SCV1bSw2qLyAYJA,6086
transformers/models/graphormer/configuration_graphormer.py,sha256=YmBNNGT13ZGp80sAk54LTNKvyAWaDcEYuHcuW9cP014,10651
transformers/models/graphormer/modeling_graphormer.py,sha256=w0AUgubPtFjWVkPo4z3n7LEYE5E5FLffWl7fFXHawGk,37223
transformers/models/groupvit/__init__.py,sha256=rO2THuhEVPYRh__0tgdPS9egtqSugEkoXU4lDMAg3q0,2875
transformers/models/groupvit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/groupvit/__pycache__/configuration_groupvit.cpython-310.pyc,,
transformers/models/groupvit/__pycache__/convert_groupvit_nvlab_to_hf.cpython-310.pyc,,
transformers/models/groupvit/__pycache__/modeling_groupvit.cpython-310.pyc,,
transformers/models/groupvit/__pycache__/modeling_tf_groupvit.cpython-310.pyc,,
transformers/models/groupvit/configuration_groupvit.py,sha256=eZl8CiAIwPGuQWozosb_1b0SvQxhNJee53sd6zRd_A0,20857
transformers/models/groupvit/convert_groupvit_nvlab_to_hf.py,sha256=9gQxkcjVNCP5lvV54SbbSsOjkKCHORcoiwq2gcczYCM,9775
transformers/models/groupvit/modeling_groupvit.py,sha256=dzhhud9iz5z2F9oqYjkZ0ORgvYfwRZic7ze3TxKrUlg,67941
transformers/models/groupvit/modeling_tf_groupvit.py,sha256=HFYD32Hm0fFe4UZN-B_qUWaMMmP6WRacJOHDC3FTKH8,78818
transformers/models/herbert/__init__.py,sha256=Sp9gQIqlUhZHausuaL2MFYDqJW4vvsVGLbVryR-kNl0,1472
transformers/models/herbert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/herbert/__pycache__/tokenization_herbert.cpython-310.pyc,,
transformers/models/herbert/__pycache__/tokenization_herbert_fast.cpython-310.pyc,,
transformers/models/herbert/tokenization_herbert.py,sha256=CIRo3RXl494ufqpISLTEqmEI6F92R5osJLRh1J5S22w,25665
transformers/models/herbert/tokenization_herbert_fast.py,sha256=RmXQlJtd_l75QrDCYTli6Gny4-brixe9LVJfjNFSL4s,6549
transformers/models/hubert/__init__.py,sha256=rfeBnkDY2iMz8xs_cZY4wSMSxoXQeVQov-C42xhA0eE,2536
transformers/models/hubert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/hubert/__pycache__/configuration_hubert.cpython-310.pyc,,
transformers/models/hubert/__pycache__/convert_distilhubert_original_s3prl_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/hubert/__pycache__/convert_hubert_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/hubert/__pycache__/convert_hubert_original_s3prl_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/hubert/__pycache__/modeling_hubert.cpython-310.pyc,,
transformers/models/hubert/__pycache__/modeling_tf_hubert.cpython-310.pyc,,
transformers/models/hubert/configuration_hubert.py,sha256=R8zWh88WsY_-iLv6Scqcaw8Oq9HabWf9hvop5KvEgOU,14908
transformers/models/hubert/convert_distilhubert_original_s3prl_checkpoint_to_pytorch.py,sha256=ENEJNVBI7j5N6ajvUnNEAfSIM6VfEmpI8dF86R4EDog,8942
transformers/models/hubert/convert_hubert_original_pytorch_checkpoint_to_pytorch.py,sha256=tVrpW4Mqkymh6pcLdYdTtkl0ykhSkHNvfTefbBIpR7w,10380
transformers/models/hubert/convert_hubert_original_s3prl_checkpoint_to_pytorch.py,sha256=BtUOQ6Jf7kppeKreWA76AvQNdy_a63t2iuq0yHvEs4E,2895
transformers/models/hubert/modeling_hubert.py,sha256=1NpiOUPbatpppXObppE7aqlRp7SXJ_Vo5X3JtFY6qqY,60186
transformers/models/hubert/modeling_tf_hubert.py,sha256=FPZyO-7KfjHcExP8vIBJ1F5ML-yw6mTxlHwhtM2wH-Y,63210
transformers/models/ibert/__init__.py,sha256=uw-Mi7HIih0Or_1DeCK7Ooc20kBdmqokZ6GEDwOD9LU,2086
transformers/models/ibert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/ibert/__pycache__/configuration_ibert.cpython-310.pyc,,
transformers/models/ibert/__pycache__/modeling_ibert.cpython-310.pyc,,
transformers/models/ibert/__pycache__/quant_modules.cpython-310.pyc,,
transformers/models/ibert/configuration_ibert.py,sha256=Y-SWRF56CuLQpoDRgayeOI6mVIFJtBQE80wGY75cT0g,7462
transformers/models/ibert/modeling_ibert.py,sha256=Q5_Cx9uiis6diaC6BA3vk3jVgyLZFKdNPc8CBW_DUvg,56785
transformers/models/ibert/quant_modules.py,sha256=ItU76CIx0XcZCPOR21dz99J9k5rK2fzffQz0jJCuNmM,30072
transformers/models/idefics/__init__.py,sha256=XnXH7RPak98A3W6H9eW1o8eiVgxgAMKoi6xAkKBOL8o,2360
transformers/models/idefics/__pycache__/__init__.cpython-310.pyc,,
transformers/models/idefics/__pycache__/configuration_idefics.cpython-310.pyc,,
transformers/models/idefics/__pycache__/image_processing_idefics.cpython-310.pyc,,
transformers/models/idefics/__pycache__/modeling_idefics.cpython-310.pyc,,
transformers/models/idefics/__pycache__/perceiver.cpython-310.pyc,,
transformers/models/idefics/__pycache__/processing_idefics.cpython-310.pyc,,
transformers/models/idefics/__pycache__/vision.cpython-310.pyc,,
transformers/models/idefics/configuration_idefics.py,sha256=JyKHuYXhQzBwFiVsHm9z3_W158JfR0z2NqWA6ICdrQ4,15625
transformers/models/idefics/image_processing_idefics.py,sha256=xcHYUAzAgIaXk92aU0YY83scvQdpQekN37UJll9utdg,7801
transformers/models/idefics/modeling_idefics.py,sha256=cMosvJxTM_QWyvgDJPjbj-L4UYsiN_eDIkqe2JEdQ0U,73055
transformers/models/idefics/perceiver.py,sha256=RtKLRu3IIjUHCYcLAgZyirDbxK-ZlKKts_to0fv1x6o,9432
transformers/models/idefics/processing_idefics.py,sha256=5bRpxhLCAZnZXvhomo6oVbIdyiuaOhODOVXO-iZeRV4,17904
transformers/models/idefics/vision.py,sha256=NhyX0C8GUjs6SGJUCkLGoAu_SL5tzOSZShjUpW1U0bk,22492
transformers/models/imagegpt/__init__.py,sha256=aPsv_YVn82O_HHaFDIsYqe8bR8hs3sk1RUlcCtaUWcc,2658
transformers/models/imagegpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/imagegpt/__pycache__/configuration_imagegpt.cpython-310.pyc,,
transformers/models/imagegpt/__pycache__/convert_imagegpt_original_tf2_to_pytorch.cpython-310.pyc,,
transformers/models/imagegpt/__pycache__/feature_extraction_imagegpt.cpython-310.pyc,,
transformers/models/imagegpt/__pycache__/image_processing_imagegpt.cpython-310.pyc,,
transformers/models/imagegpt/__pycache__/modeling_imagegpt.cpython-310.pyc,,
transformers/models/imagegpt/configuration_imagegpt.py,sha256=oVgzGNNlNNH_pYSnKA4Qd5iVX69p2csJeCcxvrVH0bM,8866
transformers/models/imagegpt/convert_imagegpt_original_tf2_to_pytorch.py,sha256=yneGtcrTR4Ui38NG8ogK7N_4dAyTiVBkmc8JQERb2bs,2691
transformers/models/imagegpt/feature_extraction_imagegpt.py,sha256=iCpQ4tU3Vml44KgO43kYJvv-RcZVxe8tc794gxUktuU,1200
transformers/models/imagegpt/image_processing_imagegpt.py,sha256=0lWKkqXJpcjWcQO1jOVpvOsjrSUU4GdG_mXWU0Y77UE,14082
transformers/models/imagegpt/modeling_imagegpt.py,sha256=9nPvSMdhi9xaH4uLsfBYzmDCRaV9IoidV9Frxh-9H58,53794
transformers/models/informer/__init__.py,sha256=VylZIY0U5EuIfEuvphPh-gCCgBtwRAByccv11nsTA5Q,1857
transformers/models/informer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/informer/__pycache__/configuration_informer.cpython-310.pyc,,
transformers/models/informer/__pycache__/modeling_informer.cpython-310.pyc,,
transformers/models/informer/configuration_informer.py,sha256=zk8COGcN9U6Z27l8v-rnaydxhc1iCZIHDYegvkTmlcM,12685
transformers/models/informer/modeling_informer.py,sha256=LSGgX5OJl7MPXdA2oaEIhPnV1y2-bt31JCpQqBCtVXI,101601
transformers/models/instructblip/__init__.py,sha256=GpbqWHExuUvlsDeouDhVv-f_etjU9Dwm006DwFiAMEg,2279
transformers/models/instructblip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/instructblip/__pycache__/configuration_instructblip.cpython-310.pyc,,
transformers/models/instructblip/__pycache__/convert_instructblip_original_to_pytorch.cpython-310.pyc,,
transformers/models/instructblip/__pycache__/modeling_instructblip.cpython-310.pyc,,
transformers/models/instructblip/__pycache__/processing_instructblip.cpython-310.pyc,,
transformers/models/instructblip/configuration_instructblip.py,sha256=677T_76UozXK3mP26tGNE2YFqJseuXdnFZez26WJIU8,17239
transformers/models/instructblip/convert_instructblip_original_to_pytorch.py,sha256=KAzRkE6iitIMZvmHqTt9ZnOFg8j6tKG1Q5LdTcgugzQ,13387
transformers/models/instructblip/modeling_instructblip.py,sha256=MtO2ni9e_6o6zuMXes6SJTlLLuNC-mbZpGtc4wCVaUs,70301
transformers/models/instructblip/processing_instructblip.py,sha256=zJT2QvAzlJAFlADmSSr36VWNB6xLpazrqFmp3og5AE8,7856
transformers/models/jukebox/__init__.py,sha256=kZx3ZvfTUb90bEGC0UVrqOfoJvIWSBrUOR701WATaHI,2084
transformers/models/jukebox/__pycache__/__init__.cpython-310.pyc,,
transformers/models/jukebox/__pycache__/configuration_jukebox.cpython-310.pyc,,
transformers/models/jukebox/__pycache__/convert_jukebox.cpython-310.pyc,,
transformers/models/jukebox/__pycache__/modeling_jukebox.cpython-310.pyc,,
transformers/models/jukebox/__pycache__/tokenization_jukebox.cpython-310.pyc,,
transformers/models/jukebox/configuration_jukebox.py,sha256=L7XY1pQpu0mPRuFlzSO9lXCH3Yv8lVwby8Hs09HgzbU,27002
transformers/models/jukebox/convert_jukebox.py,sha256=RBgOPbwIMv_42mUFJYxRv4IAGZn4cAzjTqjrMI7HtVg,11789
transformers/models/jukebox/modeling_jukebox.py,sha256=agpQO9h99JBgyI41TCtCPWVcK0IuCb1C5UnZq1GykmA,119653
transformers/models/jukebox/tokenization_jukebox.py,sha256=Kv6x-qdXBuqhcBzNbKsU4SUdhrjefZOK7J0qAk89oos,17890
transformers/models/kosmos2/__init__.py,sha256=jUzMFMa0nRBdsr0AdK08cnugtfuAWiZTFgOow25AY5o,1967
transformers/models/kosmos2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/kosmos2/__pycache__/configuration_kosmos2.cpython-310.pyc,,
transformers/models/kosmos2/__pycache__/convert_kosmos2_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/kosmos2/__pycache__/modeling_kosmos2.cpython-310.pyc,,
transformers/models/kosmos2/__pycache__/processing_kosmos2.cpython-310.pyc,,
transformers/models/kosmos2/configuration_kosmos2.py,sha256=k9kUBAv8mhN2rX38h9j3ky2KZQfn-vf1ekx1KCmCXAI,13481
transformers/models/kosmos2/convert_kosmos2_original_pytorch_checkpoint_to_pytorch.py,sha256=3ejv6hUd6irzFnmSuFVI6Eu1NVWmtJf3_ql2h9P4AHk,2724
transformers/models/kosmos2/modeling_kosmos2.py,sha256=FwQfTEONefaU20AJ9Ol-Vr31b4nCT9KjRIyLgKgkRe0,95040
transformers/models/kosmos2/processing_kosmos2.py,sha256=wwLhLGgBBgpFeRWC3os8SXLI18od-NJagHFJMe9QROo,29760
transformers/models/layoutlm/__init__.py,sha256=x-7_rGXFn-NroxQIFjQru0Rz5VfmQmINEhahNPm7R8w,3787
transformers/models/layoutlm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/layoutlm/__pycache__/configuration_layoutlm.cpython-310.pyc,,
transformers/models/layoutlm/__pycache__/modeling_layoutlm.cpython-310.pyc,,
transformers/models/layoutlm/__pycache__/modeling_tf_layoutlm.cpython-310.pyc,,
transformers/models/layoutlm/__pycache__/tokenization_layoutlm.cpython-310.pyc,,
transformers/models/layoutlm/__pycache__/tokenization_layoutlm_fast.cpython-310.pyc,,
transformers/models/layoutlm/configuration_layoutlm.py,sha256=9ZiQCdKVP6QCvlYOVBdq3UP7W39e_D_4cPHx1WBb_i0,9405
transformers/models/layoutlm/modeling_layoutlm.py,sha256=TIq4kbeg8CLnXKU2k6tvsaP7hTcCmuOLP6lZRaNynzY,60895
transformers/models/layoutlm/modeling_tf_layoutlm.py,sha256=c8feSAeVde4kNyaUQndEpfd4mvj--DY1TbZv4cSzE9M,65594
transformers/models/layoutlm/tokenization_layoutlm.py,sha256=5j9ZSKTEEngpVjSOMvD19nR2nqsXrrnKMi-SKhPiAKE,21795
transformers/models/layoutlm/tokenization_layoutlm_fast.py,sha256=imVKvRHe4UPql7w8EYIMXso7JrUEWehLRxgEO4c1q2k,8979
transformers/models/layoutlmv2/__init__.py,sha256=Ue5kj1_LyJNklq6UPXvNuaAXj_gadMT8lXxwQwIPsvY,3439
transformers/models/layoutlmv2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/configuration_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/feature_extraction_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/image_processing_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/modeling_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/processing_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/tokenization_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/tokenization_layoutlmv2_fast.cpython-310.pyc,,
transformers/models/layoutlmv2/configuration_layoutlmv2.py,sha256=6UvTakQWSugxkVNQDm5MMJcrWtxKoyVRbsNIZEIoM1U,11248
transformers/models/layoutlmv2/feature_extraction_layoutlmv2.py,sha256=M9bDCpKBLI5paxor4ioa2JjEDhSH9Np-PTbgHh2V9KI,1195
transformers/models/layoutlmv2/image_processing_layoutlmv2.py,sha256=gHFdRaOCO-EzsUZfjhm2JAc67gjIe4uC9HNK6Iay-XE,13318
transformers/models/layoutlmv2/modeling_layoutlmv2.py,sha256=XNI3WB4y1P0MRbpFPOcdlPc6aiShPpd2RIcZDeFN1Jc,60718
transformers/models/layoutlmv2/processing_layoutlmv2.py,sha256=xyhBq9pYYmNYOfK2c13gA-f1cWzu1fp0kO6FC7J9DfI,9292
transformers/models/layoutlmv2/tokenization_layoutlmv2.py,sha256=rT43zfh0DzxU9pgJpips5JTrliWXx7dHyPTFVLL_daA,72933
transformers/models/layoutlmv2/tokenization_layoutlmv2_fast.py,sha256=CcVmZyBTfxIksP9kLYhv49ifDPUF8mND8oqGwGtLf-I,38073
transformers/models/layoutlmv3/__init__.py,sha256=A4PpxK2Rhqx_ybVzlT5h9W6SyRSwndLqD5-eVKBz4ok,4512
transformers/models/layoutlmv3/__pycache__/__init__.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/configuration_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/feature_extraction_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/image_processing_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/modeling_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/modeling_tf_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/processing_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/tokenization_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/tokenization_layoutlmv3_fast.cpython-310.pyc,,
transformers/models/layoutlmv3/configuration_layoutlmv3.py,sha256=0I69vz38zqxfcYpKCg0yeIu712JgKJ8GXtqo3oPCixU,13364
transformers/models/layoutlmv3/feature_extraction_layoutlmv3.py,sha256=jWsmsi2mym0meek1lHWqfqxlJgMJdY3cgfQ_4ASEbto,1195
transformers/models/layoutlmv3/image_processing_layoutlmv3.py,sha256=bSHxYFc1j41HwsaUpVp-14oSZcVXO45N3-TVjINC3LI,18315
transformers/models/layoutlmv3/modeling_layoutlmv3.py,sha256=snVTYiTRoTSIvco7WRf8PDU16oBJVH5RrBWWptqprAU,59908
transformers/models/layoutlmv3/modeling_tf_layoutlmv3.py,sha256=OvF7zPWtM1UMCpTdfEnM5pSGI5mgzJjlrO8Ur2T_l3g,67864
transformers/models/layoutlmv3/processing_layoutlmv3.py,sha256=ShtvBmZjGHbprdB14v2QsIgVir-74gEnTGHzvL31vCI,9143
transformers/models/layoutlmv3/tokenization_layoutlmv3.py,sha256=RX1jb7pk-amGv9Z2Ens_OsdTpkfNs81uelKB9z_BwBs,72834
transformers/models/layoutlmv3/tokenization_layoutlmv3_fast.py,sha256=FyBazrusyKa5dffeIwoLPhf1MQBkMb9NrFJrv4J0gQ8,40311
transformers/models/layoutxlm/__init__.py,sha256=AIvjzuqRPFXFuWXxnOlp9pBXaIT5Zzx7fwtg2KKVETA,2037
transformers/models/layoutxlm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/layoutxlm/__pycache__/processing_layoutxlm.cpython-310.pyc,,
transformers/models/layoutxlm/__pycache__/tokenization_layoutxlm.cpython-310.pyc,,
transformers/models/layoutxlm/__pycache__/tokenization_layoutxlm_fast.cpython-310.pyc,,
transformers/models/layoutxlm/processing_layoutxlm.py,sha256=2xtffeErPXtu2tW_ya4YaHDoqWCljDPfoL2V1Jlo6JI,9242
transformers/models/layoutxlm/tokenization_layoutxlm.py,sha256=WstEa3t23U3VFe7yzAlcUG4aAsvCwOvmNdVQlgr8l4E,57502
transformers/models/layoutxlm/tokenization_layoutxlm_fast.py,sha256=szXdodi-JqFdT9_7Nx1sH8W2OOF4LlskDb-U3x4MUbY,39972
transformers/models/led/__init__.py,sha256=9CdjSo8a3H8LyFlzOxCmUUZG2icbvPJ_Q_hFcaKBf4E,3008
transformers/models/led/__pycache__/__init__.cpython-310.pyc,,
transformers/models/led/__pycache__/configuration_led.cpython-310.pyc,,
transformers/models/led/__pycache__/modeling_led.cpython-310.pyc,,
transformers/models/led/__pycache__/modeling_tf_led.cpython-310.pyc,,
transformers/models/led/__pycache__/tokenization_led.cpython-310.pyc,,
transformers/models/led/__pycache__/tokenization_led_fast.cpython-310.pyc,,
transformers/models/led/configuration_led.py,sha256=ejx2raEDvP2TlRid2bd9S9IijDA_VYQkAXZAuKz7c0U,7634
transformers/models/led/modeling_led.py,sha256=GLNn3Us84EkVJku1HIpgzzna1F8ifU41wg33HxToLmg,139060
transformers/models/led/modeling_tf_led.py,sha256=zmINwxbLBoj3iinEoEx9lLNmOXY-J__EJt5h8itbQHk,116900
transformers/models/led/tokenization_led.py,sha256=SilZj9XSZ6a4uOdceKE1BSCqnTK73t1hT1MS8XwNWlo,20406
transformers/models/led/tokenization_led_fast.py,sha256=0aN-BFq5w1AeNlZ_0C21Rr4VQ09du3aa9b-SoQJobks,15197
transformers/models/levit/__init__.py,sha256=bn2rphZqhhv59V7XPWBSS3nntAk8n8qi8o9uhqmi2do,2508
transformers/models/levit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/levit/__pycache__/configuration_levit.cpython-310.pyc,,
transformers/models/levit/__pycache__/convert_levit_timm_to_pytorch.cpython-310.pyc,,
transformers/models/levit/__pycache__/feature_extraction_levit.cpython-310.pyc,,
transformers/models/levit/__pycache__/image_processing_levit.cpython-310.pyc,,
transformers/models/levit/__pycache__/modeling_levit.cpython-310.pyc,,
transformers/models/levit/configuration_levit.py,sha256=hzMJZuunH08GKdMcBvAq5S14369-kHSYRmQ8f8I-kys,5931
transformers/models/levit/convert_levit_timm_to_pytorch.py,sha256=HKjk4WPa6DO_2CM0Qy9R3mAEOdbf71DtS-T4uqoQJ9I,6258
transformers/models/levit/feature_extraction_levit.py,sha256=l2RHbrbg9MzRqKr_ErOo_AuiSv93Gj-Oq6w0v2p-Izw,1204
transformers/models/levit/image_processing_levit.py,sha256=1ntGrh7WYQLy1N7Ky81aGbwv7aY8SjcvhiOPJFGG6JE,16641
transformers/models/levit/modeling_levit.py,sha256=8syijih7tNnJ6bgkNOnqv4DPRVliyLkLKiG1-ngM9p8,29462
transformers/models/lilt/__init__.py,sha256=bIm8VAW84HA1oTl3ZITLrjMZ9VIyJ4s6_x9R9N767nM,1909
transformers/models/lilt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/lilt/__pycache__/configuration_lilt.cpython-310.pyc,,
transformers/models/lilt/__pycache__/modeling_lilt.cpython-310.pyc,,
transformers/models/lilt/configuration_lilt.py,sha256=zNzGbpigxwdB64FLxWK19-MRnYJHinuLsszgRE_tJjI,6879
transformers/models/lilt/modeling_lilt.py,sha256=Iu4_vDKPDt2hO9DPbzC7AzDMg1INYNWj40nx8GfDrBg,52762
transformers/models/llama/__init__.py,sha256=VvfKqMiK-O6tEZ8o3dG6PH0RupN00zS0UG_fvFhmasU,3212
transformers/models/llama/__pycache__/__init__.cpython-310.pyc,,
transformers/models/llama/__pycache__/configuration_llama.cpython-310.pyc,,
transformers/models/llama/__pycache__/convert_llama_weights_to_hf.cpython-310.pyc,,
transformers/models/llama/__pycache__/modeling_flax_llama.cpython-310.pyc,,
transformers/models/llama/__pycache__/modeling_llama.cpython-310.pyc,,
transformers/models/llama/__pycache__/tokenization_llama.cpython-310.pyc,,
transformers/models/llama/__pycache__/tokenization_llama_fast.cpython-310.pyc,,
transformers/models/llama/configuration_llama.py,sha256=0iQDLCngUPH7npSek-BrAmvgw7kjuAvh4zHcmuKrDbE,9386
transformers/models/llama/convert_llama_weights_to_hf.py,sha256=OzLjH3iGzYCuVbZq0uoSRTCCUeEeNWJdSlbusmunFuw,13283
transformers/models/llama/modeling_flax_llama.py,sha256=5UaLDxttrtXneh61_lsZoXIwS6NVAea3-a7BEBAplZA,29865
transformers/models/llama/modeling_llama.py,sha256=OoquJhmTQ4lmrqtS0xTX5PIs4U3etcmg0WZ3hdvTMNM,65142
transformers/models/llama/tokenization_llama.py,sha256=jjg9Vax9Dg1N7aASt7flk_WlVRgkRkKA9_RbK8Ywbfo,22027
transformers/models/llama/tokenization_llama_fast.py,sha256=S6NcNsToPdHqmPk8hgo1fPnwjJzg6wk9CZwt0H28hYc,13095
transformers/models/llava/__init__.py,sha256=Mq-IiC-tQbmOoQvm3QHcrQ9vW01WxTl4GXaVgZf0ULg,1814
transformers/models/llava/__pycache__/__init__.cpython-310.pyc,,
transformers/models/llava/__pycache__/configuration_llava.cpython-310.pyc,,
transformers/models/llava/__pycache__/convert_llava_weights_to_hf.cpython-310.pyc,,
transformers/models/llava/__pycache__/modeling_llava.cpython-310.pyc,,
transformers/models/llava/__pycache__/processing_llava.cpython-310.pyc,,
transformers/models/llava/configuration_llava.py,sha256=v8jtu76OzbLxTr5bbnnz951xWrgKX-lGpKmsH0ofp-0,5490
transformers/models/llava/convert_llava_weights_to_hf.py,sha256=4Clbp3P_-fU1Lic9Q-fdnWncqYaIIAVFHIr9U_qQT4E,4599
transformers/models/llava/modeling_llava.py,sha256=Q7wSyE2-1b3TQ7b1tFllxGuqQeYNayWgJXBXmJycRG4,27274
transformers/models/llava/processing_llava.py,sha256=4p8HUXPuPOVfmZtTG5m7y_7-hph-R_itodMcdynxOvI,7282
transformers/models/longformer/__init__.py,sha256=mbx6LG2-PW5i_Ntq3kFn1MhnegTVAs0_ZOKAGeMi5ps,4196
transformers/models/longformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/longformer/__pycache__/configuration_longformer.cpython-310.pyc,,
transformers/models/longformer/__pycache__/convert_longformer_original_pytorch_lightning_to_pytorch.cpython-310.pyc,,
transformers/models/longformer/__pycache__/modeling_longformer.cpython-310.pyc,,
transformers/models/longformer/__pycache__/modeling_tf_longformer.cpython-310.pyc,,
transformers/models/longformer/__pycache__/tokenization_longformer.cpython-310.pyc,,
transformers/models/longformer/__pycache__/tokenization_longformer_fast.cpython-310.pyc,,
transformers/models/longformer/configuration_longformer.py,sha256=y3kUvmuolhHVas30YwUebK9YtoJxPEv3dyDDAu25uBc,9565
transformers/models/longformer/convert_longformer_original_pytorch_lightning_to_pytorch.py,sha256=gKyYNmo8Of0j_h6x8JSHaYc6hTyzJFwWETi5KectvFM,3026
transformers/models/longformer/modeling_longformer.py,sha256=J0IpW8t6gr-HlbVdgDnhz576_gAK4ZjAGmgs5533cfs,114112
transformers/models/longformer/modeling_tf_longformer.py,sha256=7jMFqFbHRp8wWfySjGmIzYEJYe1ErPIGCgXRv_8kNnY,121440
transformers/models/longformer/tokenization_longformer.py,sha256=riFnPLRdsNZWQtUFLjwJ0ymnW2gM_4Ahc5QZmya-Uz0,18950
transformers/models/longformer/tokenization_longformer_fast.py,sha256=cIlkik0w7c2NjkmLe20mxkJI5M54P65m8BEnsXPltjI,14714
transformers/models/longt5/__init__.py,sha256=nN2BIwcwmdcMffrxzPKx9oeVWsHu9wt1BUJYIPWfm3Y,2546
transformers/models/longt5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/longt5/__pycache__/configuration_longt5.cpython-310.pyc,,
transformers/models/longt5/__pycache__/convert_longt5x_checkpoint_to_flax.cpython-310.pyc,,
transformers/models/longt5/__pycache__/modeling_flax_longt5.cpython-310.pyc,,
transformers/models/longt5/__pycache__/modeling_longt5.cpython-310.pyc,,
transformers/models/longt5/configuration_longt5.py,sha256=rF_7G9sUb8ouVoUewGlO6LUHmOrpGJpDFIsGdk6mKEg,8483
transformers/models/longt5/convert_longt5x_checkpoint_to_flax.py,sha256=5LQpQWNG_8Fc0tU62eYf66RmJzUcb-RynDdrvziZEqw,11089
transformers/models/longt5/modeling_flax_longt5.py,sha256=-YQnoLnugg2z4YhCNDjhaI4qTTQlW8LlVfCHESOHcC0,105622
transformers/models/longt5/modeling_longt5.py,sha256=JYBzGwgdlrJR5NZctWneNAUKS6KVpiu959gqK4HDpus,105957
transformers/models/luke/__init__.py,sha256=xuqWDYOtcrf1vEC71vfltl8ICWfW7GyU9sP8RWD-iU4,2383
transformers/models/luke/__pycache__/__init__.cpython-310.pyc,,
transformers/models/luke/__pycache__/configuration_luke.cpython-310.pyc,,
transformers/models/luke/__pycache__/convert_luke_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/luke/__pycache__/modeling_luke.cpython-310.pyc,,
transformers/models/luke/__pycache__/tokenization_luke.cpython-310.pyc,,
transformers/models/luke/configuration_luke.py,sha256=o9Ne3lf8etNXzx4GDdgdhS-kJC8wVRCD2FvQNSGuhNE,6846
transformers/models/luke/convert_luke_original_pytorch_checkpoint_to_pytorch.py,sha256=pfnDfBvJDRyCLBLdcsalZaKV01aEz0W1og2Z364hTDs,7467
transformers/models/luke/modeling_luke.py,sha256=Gmwq_PiFdkHylhYqp_6GcjY0Y_JNxULfsvqHQ30KA24,103811
transformers/models/luke/tokenization_luke.py,sha256=0C2cPd6z6Mah4E2RUPnWnauPiqS-R5oQdNoOqp0MhXY,85434
transformers/models/lxmert/__init__.py,sha256=3rn46z5WOBmOrbr6e7zoIWh4F8Bf3hFBASDY0vxlxbI,3396
transformers/models/lxmert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/configuration_lxmert.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/convert_lxmert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/modeling_lxmert.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/modeling_tf_lxmert.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/tokenization_lxmert.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/tokenization_lxmert_fast.cpython-310.pyc,,
transformers/models/lxmert/configuration_lxmert.py,sha256=QDxTbEEQJhEIEbC6PgCyz-LfL1UZWLSJYQ40jrWpvHo,9065
transformers/models/lxmert/convert_lxmert_original_tf_checkpoint_to_pytorch.py,sha256=T3vqC76pis49OXeHODsBSBBDGDe6qnUBckwGOWySmpc,2109
transformers/models/lxmert/modeling_lxmert.py,sha256=mgZ72KaaZ-E4MOHSfezHmWa0M8L9RBJ7H770logD1qw,65037
transformers/models/lxmert/modeling_tf_lxmert.py,sha256=l13lrkXrkDsZgc1Wh8Xz4Z_krl3KHWRjSoMSql4zw7U,61761
transformers/models/lxmert/tokenization_lxmert.py,sha256=W10FN7y_8E87Q6yBt5s1snJIr-D_M39JATVbuOFvGE8,21518
transformers/models/lxmert/tokenization_lxmert_fast.py,sha256=ZeYcRkFq2mNZYyBcm8JrFIIDl2Tq3dwakz84Izy1cIU,8449
transformers/models/m2m_100/__init__.py,sha256=fT84ZTHmw2vMrme8MqfSoPZWSECY-SLXDG0AR8Z1qRc,1992
transformers/models/m2m_100/__pycache__/__init__.cpython-310.pyc,,
transformers/models/m2m_100/__pycache__/configuration_m2m_100.cpython-310.pyc,,
transformers/models/m2m_100/__pycache__/convert_m2m100_original_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/m2m_100/__pycache__/modeling_m2m_100.cpython-310.pyc,,
transformers/models/m2m_100/__pycache__/tokenization_m2m_100.cpython-310.pyc,,
transformers/models/m2m_100/configuration_m2m_100.py,sha256=3b-M2ZkofNfbpdCZOAjAPKqo4Sqbq3F7ma-0AzV8zFA,13583
transformers/models/m2m_100/convert_m2m100_original_checkpoint_to_pytorch.py,sha256=xNG8NE20odOve8Z1zKPDHJr5Ev8jM30N-mJsJqfsXtM,3159
transformers/models/m2m_100/modeling_m2m_100.py,sha256=zKEZPoAaw5E4V3gw3D-GjoewT7BgKm2qwHyDZseCQ64,64242
transformers/models/m2m_100/tokenization_m2m_100.py,sha256=3ApI9kknKSGjsc8ICyQ7cVjh0rvkMLlYc3aEQeHlVyw,17316
transformers/models/marian/__init__.py,sha256=_aQPsVh7jA_BTVbCkRprc2NmnLlkhfEtfJW_1WIwUqI,3444
transformers/models/marian/__pycache__/__init__.cpython-310.pyc,,
transformers/models/marian/__pycache__/configuration_marian.cpython-310.pyc,,
transformers/models/marian/__pycache__/convert_marian_tatoeba_to_pytorch.cpython-310.pyc,,
transformers/models/marian/__pycache__/convert_marian_to_pytorch.cpython-310.pyc,,
transformers/models/marian/__pycache__/modeling_flax_marian.cpython-310.pyc,,
transformers/models/marian/__pycache__/modeling_marian.cpython-310.pyc,,
transformers/models/marian/__pycache__/modeling_tf_marian.cpython-310.pyc,,
transformers/models/marian/__pycache__/tokenization_marian.cpython-310.pyc,,
transformers/models/marian/configuration_marian.py,sha256=-52l6myCrR46BC2SEFDUTIeQ96tZB0ot9Z9gx43q1YU,18559
transformers/models/marian/convert_marian_tatoeba_to_pytorch.py,sha256=N_YEEFgsGy2W-4QxeGD3bIIGNl_oYv64GkTw0WDpiaU,36254
transformers/models/marian/convert_marian_to_pytorch.py,sha256=G-DJhbKhZkYJwKCMTZFi7oqBDrghnJJFpXGJPjqbQ_0,26757
transformers/models/marian/modeling_flax_marian.py,sha256=E3Om7nTSSgM0zvaNoDQNJEcKqf5SrRXi7vf0b63TU-s,64260
transformers/models/marian/modeling_marian.py,sha256=OtI4y2nWkAY9BExzsPykzWHlrz42wkEu0Ag78O3z990,81832
transformers/models/marian/modeling_tf_marian.py,sha256=Grw3IK8XRdJnZxbRPonOF4ZqiY_bDWwXLUmzhE-OkKQ,68223
transformers/models/marian/tokenization_marian.py,sha256=6V4R8SBGN90vMu1O4n-akLX2wCrRIV0EBFVybMMNJlM,17756
transformers/models/markuplm/__init__.py,sha256=RjQ4xza9uhSlHJ11ZIHA19o-cWoC88fJvts8zYDOznY,2806
transformers/models/markuplm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/configuration_markuplm.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/feature_extraction_markuplm.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/modeling_markuplm.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/processing_markuplm.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/tokenization_markuplm.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/tokenization_markuplm_fast.cpython-310.pyc,,
transformers/models/markuplm/configuration_markuplm.py,sha256=VX8AW90kT3SnATh306fkXkVDxcIrEzM7k-pKRQNqzG0,7572
transformers/models/markuplm/feature_extraction_markuplm.py,sha256=3V8MR36mQskKYQeaGrWuqWo9w5JG67nhRvxzWu7fR9s,6404
transformers/models/markuplm/modeling_markuplm.py,sha256=ANbadaRLshPAm03fg-j3fh4ux_80FOt2du7KmgIDcVU,58305
transformers/models/markuplm/processing_markuplm.py,sha256=dCxh-u2OQvsoAeK0GWGDwMgZuLIgF7tu5Q7uERx5NwY,6348
transformers/models/markuplm/tokenization_markuplm.py,sha256=XmeJlKgev-gBhxPSrxDCfoOcxp7ihv3EmeGhEGHnqLw,69748
transformers/models/markuplm/tokenization_markuplm_fast.py,sha256=4Hd8ln5XrOO9nsqZcqbRYs3_Ylu5Xxre6rNJ2ddcxq0,43715
transformers/models/mask2former/__init__.py,sha256=_damTN4svyRG1tenZi3AEmsILg7QVyYbuWR_iXzrbXw,2357
transformers/models/mask2former/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mask2former/__pycache__/configuration_mask2former.cpython-310.pyc,,
transformers/models/mask2former/__pycache__/convert_mask2former_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mask2former/__pycache__/image_processing_mask2former.cpython-310.pyc,,
transformers/models/mask2former/__pycache__/modeling_mask2former.cpython-310.pyc,,
transformers/models/mask2former/configuration_mask2former.py,sha256=5NS8PUBaf1qoWwJzY5wAtYjUoMdUaFFICLLi1NWAbek,10902
transformers/models/mask2former/convert_mask2former_original_pytorch_checkpoint_to_pytorch.py,sha256=v4a-VTdnEHxZLAykOn5AgqLXZ9yFZzhY4CUu4c3XHUE,45688
transformers/models/mask2former/image_processing_mask2former.py,sha256=gTQqdzUjQh7QoeW2GfqAOAgJX3jGrRx-enzWJBELOMg,56466
transformers/models/mask2former/modeling_mask2former.py,sha256=k5BtRFcGwOrj5OYfmF8QlY9cc9C5BfJIkpse2c1_g4g,120096
transformers/models/maskformer/__init__.py,sha256=Sy9sX8-Vb9Gnn9gjU34M4pDh3jJZd7vmr5aorB9N5lw,2945
transformers/models/maskformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/configuration_maskformer.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/configuration_maskformer_swin.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/convert_maskformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/convert_maskformer_resnet_to_pytorch.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/convert_maskformer_swin_to_pytorch.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/feature_extraction_maskformer.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/image_processing_maskformer.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/modeling_maskformer.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/modeling_maskformer_swin.cpython-310.pyc,,
transformers/models/maskformer/configuration_maskformer.py,sha256=NcpPz5lyQi6A9osSJNhOPSSmsDgYxV6b3DjhFert8_Q,8794
transformers/models/maskformer/configuration_maskformer_swin.py,sha256=ViZ9nzV-8J5-cJ1Gcf8Wb6XHwSUYRSW11Rz5ePiJ4Sw,7055
transformers/models/maskformer/convert_maskformer_original_pytorch_checkpoint_to_pytorch.py,sha256=CEKaBhurc8x3mvE7YMqfULIoybxq0Guj0hGHJouG5s8,32237
transformers/models/maskformer/convert_maskformer_resnet_to_pytorch.py,sha256=iUMC5om4caBO1eSeivN3sZYsbEtYZAeJZE7I1NIygR4,20732
transformers/models/maskformer/convert_maskformer_swin_to_pytorch.py,sha256=-GWvua0iYDbJYZ7VUcywp0rf-jR7iKXz8az9N4r5k_0,20321
transformers/models/maskformer/feature_extraction_maskformer.py,sha256=MMPQuQY2EnK4vixDve-I-PIFqCDWQNYYeVdAYvIY8HY,1214
transformers/models/maskformer/image_processing_maskformer.py,sha256=BwUwP8aJL0dQf_4Dbx6J58fjf7qCkQnWjIAiWRAtYvg,58307
transformers/models/maskformer/modeling_maskformer.py,sha256=He1DBMI05HSGYj8dF8W6zHq5NhQhuUDhMx4pEF76MGM,93105
transformers/models/maskformer/modeling_maskformer_swin.py,sha256=2DyRWtHLA077-GWY0Z2mngv62I0RpGVHKr3NhIJm3c8,40758
transformers/models/mbart/__init__.py,sha256=N1NqaZU1QPNt3r2VI3y4sv-XwdBkAtV-41REYSah7w4,4403
transformers/models/mbart/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mbart/__pycache__/configuration_mbart.cpython-310.pyc,,
transformers/models/mbart/__pycache__/convert_mbart_original_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mbart/__pycache__/modeling_flax_mbart.cpython-310.pyc,,
transformers/models/mbart/__pycache__/modeling_mbart.cpython-310.pyc,,
transformers/models/mbart/__pycache__/modeling_tf_mbart.cpython-310.pyc,,
transformers/models/mbart/__pycache__/tokenization_mbart.cpython-310.pyc,,
transformers/models/mbart/__pycache__/tokenization_mbart_fast.cpython-310.pyc,,
transformers/models/mbart/configuration_mbart.py,sha256=zYZCbvRCEqmDZoxRvEILeXL3qEpp0jc4TRHy634RV5U,18388
transformers/models/mbart/convert_mbart_original_checkpoint_to_pytorch.py,sha256=xVW9Mj-jd7X_MImJCgS52Aok1CGPf-E6u8ptvG1hK8o,3035
transformers/models/mbart/modeling_flax_mbart.py,sha256=uUgTTL5zTGbJZX45q4YoPKiSbizfXNsx8jr-T7P2C_c,75090
transformers/models/mbart/modeling_mbart.py,sha256=QPbrxhYO5dAV-0shhtNEeLZAiwODvrUbxLRMMa_yeuk,100984
transformers/models/mbart/modeling_tf_mbart.py,sha256=tx7UJm0gZgiGX8L-nk6r4mKae9smX5kHjy_YnWzizQo,68919
transformers/models/mbart/tokenization_mbart.py,sha256=DcRh3oa0zy7ZlcPc1t8OgUdNOPJWuxJoS0a25T4G99A,14719
transformers/models/mbart/tokenization_mbart_fast.py,sha256=Xoc_K63xih5PbrjtGdMepr33p8mpxzN_OP6UXT-Jy6A,11878
transformers/models/mbart50/__init__.py,sha256=5ekQCS9OkL3_5UJXnu7Z5cVeCi76pVgAxHkC8qQ8XKk,1847
transformers/models/mbart50/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mbart50/__pycache__/tokenization_mbart50.cpython-310.pyc,,
transformers/models/mbart50/__pycache__/tokenization_mbart50_fast.cpython-310.pyc,,
transformers/models/mbart50/tokenization_mbart50.py,sha256=7ozEd-xpj_ilCU2mxOp4CZ1uBhKFkO5_2CCP0lmwgLI,16664
transformers/models/mbart50/tokenization_mbart50_fast.py,sha256=-YTkCwhpgmu9EywN4--3COQq9hF58vNYysUxZpJU-Q4,12258
transformers/models/mega/__init__.py,sha256=sJJLSLHF1HMGGOkDRFol40JHptUCxSDiB0yUUbvDVL4,2140
transformers/models/mega/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mega/__pycache__/configuration_mega.cpython-310.pyc,,
transformers/models/mega/__pycache__/convert_mega_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mega/__pycache__/modeling_mega.cpython-310.pyc,,
transformers/models/mega/configuration_mega.py,sha256=rgqfmeGucfEh9VWoO7sjNDfu-nML6rLrv16rMoiSt6A,12739
transformers/models/mega/convert_mega_original_pytorch_checkpoint_to_pytorch.py,sha256=FK9gAgMB5VEO2Fji39w100ywUJ8wA8utdmWRZFanb2c,13154
transformers/models/mega/modeling_mega.py,sha256=GMAwDlPtLTJl4gySJfPVOiZoDoVYj7r8-iH2bHfpNj4,109550
transformers/models/megatron_bert/__init__.py,sha256=TUAneYZq0bKIQqKDcED_EuJhgnzOnWNrNrye_x8KX90,2506
transformers/models/megatron_bert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/megatron_bert/__pycache__/configuration_megatron_bert.cpython-310.pyc,,
transformers/models/megatron_bert/__pycache__/convert_megatron_bert_checkpoint.cpython-310.pyc,,
transformers/models/megatron_bert/__pycache__/modeling_megatron_bert.cpython-310.pyc,,
transformers/models/megatron_bert/configuration_megatron_bert.py,sha256=Sc24aLdbtyYQPEVxPohrBSS9VeNW9-JH8GUEcYoUb8g,6574
transformers/models/megatron_bert/convert_megatron_bert_checkpoint.py,sha256=VAMD1MFdVG8w9cQkRfmlZCEvaMgoo-lyFI9deunD5OA,13686
transformers/models/megatron_bert/modeling_megatron_bert.py,sha256=qAWHUPLMGI2XgWqL3K__1n9-X7OoDljkzDTZbHMQNMQ,83399
transformers/models/megatron_gpt2/__init__.py,sha256=WycFl9cUevoXIBhB76qKtnNRIPMk2LoTDkmkfAfOy9M,630
transformers/models/megatron_gpt2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/megatron_gpt2/__pycache__/checkpoint_reshaping_and_interoperability.cpython-310.pyc,,
transformers/models/megatron_gpt2/__pycache__/convert_megatron_gpt2_checkpoint.cpython-310.pyc,,
transformers/models/megatron_gpt2/checkpoint_reshaping_and_interoperability.py,sha256=zlFqC1mZRXZf2qWePXQMjGbdIRBLcilCdjtlAH0YTWI,36675
transformers/models/megatron_gpt2/convert_megatron_gpt2_checkpoint.py,sha256=SGuAbuQ08_0Bd1milb0RcchbRR6EeR4S1CKVRrJuNKI,13627
transformers/models/mgp_str/__init__.py,sha256=YMCtFGSXL18Kh4Pm3KTBEgtxlaDDYwb3WnMFsEsaJ-4,2164
transformers/models/mgp_str/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mgp_str/__pycache__/configuration_mgp_str.cpython-310.pyc,,
transformers/models/mgp_str/__pycache__/modeling_mgp_str.cpython-310.pyc,,
transformers/models/mgp_str/__pycache__/processing_mgp_str.cpython-310.pyc,,
transformers/models/mgp_str/__pycache__/tokenization_mgp_str.cpython-310.pyc,,
transformers/models/mgp_str/configuration_mgp_str.py,sha256=-DlPYCu9xFlSbtS1r3YnZu0x3PFa0_Q-OtQG2ZVHpXA,5937
transformers/models/mgp_str/modeling_mgp_str.py,sha256=E2mlwPBlHQAXuWAhaGfYyeHvi7i6Gbm9dRUJiO-U4xU,22053
transformers/models/mgp_str/processing_mgp_str.py,sha256=aua3QDunS6LfX7JK2HQBCM0Wq7nFejBljRnCpYwJ2qE,9269
transformers/models/mgp_str/tokenization_mgp_str.py,sha256=_EUA0gIB3kdAsj9Z-4ridLMAz-k57JecGGmk4kifWVE,4113
transformers/models/mistral/__init__.py,sha256=zp6hZXzkTTi2pJNCMBD5sIcAMiR7H2KRtlS6v9FHzj0,1806
transformers/models/mistral/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mistral/__pycache__/configuration_mistral.cpython-310.pyc,,
transformers/models/mistral/__pycache__/convert_mistral_weights_to_hf.cpython-310.pyc,,
transformers/models/mistral/__pycache__/modeling_mistral.cpython-310.pyc,,
transformers/models/mistral/configuration_mistral.py,sha256=hrZcftzIpp9B2YipjjTBY5bXsefDQSZ14w_UlWdy4yE,7170
transformers/models/mistral/convert_mistral_weights_to_hf.py,sha256=bG8KXwc1rd3kSd5IothmZGiDiOfhERfh3VrS6_wOaoM,10725
transformers/models/mistral/modeling_mistral.py,sha256=lnVDefqGsJlAvX20-eoatWcDVjTvAmjDL3zHmqOBmqM,57880
transformers/models/mixtral/__init__.py,sha256=gUOb9IB2p_2uISpGaLaKXTWW0-nWVa4INgiTZmO8guE,1806
transformers/models/mixtral/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mixtral/__pycache__/configuration_mixtral.cpython-310.pyc,,
transformers/models/mixtral/__pycache__/convert_mixtral_weights_to_hf.cpython-310.pyc,,
transformers/models/mixtral/__pycache__/modeling_mixtral.cpython-310.pyc,,
transformers/models/mixtral/configuration_mixtral.py,sha256=6fTXVF4qCj-Wx4JYCzf28A3LyUgFeMxNREzv9w65c90,8068
transformers/models/mixtral/convert_mixtral_weights_to_hf.py,sha256=zIy-8QJ-2emSsM24JqF_V67LdPCfEvYQ4Rzp9VluY7o,9116
transformers/models/mixtral/modeling_mixtral.py,sha256=cCUpEuybTjwuiJephKR1UV0FFpW2PbQoDRl6X53zSLI,66284
transformers/models/mluke/__init__.py,sha256=Pj0GBjIU6vYdhEzO7M8O35c5Jj4ivIIGAiLABhN4K7U,1356
transformers/models/mluke/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mluke/__pycache__/convert_mluke_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mluke/__pycache__/tokenization_mluke.cpython-310.pyc,,
transformers/models/mluke/convert_mluke_original_pytorch_checkpoint_to_pytorch.py,sha256=G6Z94-1_AiilSTU96PSjX_pdgFIx-b_bk8xlMKX5TuE,10185
transformers/models/mluke/tokenization_mluke.py,sha256=ZAvXTz5UeLCW3b22YWaJd2-4nXtp_qOopnKTuy0LVtY,81498
transformers/models/mobilebert/__init__.py,sha256=Gpd8kL6D0UrD5ufVg0MjcknSeHhtlLnD3Bkrzqao4Ok,4604
transformers/models/mobilebert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/configuration_mobilebert.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/convert_mobilebert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/modeling_mobilebert.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/modeling_tf_mobilebert.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/tokenization_mobilebert.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/tokenization_mobilebert_fast.cpython-310.pyc,,
transformers/models/mobilebert/configuration_mobilebert.py,sha256=KWThAd4EaQqWZ-WYrw_x2NxTNKxm5rET6psotuz4DFc,8587
transformers/models/mobilebert/convert_mobilebert_original_tf_checkpoint_to_pytorch.py,sha256=MRW9sorswIo4RiWq7PVVmaZsYm4wJEc1-DhcLzssDRU,2200
transformers/models/mobilebert/modeling_mobilebert.py,sha256=wP_NeNbqlMF1GzYmXwnyE_dTLNOLuZjLc5JdW1BJzMA,70540
transformers/models/mobilebert/modeling_tf_mobilebert.py,sha256=d3CFkcZwzcJQ27ztcmnSuIhP4bke5BviB5CHehSK5i0,71406
transformers/models/mobilebert/tokenization_mobilebert.py,sha256=3_efEClwdcoZJNFeud1-gq9GzzD1ZTt1hChmzaY7NyY,21401
transformers/models/mobilebert/tokenization_mobilebert_fast.py,sha256=Jp83WhGI3-0dm6Hl0UL4fFsWXX2ysiOeL2mcTWRtKvk,8389
transformers/models/mobilenet_v1/__init__.py,sha256=rbZvH8u5nov7gMxVexJZTVa8yJSIwI4ZHilp8sTEw64,2735
transformers/models/mobilenet_v1/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mobilenet_v1/__pycache__/configuration_mobilenet_v1.cpython-310.pyc,,
transformers/models/mobilenet_v1/__pycache__/convert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mobilenet_v1/__pycache__/feature_extraction_mobilenet_v1.cpython-310.pyc,,
transformers/models/mobilenet_v1/__pycache__/image_processing_mobilenet_v1.cpython-310.pyc,,
transformers/models/mobilenet_v1/__pycache__/modeling_mobilenet_v1.cpython-310.pyc,,
transformers/models/mobilenet_v1/configuration_mobilenet_v1.py,sha256=ze6KbgS1Ow0tIm43SijdxFXuZe8OaxPGdmIqUNz7woI,5238
transformers/models/mobilenet_v1/convert_original_tf_checkpoint_to_pytorch.py,sha256=XjGgfnPQBWp-0pNakJ1CNU1YnoYfeXCZ9WSIrTf02n8,4932
transformers/models/mobilenet_v1/feature_extraction_mobilenet_v1.py,sha256=goR0AC-IhWMrQlvzSK_0Zej42JYN-oswSGNQWnIOENU,1222
transformers/models/mobilenet_v1/image_processing_mobilenet_v1.py,sha256=dI921hPlrcNUuLsS0ZWut19_M3gnsVKcyFTPNIshd-8,15395
transformers/models/mobilenet_v1/modeling_mobilenet_v1.py,sha256=Ze_zq7rncOY95Nfss9i1s5j-1GLwtQJ-iL0nZAP3GeQ,18777
transformers/models/mobilenet_v2/__init__.py,sha256=p4OHu9O6JD4N2TcjOgLu7S2u151xEvGwvdHizbzevc0,2830
transformers/models/mobilenet_v2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mobilenet_v2/__pycache__/configuration_mobilenet_v2.cpython-310.pyc,,
transformers/models/mobilenet_v2/__pycache__/convert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mobilenet_v2/__pycache__/feature_extraction_mobilenet_v2.cpython-310.pyc,,
transformers/models/mobilenet_v2/__pycache__/image_processing_mobilenet_v2.cpython-310.pyc,,
transformers/models/mobilenet_v2/__pycache__/modeling_mobilenet_v2.cpython-310.pyc,,
transformers/models/mobilenet_v2/configuration_mobilenet_v2.py,sha256=0beA3D50hZE2Rbg6G-FYDwH_MYU52bYTwtaOuc2EEM8,7362
transformers/models/mobilenet_v2/convert_original_tf_checkpoint_to_pytorch.py,sha256=acsdT3rMMqCPV9whw2xyiVK1UOs8tr8ySvYRFNRmVWM,6402
transformers/models/mobilenet_v2/feature_extraction_mobilenet_v2.py,sha256=_IUVvyoMBsqymCoh-CVmoswZ4nOBpqFJlaoUfD8WQ3E,1222
transformers/models/mobilenet_v2/image_processing_mobilenet_v2.py,sha256=oskeOIj-LDRGlm2odHU8FCYxdtOH09Iq6woIaG4dwD4,17750
transformers/models/mobilenet_v2/modeling_mobilenet_v2.py,sha256=FMRJ4P9qiepWWECM5hlTMYdlbIlfm6bA0rZ6gSCCRrQ,34752
transformers/models/mobilevit/__init__.py,sha256=AN8UeJz0pDko_ezgS5J4cYAZT3P6Hv2EZKlqZGnkgSI,3492
transformers/models/mobilevit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/configuration_mobilevit.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/convert_mlcvnets_to_pytorch.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/feature_extraction_mobilevit.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/image_processing_mobilevit.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/modeling_mobilevit.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/modeling_tf_mobilevit.cpython-310.pyc,,
transformers/models/mobilevit/configuration_mobilevit.py,sha256=YhL9APCAC0OrB3jSOv_o2_ZdZVLAmiZ1YeJf4UVM0HM,8402
transformers/models/mobilevit/convert_mlcvnets_to_pytorch.py,sha256=Ng8zzr_CxIO9IFcf0ijXqR_EWJeAhhQ3HAkethSpCn4,12402
transformers/models/mobilevit/feature_extraction_mobilevit.py,sha256=na2H01bKIhQsyCHayPaVase5HRGRmmO7zVDDuY76Uj0,1207
transformers/models/mobilevit/image_processing_mobilevit.py,sha256=L9b3z6qD4cs6nCrxNPPS58zjvck_wBog0iHM81sbikc,16771
transformers/models/mobilevit/modeling_mobilevit.py,sha256=LNfQxBUNZGubEEugkVgXVvWi8lZDNCKo50LJ74C1Bhc,40158
transformers/models/mobilevit/modeling_tf_mobilevit.py,sha256=LYCEiYqoYw0dCfKN_QFuwBNoke0i2AlsGt1gGPrbbyE,44632
transformers/models/mobilevitv2/__init__.py,sha256=kSj85QHMKZk8_MdSUYKIsFL6V8SCAJWQlzo1hlvlYw8,2111
transformers/models/mobilevitv2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mobilevitv2/__pycache__/configuration_mobilevitv2.cpython-310.pyc,,
transformers/models/mobilevitv2/__pycache__/convert_mlcvnets_to_pytorch.cpython-310.pyc,,
transformers/models/mobilevitv2/__pycache__/modeling_mobilevitv2.cpython-310.pyc,,
transformers/models/mobilevitv2/configuration_mobilevitv2.py,sha256=bGvTY_kJmIhO8FwkuDp1sYRJlXOoXHXVmTxUla8jfG8,7243
transformers/models/mobilevitv2/convert_mlcvnets_to_pytorch.py,sha256=ZzEtog7BRgGK8W0zwC_peXQOOaBkuduPO3Tbq9_xtjo,12557
transformers/models/mobilevitv2/modeling_mobilevitv2.py,sha256=7LLWDFIQkrKImKVvC-NFdLhNN8FskXEbfeVbi-csyu4,38366
transformers/models/mpnet/__init__.py,sha256=hyB4jNWDdoHWggavnqLZEF85f9a11vXSTKaLWTdPh-k,3875
transformers/models/mpnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mpnet/__pycache__/configuration_mpnet.cpython-310.pyc,,
transformers/models/mpnet/__pycache__/modeling_mpnet.cpython-310.pyc,,
transformers/models/mpnet/__pycache__/modeling_tf_mpnet.cpython-310.pyc,,
transformers/models/mpnet/__pycache__/tokenization_mpnet.cpython-310.pyc,,
transformers/models/mpnet/__pycache__/tokenization_mpnet_fast.cpython-310.pyc,,
transformers/models/mpnet/configuration_mpnet.py,sha256=pDol_52Mz0I2b-aeWNGRTdQcwh5XVf_XadKtG7B6Rpk,5443
transformers/models/mpnet/modeling_mpnet.py,sha256=6Ps9FyoNpYEVhxmy1h0b55MZ4Hb4BBUSGbF5fzVQPPE,42630
transformers/models/mpnet/modeling_tf_mpnet.py,sha256=DDSpWHLgfxggqghYcQyF51e2TBC70BMbulfCelmL4OE,48395
transformers/models/mpnet/tokenization_mpnet.py,sha256=8mlkAbEeNgVr1Z0OlYkM85j2fuCzlZZk74zb4vbkJt4,22650
transformers/models/mpnet/tokenization_mpnet_fast.py,sha256=-KokNeluUanSsREUrO7B9fhZYvEScGQcac7tGUmUuL8,9821
transformers/models/mpt/__init__.py,sha256=ZH7_XPJ100kSo0osi0XxzbkyFHj6HnS9ghjxpsqVXac,1977
transformers/models/mpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mpt/__pycache__/configuration_mpt.cpython-310.pyc,,
transformers/models/mpt/__pycache__/modeling_mpt.cpython-310.pyc,,
transformers/models/mpt/configuration_mpt.py,sha256=_uywOO0DMrIPSnMmsnksZ4QbX3A0ubnKHycWhtiW1qA,11364
transformers/models/mpt/modeling_mpt.py,sha256=542pqIl5EaJ18fIXsseOthwvqhDnD5mXoaV9iqYnW4A,40783
transformers/models/mra/__init__.py,sha256=CotdFTXkFtz90MDv55my886vc-0VBxs8h3mnGs-z7WQ,2254
transformers/models/mra/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mra/__pycache__/configuration_mra.cpython-310.pyc,,
transformers/models/mra/__pycache__/convert_mra_pytorch_to_pytorch.cpython-310.pyc,,
transformers/models/mra/__pycache__/modeling_mra.cpython-310.pyc,,
transformers/models/mra/configuration_mra.py,sha256=XoMwVVSRNFlgkp86MGYxfITCwWJEj0YLAOaKBaN0W80,6663
transformers/models/mra/convert_mra_pytorch_to_pytorch.py,sha256=LhaVlQ4q88gtewg-geRYZ748xQ3brLLhyDIo-OGWSdI,4247
transformers/models/mra/modeling_mra.py,sha256=OuHJY3FVGrNsN_v0LRNfrs9wyyIq5wdl_XbhS_7Y--E,62057
transformers/models/mt5/__init__.py,sha256=dXw11-ljMOjFnaJOe_p7NUtDc3O9ykD0_EGUbVdeAHI,3521
transformers/models/mt5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mt5/__pycache__/configuration_mt5.cpython-310.pyc,,
transformers/models/mt5/__pycache__/modeling_flax_mt5.cpython-310.pyc,,
transformers/models/mt5/__pycache__/modeling_mt5.cpython-310.pyc,,
transformers/models/mt5/__pycache__/modeling_tf_mt5.cpython-310.pyc,,
transformers/models/mt5/configuration_mt5.py,sha256=Ri1q9NqTPd43HYOME2BNaq1MWy35jusXSx0LajYCX_g,8010
transformers/models/mt5/modeling_flax_mt5.py,sha256=1p8D9st-unpG0rcRGDrUQG__3GIFa77Wst8cYgOGVng,4243
transformers/models/mt5/modeling_mt5.py,sha256=_Iu_6OieWsmf7RnW9lM_NCpZZmZsXFQCjfRx_jV3XYM,109738
transformers/models/mt5/modeling_tf_mt5.py,sha256=9Stq04drvy7iyZaptOzmDAWsUzXsKoTFTNsvCjceq_E,3326
transformers/models/musicgen/__init__.py,sha256=EY9dwTvFbwcUcdSclI-kp8xvRO24giI4UJMAmiOWIr0,2099
transformers/models/musicgen/__pycache__/__init__.cpython-310.pyc,,
transformers/models/musicgen/__pycache__/configuration_musicgen.cpython-310.pyc,,
transformers/models/musicgen/__pycache__/convert_musicgen_transformers.cpython-310.pyc,,
transformers/models/musicgen/__pycache__/modeling_musicgen.cpython-310.pyc,,
transformers/models/musicgen/__pycache__/processing_musicgen.cpython-310.pyc,,
transformers/models/musicgen/configuration_musicgen.py,sha256=Mp_26gBxRttiYP_Qy5V5DQ8fjIsK0d7GhN0ViJl_reg,10872
transformers/models/musicgen/convert_musicgen_transformers.py,sha256=i7sML5cjw29lK_ufbmaIhAP3FzaCbgXhCZ4AiZ5AAio,9377
transformers/models/musicgen/modeling_musicgen.py,sha256=RsnXHcp-qYFl7f1bCqCBKDZxo0f0QoGRq0aEfuPJ2Bk,125290
transformers/models/musicgen/processing_musicgen.py,sha256=wJE7gvyKPFVyMj5O_pD1Tg1BCC3RizsRIyHo_eV4_os,5666
transformers/models/mvp/__init__.py,sha256=w3eswhHeLn9gayC1Cl8kfkkMGtD036aJeZF2541NmqM,2536
transformers/models/mvp/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mvp/__pycache__/configuration_mvp.cpython-310.pyc,,
transformers/models/mvp/__pycache__/modeling_mvp.cpython-310.pyc,,
transformers/models/mvp/__pycache__/tokenization_mvp.cpython-310.pyc,,
transformers/models/mvp/__pycache__/tokenization_mvp_fast.cpython-310.pyc,,
transformers/models/mvp/configuration_mvp.py,sha256=7WXWilTBmDQpEvO47ioq0NqJdRRYJy6UlGAn0SWjj4s,8534
transformers/models/mvp/modeling_mvp.py,sha256=23i4U46XL3B4OdDkjHoCnNc1WhoQt8TVJVV8FlaRNfc,93308
transformers/models/mvp/tokenization_mvp.py,sha256=1_FBipW0EUT4tATW8Ki24C_HvRoWKePX30nIrwgdv6o,16781
transformers/models/mvp/tokenization_mvp_fast.py,sha256=rxC_tZB2BMZ56nh7TZ16Js0eNKP40HUtQTq2hMTDy2U,12979
transformers/models/nat/__init__.py,sha256=YY8yjsIBbTC1eZRAnR4_p_gHQ3n4JyywB2G1JQuM4AQ,1776
transformers/models/nat/__pycache__/__init__.cpython-310.pyc,,
transformers/models/nat/__pycache__/configuration_nat.cpython-310.pyc,,
transformers/models/nat/__pycache__/modeling_nat.cpython-310.pyc,,
transformers/models/nat/configuration_nat.py,sha256=qoFWNotpcDNyqnWStVk9rjEQs7WQBBt8OI8dU6GBOYk,7033
transformers/models/nat/modeling_nat.py,sha256=iikgFMfNgGOgyGjY3bA3D-W2q_cGXXJODUQ3GVTK_WA,39967
transformers/models/nezha/__init__.py,sha256=ae3hJzlO_gAa20enOImKo15phpgIXk2_Zt8tVLAY3MU,2233
transformers/models/nezha/__pycache__/__init__.cpython-310.pyc,,
transformers/models/nezha/__pycache__/configuration_nezha.cpython-310.pyc,,
transformers/models/nezha/__pycache__/modeling_nezha.cpython-310.pyc,,
transformers/models/nezha/configuration_nezha.py,sha256=VWlhTUiUP-vWfZ85eDX5Ue3BlBWUr3dystZRuql02Ec,5034
transformers/models/nezha/modeling_nezha.py,sha256=F4I3saBysU9ryPwiQSd95CLtPDoTdk4pQ9e8OOYtK-s,74837
transformers/models/nllb/__init__.py,sha256=tM7_FdmE7zOQm68GoRQiRt1jbYfPea9kC24QJSSMgIE,1868
transformers/models/nllb/__pycache__/__init__.cpython-310.pyc,,
transformers/models/nllb/__pycache__/tokenization_nllb.cpython-310.pyc,,
transformers/models/nllb/__pycache__/tokenization_nllb_fast.cpython-310.pyc,,
transformers/models/nllb/tokenization_nllb.py,sha256=DXeqPIGY1R6UeeuNXtebj6GIQ6s6fRLqYoOEU9QU_yI,20021
transformers/models/nllb/tokenization_nllb_fast.py,sha256=Efe1hRb2OcdSEpNAOjEv5iIs8gKAbxH5oC1xQOgYZeE,16995
transformers/models/nllb_moe/__init__.py,sha256=ULdz8wrqlqfamWMIQpjmmkPJPPznr34f2JxkYkqquCQ,1978
transformers/models/nllb_moe/__pycache__/__init__.cpython-310.pyc,,
transformers/models/nllb_moe/__pycache__/configuration_nllb_moe.cpython-310.pyc,,
transformers/models/nllb_moe/__pycache__/convert_nllb_moe_sharded_original_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/nllb_moe/__pycache__/modeling_nllb_moe.cpython-310.pyc,,
transformers/models/nllb_moe/configuration_nllb_moe.py,sha256=g91oXnPTLuH-cFhvBCpuXTamEwPpgASHCyXr4jhsaMc,11316
transformers/models/nllb_moe/convert_nllb_moe_sharded_original_checkpoint_to_pytorch.py,sha256=c9Zab9qVzNESk0U2exJNaoDwUQo_Q7ZpcZHViZjqTQQ,6477
transformers/models/nllb_moe/modeling_nllb_moe.py,sha256=IgSd_O-skcVics2fVbS3SzHwIqUhsqWC-a2-bCeGNRU,85192
transformers/models/nougat/__init__.py,sha256=2cSw40yf-T81USela2GvWs-NSXWHkOa6zJ_3BO7QSCY,1914
transformers/models/nougat/__pycache__/__init__.cpython-310.pyc,,
transformers/models/nougat/__pycache__/convert_nougat_to_hf.cpython-310.pyc,,
transformers/models/nougat/__pycache__/image_processing_nougat.cpython-310.pyc,,
transformers/models/nougat/__pycache__/processing_nougat.cpython-310.pyc,,
transformers/models/nougat/__pycache__/tokenization_nougat_fast.cpython-310.pyc,,
transformers/models/nougat/convert_nougat_to_hf.py,sha256=S6wb6SK-46EHmBvoNSu8n-C1RgbOwzL7XBtCSmTHLrM,10941
transformers/models/nougat/image_processing_nougat.py,sha256=M3VbAQqI1U3IphWOsJvGSWQgRqsTOrMsaVHKend4OYE,23680
transformers/models/nougat/processing_nougat.py,sha256=65OZ7-XvFeiEwFjEi69ZDY931w6NvHTHGo9EixCVxKU,6731
transformers/models/nougat/tokenization_nougat_fast.py,sha256=Zm-g0KwMQA8M84NxjiCqyok8y4OuC3PaulzAe9udaLU,25080
transformers/models/nystromformer/__init__.py,sha256=80Fr1KQ5iZtS-bmWIrqfo26_Yp43SbHRv_YSloD2J4I,2337
transformers/models/nystromformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/nystromformer/__pycache__/configuration_nystromformer.cpython-310.pyc,,
transformers/models/nystromformer/__pycache__/convert_nystromformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/nystromformer/__pycache__/modeling_nystromformer.cpython-310.pyc,,
transformers/models/nystromformer/configuration_nystromformer.py,sha256=HUtb94ZWEv_QVibPB_pJgh4oSMb9eDDewpIMTcStqVo,6624
transformers/models/nystromformer/convert_nystromformer_original_pytorch_checkpoint_to_pytorch.py,sha256=8K5IGFosME-LAljFLuTc09oce1IwxZDcxw1KPHsamqc,4197
transformers/models/nystromformer/modeling_nystromformer.py,sha256=_XDfZfOAIkwJIv28GEgHrIo1WZTUwNh1VeoeCUPqOsE,48814
transformers/models/oneformer/__init__.py,sha256=mhWiuUMUOFF1ba9KLNdNJYPYScCLxlZ61WiyO995jjo,2402
transformers/models/oneformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/oneformer/__pycache__/configuration_oneformer.cpython-310.pyc,,
transformers/models/oneformer/__pycache__/convert_to_hf_oneformer.cpython-310.pyc,,
transformers/models/oneformer/__pycache__/image_processing_oneformer.cpython-310.pyc,,
transformers/models/oneformer/__pycache__/modeling_oneformer.cpython-310.pyc,,
transformers/models/oneformer/__pycache__/processing_oneformer.cpython-310.pyc,,
transformers/models/oneformer/configuration_oneformer.py,sha256=a70cz7GJzQm3rBWgFg_1Y4HCHEoow6jU4pFVxW_j-V0,11980
transformers/models/oneformer/convert_to_hf_oneformer.py,sha256=yBWS0SE1sGS9UqCzX2EdbhAiIWvBCumSBwutJ8VQFF4,50691
transformers/models/oneformer/image_processing_oneformer.py,sha256=mUIvFsuVdzR_uk8iP8ArrGcdppEZRDzT9Ye0i5rdmzU,60109
transformers/models/oneformer/modeling_oneformer.py,sha256=PRttuYeL6xNQXnAU2u-OpCn6tT3kN1nr8ZGs9qwTJT0,143316
transformers/models/oneformer/processing_oneformer.py,sha256=WimwZxD8qx7f4tna3czw_Xx35qvTINa2cc485P6lDrU,9483
transformers/models/openai/__init__.py,sha256=5Y0BYw7AWmCFdxKdBMd4-wTi9wj6-8lX7Ii1WvFlfA8,3658
transformers/models/openai/__pycache__/__init__.cpython-310.pyc,,
transformers/models/openai/__pycache__/configuration_openai.cpython-310.pyc,,
transformers/models/openai/__pycache__/convert_openai_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/openai/__pycache__/modeling_openai.cpython-310.pyc,,
transformers/models/openai/__pycache__/modeling_tf_openai.cpython-310.pyc,,
transformers/models/openai/__pycache__/tokenization_openai.cpython-310.pyc,,
transformers/models/openai/__pycache__/tokenization_openai_fast.cpython-310.pyc,,
transformers/models/openai/configuration_openai.py,sha256=sMUMuJUX50WaVhGgGwzHR5cUbf6XqYLT4oxoiWwyF-0,7165
transformers/models/openai/convert_openai_original_tf_checkpoint_to_pytorch.py,sha256=nAomaHvwIi5gFuedK1WtT61GCu5tBxLE5zj6bY-fjGo,2666
transformers/models/openai/modeling_openai.py,sha256=aiE8tea8I_WoPgsO7vJUZuLzl5LOpoJ2pbLJHMiXxBk,38155
transformers/models/openai/modeling_tf_openai.py,sha256=oKdwKlUbHY3Y2ecRT3UfSy5iHQ3cnoODoTd6chnIizk,37690
transformers/models/openai/tokenization_openai.py,sha256=YypUOPkyVHO7PrG8kpQnjsr0MrYE5bDqHnw9zCHAR8o,15582
transformers/models/openai/tokenization_openai_fast.py,sha256=5Y7x0FEFOh7TwjNmGWKNq5WheUfTTSDJDH7C-QFVxJk,3046
transformers/models/opt/__init__.py,sha256=MQ8MhQamtoySbkT8WbqZ48mMUxp5Ae_UGX2Sl3HKPEc,2977
transformers/models/opt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/opt/__pycache__/configuration_opt.cpython-310.pyc,,
transformers/models/opt/__pycache__/convert_opt_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/opt/__pycache__/modeling_flax_opt.cpython-310.pyc,,
transformers/models/opt/__pycache__/modeling_opt.cpython-310.pyc,,
transformers/models/opt/__pycache__/modeling_tf_opt.cpython-310.pyc,,
transformers/models/opt/configuration_opt.py,sha256=iG9UEpwyKXGYzuk_SzeSy_jAtK7-TLRB5seoD3wv4pQ,7245
transformers/models/opt/convert_opt_original_pytorch_checkpoint_to_pytorch.py,sha256=7dHR6Tk9BBuFMEmHOxbu0jDf-gOnYFPsPLLH6SsA1gI,3858
transformers/models/opt/modeling_flax_opt.py,sha256=MHJpXRbl4u1JcgWkV58DmS6n0wEOTYpZBeOJQFzdBT0,31541
transformers/models/opt/modeling_opt.py,sha256=geVEmiycIu66bLTuAnLAX8bhrBYZtaTbwrM-yANK_j4,68663
transformers/models/opt/modeling_tf_opt.py,sha256=qGW9KG15G1Pu_MCAFlwhVyYIOOwuWaQ4F_uSK3FB9D4,45828
transformers/models/owlv2/__init__.py,sha256=fvzKBoWfoB8-9hZKeId1Qvy3p_N9PLgsGoXzrg-fBzI,2606
transformers/models/owlv2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/owlv2/__pycache__/configuration_owlv2.cpython-310.pyc,,
transformers/models/owlv2/__pycache__/convert_owlv2_to_hf.cpython-310.pyc,,
transformers/models/owlv2/__pycache__/image_processing_owlv2.cpython-310.pyc,,
transformers/models/owlv2/__pycache__/modeling_owlv2.cpython-310.pyc,,
transformers/models/owlv2/__pycache__/processing_owlv2.cpython-310.pyc,,
transformers/models/owlv2/configuration_owlv2.py,sha256=l65Zv-lmsPatGFTDv59V0YUcjxYoujTA3ANVTFslHB8,15625
transformers/models/owlv2/convert_owlv2_to_hf.py,sha256=rF02k9XWTswf4P4ZZ76ekB3be6pRsFJLtbuWaJpyx3Y,22018
transformers/models/owlv2/image_processing_owlv2.py,sha256=jfYqpkhyltdJca0ShbrJN_qWdO_1o2oNyicbV1mv11s,26182
transformers/models/owlv2/modeling_owlv2.py,sha256=DWllzj9DJ8Bna-IRh57ZYVdQtYvJ4ZMYf0GvheIk8YM,82685
transformers/models/owlv2/processing_owlv2.py,sha256=ZnTH6-bZkd94Opf9TDnRBjOs4K2gZr0n-_B9fPyrLls,10152
transformers/models/owlvit/__init__.py,sha256=zBsZnxDQ28eWv3rpN77KfHfIQPv4sIurjn-kNoykQyo,2915
transformers/models/owlvit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/configuration_owlvit.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/convert_owlvit_original_flax_to_hf.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/feature_extraction_owlvit.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/image_processing_owlvit.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/modeling_owlvit.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/processing_owlvit.cpython-310.pyc,,
transformers/models/owlvit/configuration_owlvit.py,sha256=azl7t6S1nfxlCs2PruOX7Czp8PPVQUt6QF2ZdZZSXbc,17044
transformers/models/owlvit/convert_owlvit_original_flax_to_hf.py,sha256=tofzNZcVROwfYoV7pV6u50Am3TFm-XmuJEAGwNvRT9o,13988
transformers/models/owlvit/feature_extraction_owlvit.py,sha256=yPO8FbUw3YabKbsV_ozKpIr6JixO9knVw1eMIHeiCtY,1186
transformers/models/owlvit/image_processing_owlvit.py,sha256=HMLRhvGwGvHND4gb3NlJIC66P-4h4E7fndUO4k7tngY,28246
transformers/models/owlvit/modeling_owlvit.py,sha256=ctCRD0ASyEpoiUIrFEkFvtXgGCsRh-Ikj4HBcy1QRpI,76305
transformers/models/owlvit/processing_owlvit.py,sha256=XoD3T1ioapw8w2JvjK-Ju-M4juPmt_Y4LTt2YZT_qFk,11148
transformers/models/patchtsmixer/__init__.py,sha256=z9KtbxxAyoNMB0DkWBDvpxmgfZMzx5B056p1nlLjhIE,2204
transformers/models/patchtsmixer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/patchtsmixer/__pycache__/configuration_patchtsmixer.cpython-310.pyc,,
transformers/models/patchtsmixer/__pycache__/modeling_patchtsmixer.cpython-310.pyc,,
transformers/models/patchtsmixer/configuration_patchtsmixer.py,sha256=ziE0j11vjU13vJzORte1ajRb9d-vQXWX35_rYHDYKDw,12693
transformers/models/patchtsmixer/modeling_patchtsmixer.py,sha256=OlTH7YL7mI7DH_04AlXOA3OzmZJFLa_lL6LP5cyKn7M,87974
transformers/models/patchtst/__init__.py,sha256=AyK9VUDx2iphFn8IMvgt49apReqE0VBTxrjDwE6fRhc,2071
transformers/models/patchtst/__pycache__/__init__.cpython-310.pyc,,
transformers/models/patchtst/__pycache__/configuration_patchtst.cpython-310.pyc,,
transformers/models/patchtst/__pycache__/modeling_patchtst.cpython-310.pyc,,
transformers/models/patchtst/configuration_patchtst.py,sha256=fxONhc176CjlhxtfrwdsVA53ysuC1O5dfOLecWY2Lq0,12711
transformers/models/patchtst/modeling_patchtst.py,sha256=BbejdN9TUT_cfamSPKuY024Bs3inIP2bhMjZFEgKV_8,91617
transformers/models/pegasus/__init__.py,sha256=SXHYeNzkJrHfERo9lhqyvu3S75BYDmqceiFfim50Y_g,4111
transformers/models/pegasus/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/configuration_pegasus.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/convert_pegasus_tf_to_pytorch.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/modeling_flax_pegasus.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/modeling_pegasus.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/modeling_tf_pegasus.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/tokenization_pegasus.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/tokenization_pegasus_fast.cpython-310.pyc,,
transformers/models/pegasus/configuration_pegasus.py,sha256=gvZbwQgiMQU1H7w7bioQBfX5Win6AgsKUMc5LnF-1Vo,7694
transformers/models/pegasus/convert_pegasus_tf_to_pytorch.py,sha256=9geJowNAukZc9FE2OEq0pXQi6ynw9k-2NFtlmISxpUg,5359
transformers/models/pegasus/modeling_flax_pegasus.py,sha256=WvEWsMl_27ci7797WYwWAZj8lcz2TOFs0QhjnXgDHLw,65972
transformers/models/pegasus/modeling_pegasus.py,sha256=EhO18EXSYgV4EoGbcYGcg919pz-DhMqlmzHaga5l1O4,80707
transformers/models/pegasus/modeling_tf_pegasus.py,sha256=LDR3hvhl2tcmrUS251YrEGmGQYVH0YeEDys0NEr5gN0,69379
transformers/models/pegasus/tokenization_pegasus.py,sha256=Qg4F0h6IYPL7Kj-a-HSacSXVU7PZ23Z2nRwvAQQvP14,13478
transformers/models/pegasus/tokenization_pegasus_fast.py,sha256=4ZQe68xXmdB3egUhYPapeyYUPt8u6JFY56TYirWvcr0,10431
transformers/models/pegasus_x/__init__.py,sha256=M7Ef6UH-lQ53z-17c-XQi5nmmi-uVz8UKFHQe71LDVU,1828
transformers/models/pegasus_x/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pegasus_x/__pycache__/configuration_pegasus_x.cpython-310.pyc,,
transformers/models/pegasus_x/__pycache__/modeling_pegasus_x.cpython-310.pyc,,
transformers/models/pegasus_x/configuration_pegasus_x.py,sha256=GP_98_7nhkDnBSGwt6pvPa1MIxTJVkvEM38ijrb6J_I,8420
transformers/models/pegasus_x/modeling_pegasus_x.py,sha256=bgGHSOKGZn4V3FCH6T5EuyaT3612Vs0ueeVhFK_gaGM,75790
transformers/models/perceiver/__init__.py,sha256=y-6ZMYh3FfGpj9A1gZafPXrfGKJoGKEenKlJT9ZZEw8,3293
transformers/models/perceiver/__pycache__/__init__.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/configuration_perceiver.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/convert_perceiver_haiku_to_pytorch.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/feature_extraction_perceiver.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/image_processing_perceiver.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/modeling_perceiver.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/tokenization_perceiver.cpython-310.pyc,,
transformers/models/perceiver/configuration_perceiver.py,sha256=DvhPj8Pcm-kkMJh56FIWraYKhqxtuan2pjnV0WJZLCI,12397
transformers/models/perceiver/convert_perceiver_haiku_to_pytorch.py,sha256=f8p0sPVQv19tMDKkIM8IfTg60-SYX9MMABAzstxFt7k,21286
transformers/models/perceiver/feature_extraction_perceiver.py,sha256=0lW_qh5ONtUwr0ARM9RB9hizA76wL6fmeofDrhbIsXI,1207
transformers/models/perceiver/image_processing_perceiver.py,sha256=ESDnil80L5JYyIcZHo8pyNCO4RkWD8_4LXiiqzYJc0U,17555
transformers/models/perceiver/modeling_perceiver.py,sha256=NzcUFXVQ8Sefez_ilxGk300GovMNhfPly_4Cx7fT6-o,146639
transformers/models/perceiver/tokenization_perceiver.py,sha256=VOWp64riIrTTB7oqvLBq7N6_U515ZWzaaVpwSx7SncI,8020
transformers/models/persimmon/__init__.py,sha256=gp5VkpnXik0R_PBRitY6UBMcBDMmL41N8o1LjPW_Hmo,1835
transformers/models/persimmon/__pycache__/__init__.cpython-310.pyc,,
transformers/models/persimmon/__pycache__/configuration_persimmon.cpython-310.pyc,,
transformers/models/persimmon/__pycache__/convert_persimmon_weights_to_hf.cpython-310.pyc,,
transformers/models/persimmon/__pycache__/modeling_persimmon.cpython-310.pyc,,
transformers/models/persimmon/configuration_persimmon.py,sha256=E5mU1_NWby45NOrH3CV8HyfveoSF2vsPMlref437Eas,7839
transformers/models/persimmon/convert_persimmon_weights_to_hf.py,sha256=F3NFcbCWD-UxFwgp2h-Nv78_M0p0LELPq4re30ZNIjU,4644
transformers/models/persimmon/modeling_persimmon.py,sha256=onD7dKjceGPC-A5LukIQfuZX9lPJaVjQ-F6XgANO2EA,46905
transformers/models/phi/__init__.py,sha256=cSkf7i5ur4JTXt8gWalgbD-HFoJeFjMVTH3u5IOfICE,1971
transformers/models/phi/__pycache__/__init__.cpython-310.pyc,,
transformers/models/phi/__pycache__/configuration_phi.cpython-310.pyc,,
transformers/models/phi/__pycache__/convert_phi_weights_to_hf.cpython-310.pyc,,
transformers/models/phi/__pycache__/modeling_phi.cpython-310.pyc,,
transformers/models/phi/configuration_phi.py,sha256=IeMwglMDFx__KhMUp6k2sjozmZbMiMX5yEhpbG_cR4o,8431
transformers/models/phi/convert_phi_weights_to_hf.py,sha256=TCMsPsA4j4-uvR93H8FAV2zH_RnKHR8WWwAkEQ4iXZw,6414
transformers/models/phi/modeling_phi.py,sha256=MOkABaYj3h5yVFG9mBr1XzQS6QKo9Gem1nvm6JPAzK8,61868
transformers/models/phobert/__init__.py,sha256=JDAAoG6FOpN1o5kgFBbHkoko9NsiioFi-ZAeAgR79nY,955
transformers/models/phobert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/phobert/__pycache__/tokenization_phobert.cpython-310.pyc,,
transformers/models/phobert/tokenization_phobert.py,sha256=5r7Kdf6SFcokEh_WHudOClN5XV41BxW3qVRsY5jxXrM,13814
transformers/models/pix2struct/__init__.py,sha256=VSpzQStsFkcbIF3aftcNle95WQ7-cZzuWwDhjgzK-IU,2701
transformers/models/pix2struct/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pix2struct/__pycache__/configuration_pix2struct.cpython-310.pyc,,
transformers/models/pix2struct/__pycache__/convert_pix2struct_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/pix2struct/__pycache__/image_processing_pix2struct.cpython-310.pyc,,
transformers/models/pix2struct/__pycache__/modeling_pix2struct.cpython-310.pyc,,
transformers/models/pix2struct/__pycache__/processing_pix2struct.cpython-310.pyc,,
transformers/models/pix2struct/configuration_pix2struct.py,sha256=f9p5Gyz0oTvBcMXFuyKQSTE0w007Rx49d-whDAgG0a4,17478
transformers/models/pix2struct/convert_pix2struct_original_pytorch_to_hf.py,sha256=m_S-9oxyN4PQafRbWQIP-G0NUDrTqxOmr8IwiHNCOuU,5886
transformers/models/pix2struct/image_processing_pix2struct.py,sha256=PdEmX2g3Wk8-0nQI1SLMC4rk5bElvOiGylmSmFjnleM,20217
transformers/models/pix2struct/modeling_pix2struct.py,sha256=SJ4-vjEr466I9cktoqTxZq4-8SP3WJg98fOTLAmxKPM,83468
transformers/models/pix2struct/processing_pix2struct.py,sha256=YFwg3KSy0SKXAkBucCTOwsOFSm7pFYj-M6bCViLYVqU,6960
transformers/models/plbart/__init__.py,sha256=uNjyVJsOGh5eb2iNYSc7av9uNk-n3xB6rLv3BSRBKoY,2429
transformers/models/plbart/__pycache__/__init__.cpython-310.pyc,,
transformers/models/plbart/__pycache__/configuration_plbart.cpython-310.pyc,,
transformers/models/plbart/__pycache__/convert_plbart_original_checkpoint_to_torch.cpython-310.pyc,,
transformers/models/plbart/__pycache__/modeling_plbart.cpython-310.pyc,,
transformers/models/plbart/__pycache__/tokenization_plbart.cpython-310.pyc,,
transformers/models/plbart/configuration_plbart.py,sha256=uBJEf1G2ZSxJGig6nucB7_Jh-ODQH-RSEDwCYVayLZ0,8720
transformers/models/plbart/convert_plbart_original_checkpoint_to_torch.py,sha256=BOXNudNSr1xevmHnvNpa_4ya3Q89m6J4lndQhCWSLB8,3553
transformers/models/plbart/modeling_plbart.py,sha256=azRPGaD4gUKZRTWmNMlWRDTYMGH9c5ufcStwAkJQPKo,84509
transformers/models/plbart/tokenization_plbart.py,sha256=TFYonpfEv-HaRgPs8C1su_oRxN40Myv43LhU0evsBDk,21642
transformers/models/poolformer/__init__.py,sha256=fzMbnIpAxBApWl0QVCU965q9km5dySep9Hjhml26r68,2586
transformers/models/poolformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/poolformer/__pycache__/configuration_poolformer.cpython-310.pyc,,
transformers/models/poolformer/__pycache__/convert_poolformer_original_to_pytorch.cpython-310.pyc,,
transformers/models/poolformer/__pycache__/feature_extraction_poolformer.cpython-310.pyc,,
transformers/models/poolformer/__pycache__/image_processing_poolformer.cpython-310.pyc,,
transformers/models/poolformer/__pycache__/modeling_poolformer.cpython-310.pyc,,
transformers/models/poolformer/configuration_poolformer.py,sha256=zNmWnPCw24xDlZn-wkUWbOyxjSnwYQviNDrF8VM2-A4,5804
transformers/models/poolformer/convert_poolformer_original_to_pytorch.py,sha256=Vvlp7ju7kr2sg1NdXKma6vYGABjs4sVhPKhgFKPJRpk,7947
transformers/models/poolformer/feature_extraction_poolformer.py,sha256=KDL4tg7hxwzQKYmGc6jMZfzeD9UCTb00oNfbejIjzmk,1214
transformers/models/poolformer/image_processing_poolformer.py,sha256=fvzRA2TxmbAhsqOLA1pN0w-DHKFwOZBRlAujDedP2eY,17913
transformers/models/poolformer/modeling_poolformer.py,sha256=b9nhiafn6_45Fbn2yeSygDZ9HfY8Uhs84yuzXXzuWrU,17896
transformers/models/pop2piano/__init__.py,sha256=wxMmbwwAuqcGF8MimtfwAf4JPJ5D8x8up-q4yRlwU5E,3819
transformers/models/pop2piano/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/configuration_pop2piano.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/convert_pop2piano_weights_to_hf.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/feature_extraction_pop2piano.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/modeling_pop2piano.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/processing_pop2piano.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/tokenization_pop2piano.cpython-310.pyc,,
transformers/models/pop2piano/configuration_pop2piano.py,sha256=dWudee4erbp44euxJE_Kje1DktlBibfanEmqMlUOKI8,6072
transformers/models/pop2piano/convert_pop2piano_weights_to_hf.py,sha256=eZuC9RFueLoOmsaGWMa-6hNQyLBLTg9WXlRQRuiQerA,8626
transformers/models/pop2piano/feature_extraction_pop2piano.py,sha256=SBNQB6aol_Uan2p_z33IQue9y4exatqd80XyzHGBoqY,19839
transformers/models/pop2piano/modeling_pop2piano.py,sha256=5wMlhrpjO_6iqHOcKDtfmU4GVgYVbzUhmq87zNURA_Q,65812
transformers/models/pop2piano/processing_pop2piano.py,sha256=ytBqku-v0wCqeK4_JVd-0SNCI7jmYltMb5wDzagn6V4,5525
transformers/models/pop2piano/tokenization_pop2piano.py,sha256=kAGnOroIWUsaPcVYHLeQ4hPneWO2RZbbeExgyEDi8SQ,32086
transformers/models/prophetnet/__init__.py,sha256=1w4cY9QLl0elN9_oFDScwrb0F12-54b5ylPrxCiqpFw,2157
transformers/models/prophetnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/prophetnet/__pycache__/configuration_prophetnet.cpython-310.pyc,,
transformers/models/prophetnet/__pycache__/convert_prophetnet_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/prophetnet/__pycache__/modeling_prophetnet.cpython-310.pyc,,
transformers/models/prophetnet/__pycache__/tokenization_prophetnet.cpython-310.pyc,,
transformers/models/prophetnet/configuration_prophetnet.py,sha256=eyjxg-fI6no0GwvYskI_INPmIkh5cf1Z-acGYLWfcOU,9063
transformers/models/prophetnet/convert_prophetnet_original_pytorch_checkpoint_to_pytorch.py,sha256=EzgNdUzWNQowTUpyfXO-_RBZEw0sa5sVA1b7jbqFUxU,7055
transformers/models/prophetnet/modeling_prophetnet.py,sha256=PJcAHpsAq7ijwuD-moXBQ8VScmBCaM2K3VyrCDkkXps,115505
transformers/models/prophetnet/tokenization_prophetnet.py,sha256=NMz1ByHT5IO0wa1cRLPK21bOBmOaR-MNMggpGpgV6D4,21489
transformers/models/pvt/__init__.py,sha256=FxRer-Bn0NI00eTjXYOlUzVNJMH50lB78JEWPk1BNuw,2384
transformers/models/pvt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pvt/__pycache__/configuration_pvt.cpython-310.pyc,,
transformers/models/pvt/__pycache__/convert_pvt_to_pytorch.cpython-310.pyc,,
transformers/models/pvt/__pycache__/image_processing_pvt.cpython-310.pyc,,
transformers/models/pvt/__pycache__/modeling_pvt.cpython-310.pyc,,
transformers/models/pvt/configuration_pvt.py,sha256=zQZXZmu9qvMjymLUn7n89gF1Usb3Q6WASGkZvE-9SgM,7098
transformers/models/pvt/convert_pvt_to_pytorch.py,sha256=1DIHp33moj_2LrWws9x02AZ9qRrVMCQ3jifvV3SxmFc,9738
transformers/models/pvt/image_processing_pvt.py,sha256=RR1-BOZYAPirzZwPAbN2px3tb3QBiq0pMf1dSXzARTI,13676
transformers/models/pvt/modeling_pvt.py,sha256=CE1Ro4flzxshzKCnw-w2xy2EoN1cqqNGHhmM80b9PHk,28393
transformers/models/qdqbert/__init__.py,sha256=x3xI7kd5kpsjAvYJT8SrR5_uCeInhVA8repNZFRtXhU,2402
transformers/models/qdqbert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/qdqbert/__pycache__/configuration_qdqbert.cpython-310.pyc,,
transformers/models/qdqbert/__pycache__/modeling_qdqbert.cpython-310.pyc,,
transformers/models/qdqbert/configuration_qdqbert.py,sha256=osDmJcZU_y0wPVtMe5e6P-fOIKijJnbVrZf6MR2CJ5I,5896
transformers/models/qdqbert/modeling_qdqbert.py,sha256=MVycr3iTycQo3MHyvDOiWVi3KSfZb1KW_WWdgL_I_V8,77255
transformers/models/rag/__init__.py,sha256=omMwtpcTWBHYKZvt8NIxbACHhICmYWfeTgiC7O4U88g,2426
transformers/models/rag/__pycache__/__init__.cpython-310.pyc,,
transformers/models/rag/__pycache__/configuration_rag.cpython-310.pyc,,
transformers/models/rag/__pycache__/modeling_rag.cpython-310.pyc,,
transformers/models/rag/__pycache__/modeling_tf_rag.cpython-310.pyc,,
transformers/models/rag/__pycache__/retrieval_rag.cpython-310.pyc,,
transformers/models/rag/__pycache__/tokenization_rag.cpython-310.pyc,,
transformers/models/rag/configuration_rag.py,sha256=R29aZiFq0Aykstyd0nBhz6AfpoUJdw14cn2Bf7wUigw,8259
transformers/models/rag/modeling_rag.py,sha256=1K9Hy9QA7OyPMuldyDue379TW5qubm7WWOS9rRk0oSE,86129
transformers/models/rag/modeling_tf_rag.py,sha256=l99HdN35E0EGgyHHgQV3QqDXjlsN5rpqgKDmcsnaTm8,87928
transformers/models/rag/retrieval_rag.py,sha256=SRMr021qyYg27cLpuAXnaLPJuGaDiuLjFfwPwg9IEx0,29661
transformers/models/rag/tokenization_rag.py,sha256=O5gPSIP0dOyYEe5k4VjcCttsbAoAAZ6338z0IsWF690,4576
transformers/models/realm/__init__.py,sha256=k3gccDAsk5YJYrjrd8hOZCc1q8KJR2EMoGhvEdF-OTU,2675
transformers/models/realm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/realm/__pycache__/configuration_realm.cpython-310.pyc,,
transformers/models/realm/__pycache__/modeling_realm.cpython-310.pyc,,
transformers/models/realm/__pycache__/retrieval_realm.cpython-310.pyc,,
transformers/models/realm/__pycache__/tokenization_realm.cpython-310.pyc,,
transformers/models/realm/__pycache__/tokenization_realm_fast.cpython-310.pyc,,
transformers/models/realm/configuration_realm.py,sha256=aBJUxn_baZjSYEEJR_Vo5Kiy_PRGbiq54mzPP8tueZw,8744
transformers/models/realm/modeling_realm.py,sha256=taematzWMSOF1ckhKBnVsdk0dDvE_oKKLzA748xw-nU,84408
transformers/models/realm/retrieval_realm.py,sha256=86jQyu1U8QePlahXS8rGD_E6TlvEqQeqg21qSsAno-M,6370
transformers/models/realm/tokenization_realm.py,sha256=qVlgAB0Kur0xq5qKdPZldhICqsf1z8CZMhq817DOW-0,25476
transformers/models/realm/tokenization_realm_fast.py,sha256=8HRiBJaYGHz9zyYgQkkcCbi-ATUoHYediIuh77lLRpE,14587
transformers/models/reformer/__init__.py,sha256=MKhG4aefK429UY32oYQbVTLm1T2L_SIYS_TNnrWnTwA,3139
transformers/models/reformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/reformer/__pycache__/configuration_reformer.cpython-310.pyc,,
transformers/models/reformer/__pycache__/convert_reformer_trax_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/reformer/__pycache__/modeling_reformer.cpython-310.pyc,,
transformers/models/reformer/__pycache__/tokenization_reformer.cpython-310.pyc,,
transformers/models/reformer/__pycache__/tokenization_reformer_fast.cpython-310.pyc,,
transformers/models/reformer/configuration_reformer.py,sha256=62h-3phwg2n_GgPBJ-xTN4Gv3Y1uKEoLP8N6jfFnDoo,13464
transformers/models/reformer/convert_reformer_trax_checkpoint_to_pytorch.py,sha256=axn3FvdtVSdQT5V5u1-sfJ3sMV3YpEU6r5B10bTYZ8o,7818
transformers/models/reformer/modeling_reformer.py,sha256=E7jTEQaVf6HsKFfuvJocuK6n7RyZvskamByHG0eYa-g,115311
transformers/models/reformer/tokenization_reformer.py,sha256=fIMEtwgoDeoLRCG8_exbgMVMu4ev7fysHT_LBcDYE0w,7173
transformers/models/reformer/tokenization_reformer_fast.py,sha256=pLzMRju8y7WktPg9Q1M1VGqZUuyTxWBwlJbZfV-yoQU,4886
transformers/models/regnet/__init__.py,sha256=KQR1LgyjMxE0d-7nACPCHiRXo0rSm93vfcy8puDXbuE,3168
transformers/models/regnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/regnet/__pycache__/configuration_regnet.cpython-310.pyc,,
transformers/models/regnet/__pycache__/convert_regnet_seer_10b_to_pytorch.cpython-310.pyc,,
transformers/models/regnet/__pycache__/convert_regnet_to_pytorch.cpython-310.pyc,,
transformers/models/regnet/__pycache__/modeling_flax_regnet.cpython-310.pyc,,
transformers/models/regnet/__pycache__/modeling_regnet.cpython-310.pyc,,
transformers/models/regnet/__pycache__/modeling_tf_regnet.cpython-310.pyc,,
transformers/models/regnet/configuration_regnet.py,sha256=6WuMP1n1I67kdqfhnNPzb4LvxWJ9Kt2S1nZH-Hkrn6g,4089
transformers/models/regnet/convert_regnet_seer_10b_to_pytorch.py,sha256=zDPbUZRiO0lJl7hdUztm9JnUAbOI1Wv5wyHZdCKQ-d0,11770
transformers/models/regnet/convert_regnet_to_pytorch.py,sha256=lvSaB1ny0EKvS4KfhTpbNjdrYI6xE1zmYctM_O_a_Ak,18719
transformers/models/regnet/modeling_flax_regnet.py,sha256=2Ao7eODWcHufpZoNbGC4FbX6tZVE2bfWWrZSMbPGcMg,28410
transformers/models/regnet/modeling_regnet.py,sha256=4hRlcqDZeDDAZxzNR-W45WVmk-YBtYm0VoyrJ1JAFbg,17331
transformers/models/regnet/modeling_tf_regnet.py,sha256=lWBQPaZpQ63z41fKaWdTAnnCHnhO4YQxHu2tT3tTnFs,19535
transformers/models/rembert/__init__.py,sha256=XC3xr6aUNReL6SzFXr6TyAWPg9EXiBFl4o225gmkNQQ,4514
transformers/models/rembert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/rembert/__pycache__/configuration_rembert.cpython-310.pyc,,
transformers/models/rembert/__pycache__/convert_rembert_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/rembert/__pycache__/modeling_rembert.cpython-310.pyc,,
transformers/models/rembert/__pycache__/modeling_tf_rembert.cpython-310.pyc,,
transformers/models/rembert/__pycache__/tokenization_rembert.cpython-310.pyc,,
transformers/models/rembert/__pycache__/tokenization_rembert_fast.cpython-310.pyc,,
transformers/models/rembert/configuration_rembert.py,sha256=XvkNmkNMB7T7OVT_O9qVxbAxPKu0ekHy78HfCaoSc0k,7451
transformers/models/rembert/convert_rembert_tf_checkpoint_to_pytorch.py,sha256=C-TS1MrtQHTxK3j5HUKwlcrQItW24T7_iPvtt8KGbAU,2208
transformers/models/rembert/modeling_rembert.py,sha256=wCy5nEYGeOjGLbup1ODDMfOvX-g2RrsMXBk77VpSFIU,68287
transformers/models/rembert/modeling_tf_rembert.py,sha256=eoad29TxIVHyfwhPAVCLOq0QHj31uvHoSFdBtsm5R70,69600
transformers/models/rembert/tokenization_rembert.py,sha256=lJ0gRAsl75DaTrE0LzvfYmSB2Tu6XcBxr2YtgID4WVE,10954
transformers/models/rembert/tokenization_rembert_fast.py,sha256=RHLJO3J8up1dbVs977BQNBrynuQ7RtWnfDJux1quZzE,10483
transformers/models/resnet/__init__.py,sha256=n63hjzrOOmaIXaDS0F9thB531jarpWDBkXmgFaMBRbo,3216
transformers/models/resnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/resnet/__pycache__/configuration_resnet.cpython-310.pyc,,
transformers/models/resnet/__pycache__/convert_resnet_to_pytorch.cpython-310.pyc,,
transformers/models/resnet/__pycache__/modeling_flax_resnet.cpython-310.pyc,,
transformers/models/resnet/__pycache__/modeling_resnet.cpython-310.pyc,,
transformers/models/resnet/__pycache__/modeling_tf_resnet.cpython-310.pyc,,
transformers/models/resnet/configuration_resnet.py,sha256=mCokvBbXTcg3VPzq5z2JnMfpfqaDawce7XlCswkCZUM,5996
transformers/models/resnet/convert_resnet_to_pytorch.py,sha256=ShZl8Ob5ElrgRujQCoGXWdIY_99UICrWqiHdSzFdOHc,7287
transformers/models/resnet/modeling_flax_resnet.py,sha256=uJMz2FgVXm6ffwjiorCHkuPbCRra8VdN1vYILRuIgxY,24607
transformers/models/resnet/modeling_resnet.py,sha256=BHkhHk1gFdqMUfPKU0vLlIWpVK_G0O2JxrXgTj39bq8,19410
transformers/models/resnet/modeling_tf_resnet.py,sha256=NqqqxAyKh57TNCIDRIKyKKRn_bktYjMGwHCvP4r2qus,19121
transformers/models/roberta/__init__.py,sha256=GvGX0z6XPZtwkfCh4K2xagGOK0tlW0DT91QVQhTcA4o,5091
transformers/models/roberta/__pycache__/__init__.cpython-310.pyc,,
transformers/models/roberta/__pycache__/configuration_roberta.cpython-310.pyc,,
transformers/models/roberta/__pycache__/convert_roberta_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/roberta/__pycache__/modeling_flax_roberta.cpython-310.pyc,,
transformers/models/roberta/__pycache__/modeling_roberta.cpython-310.pyc,,
transformers/models/roberta/__pycache__/modeling_tf_roberta.cpython-310.pyc,,
transformers/models/roberta/__pycache__/tokenization_roberta.cpython-310.pyc,,
transformers/models/roberta/__pycache__/tokenization_roberta_fast.cpython-310.pyc,,
transformers/models/roberta/configuration_roberta.py,sha256=1enqLiOhGFEAw2e5kersyNhFaG0TJWV_P9EbjaBCsVk,7879
transformers/models/roberta/convert_roberta_original_pytorch_checkpoint_to_pytorch.py,sha256=MmHtq9AhcXXd-V8Fz0XWC8n-PL-S1MSdFhTCVM6Cksk,8002
transformers/models/roberta/modeling_flax_roberta.py,sha256=fFP2YeJlTg_v3iCQZB-u5yYw-bKQCRyX1tYZYNEyKnQ,56960
transformers/models/roberta/modeling_roberta.py,sha256=sn4TJ1fAuTHonvCTv1t0AAWYPuq6-h42flPcvsMMJJY,71333
transformers/models/roberta/modeling_tf_roberta.py,sha256=_G62Wav4BDcHY4SVT9iQ3HKidlt_b1AQMlKXYRXMRgw,71893
transformers/models/roberta/tokenization_roberta.py,sha256=lZk369TfJyiG2NicdbxZgmLsAM1lgFkgaW1N86y-vVg,18174
transformers/models/roberta/tokenization_roberta_fast.py,sha256=tMc0zZ-7O0tULkCcoL7LS8nMKOALBt8SgJruptA9CBg,13862
transformers/models/roberta_prelayernorm/__init__.py,sha256=C9bA_ah_10OCt_LUT1bsOJTUjSt6boV2frOKBtHCes4,5391
transformers/models/roberta_prelayernorm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/configuration_roberta_prelayernorm.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/convert_roberta_prelayernorm_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_flax_roberta_prelayernorm.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_roberta_prelayernorm.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_tf_roberta_prelayernorm.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/configuration_roberta_prelayernorm.py,sha256=FzbTxix-VTfBMB4hnv3H9UYIoIscydTgXi24fog4Nv4,7997
transformers/models/roberta_prelayernorm/convert_roberta_prelayernorm_original_pytorch_checkpoint_to_pytorch.py,sha256=ti9rttSVMs3SemlZrVQFkDKKHBubrk29d4lQkpkI3Ro,2975
transformers/models/roberta_prelayernorm/modeling_flax_roberta_prelayernorm.py,sha256=1fQI7oIBnWpBtd1-5qWEV9ecuxkeN3AFJGl_B7m7fqM,60532
transformers/models/roberta_prelayernorm/modeling_roberta_prelayernorm.py,sha256=4uDN8bKHuRMd7fPgATBkj4Stmrzzg9X__b_ghzpd5Xg,74163
transformers/models/roberta_prelayernorm/modeling_tf_roberta_prelayernorm.py,sha256=kJYM-NlKKtSWEB9aK22XHRRulZPo6JwDrdRRlgXLyWQ,74866
transformers/models/roc_bert/__init__.py,sha256=ItDlyJx76hWJLT_159wnQgdWC82bT-TG_FpFzjRqXaU,2875
transformers/models/roc_bert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/roc_bert/__pycache__/configuration_roc_bert.cpython-310.pyc,,
transformers/models/roc_bert/__pycache__/modeling_roc_bert.cpython-310.pyc,,
transformers/models/roc_bert/__pycache__/tokenization_roc_bert.cpython-310.pyc,,
transformers/models/roc_bert/configuration_roc_bert.py,sha256=p_G77fOA04_x2XwC4tOf4iVmqIwjJ30uYi5WQHnNEEo,8658
transformers/models/roc_bert/modeling_roc_bert.py,sha256=fVFfM19EKTF9Z85qioSyDrf2ezNHE1uZGfnY4E9EdyY,93052
transformers/models/roc_bert/tokenization_roc_bert.py,sha256=j8h8or4nC3Btd4QGjRcYmTHJ2LgX9ZM9cPhhKGUryw0,51087
transformers/models/roformer/__init__.py,sha256=1EFy2Zdn9AdraO-fmIpYg1q_HLYq-7rT5qDL_8Gurnc,5333
transformers/models/roformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/roformer/__pycache__/configuration_roformer.cpython-310.pyc,,
transformers/models/roformer/__pycache__/convert_roformer_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/roformer/__pycache__/modeling_flax_roformer.cpython-310.pyc,,
transformers/models/roformer/__pycache__/modeling_roformer.cpython-310.pyc,,
transformers/models/roformer/__pycache__/modeling_tf_roformer.cpython-310.pyc,,
transformers/models/roformer/__pycache__/tokenization_roformer.cpython-310.pyc,,
transformers/models/roformer/__pycache__/tokenization_roformer_fast.cpython-310.pyc,,
transformers/models/roformer/__pycache__/tokenization_utils.cpython-310.pyc,,
transformers/models/roformer/configuration_roformer.py,sha256=-IJsjs6MKDEz1r6U25qEH55CASp9udFXl69E-RSXm_A,7734
transformers/models/roformer/convert_roformer_original_tf_checkpoint_to_pytorch.py,sha256=G57qbbWpRH06sm041u6D3BdNE7mCPSDvlaNLOZjWdvY,2240
transformers/models/roformer/modeling_flax_roformer.py,sha256=9kBP35oCuJteX63gvk1HnEgGW7NcxmDHm_HRbEYm3xU,39468
transformers/models/roformer/modeling_roformer.py,sha256=LshlWtUk9Cdr3OhdDSnVvISYRKFu3BoXOmBTF_IqlHQ,69483
transformers/models/roformer/modeling_tf_roformer.py,sha256=VUPezczZMCSmyAbP5UL0LfGk-xqvoIwDUHauDS0fiKw,57617
transformers/models/roformer/tokenization_roformer.py,sha256=0NcTJp0JT1uupyHd2d1mQ5nIweekqZX1GqrgNbVN0SA,23836
transformers/models/roformer/tokenization_roformer_fast.py,sha256=7YgwlaZNsx9u8JJW-cJGBzUeuZKjGQSRZ88887xk7FY,8288
transformers/models/roformer/tokenization_utils.py,sha256=0ciH13qW2kCa5my1rPwfwAuSXX-jGzN0nzemvGvOBxw,2652
transformers/models/rwkv/__init__.py,sha256=2uUo3Zi2By-3QKG7YkrEqllvFG4_SqJZ-NeplOxHCD4,1780
transformers/models/rwkv/__pycache__/__init__.cpython-310.pyc,,
transformers/models/rwkv/__pycache__/configuration_rwkv.cpython-310.pyc,,
transformers/models/rwkv/__pycache__/convert_rwkv_checkpoint_to_hf.cpython-310.pyc,,
transformers/models/rwkv/__pycache__/modeling_rwkv.cpython-310.pyc,,
transformers/models/rwkv/configuration_rwkv.py,sha256=Dgz84bpJyy9LOJX11yEeGxpgyphlNTwJk4fmWCZY4nU,6210
transformers/models/rwkv/convert_rwkv_checkpoint_to_hf.py,sha256=oXXZN2tt_yWCRAkqpE6-7kDPMy4PyKaYmpMZwsH-IUE,6994
transformers/models/rwkv/modeling_rwkv.py,sha256=mx4htr-HpEGiYdzEuZYRU0NblxIm3ez0ewDOYZsCtNE,37009
transformers/models/sam/__init__.py,sha256=1wiFtdU-_NON6yx4QfFBk4vrfwN4hHv7JEA3CSGq_wU,2980
transformers/models/sam/__pycache__/__init__.cpython-310.pyc,,
transformers/models/sam/__pycache__/configuration_sam.cpython-310.pyc,,
transformers/models/sam/__pycache__/convert_sam_original_to_hf_format.cpython-310.pyc,,
transformers/models/sam/__pycache__/image_processing_sam.cpython-310.pyc,,
transformers/models/sam/__pycache__/modeling_sam.cpython-310.pyc,,
transformers/models/sam/__pycache__/modeling_tf_sam.cpython-310.pyc,,
transformers/models/sam/__pycache__/processing_sam.cpython-310.pyc,,
transformers/models/sam/configuration_sam.py,sha256=_M7Yt6_9JyBx-F4LwmXr8cTH03-j5UDBU-J4XBg3zUU,14112
transformers/models/sam/convert_sam_original_to_hf_format.py,sha256=EPsY1Ne6lziXTjV7kun80W10En23hs5PSC10hiR-dSA,6958
transformers/models/sam/image_processing_sam.py,sha256=Fy_Cm8mpemCiQA4f6nFc6gxJTbhWuRLrt7QC_DPAN_E,59121
transformers/models/sam/modeling_sam.py,sha256=8O4GdKMlqWg9tjro5P-4mSNuG0kQqBS3mC-ObyTS-iQ,64838
transformers/models/sam/modeling_tf_sam.py,sha256=-T0KSiQFSEwNo-vwkOIRVXy4XHqbHvRI0C66EeZ61O8,66881
transformers/models/sam/processing_sam.py,sha256=qpw0s071hYDTx04ANuLluqWR1EjQYiSvPrCHYWJWco4,10849
transformers/models/seamless_m4t/__init__.py,sha256=PRZMtfk0WN3i0ZSvQbv8wgqp4dOREyIvkgzx5obqn7I,3706
transformers/models/seamless_m4t/__pycache__/__init__.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/configuration_seamless_m4t.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/convert_fairseq2_to_hf.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/feature_extraction_seamless_m4t.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/modeling_seamless_m4t.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/processing_seamless_m4t.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/tokenization_seamless_m4t.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/tokenization_seamless_m4t_fast.cpython-310.pyc,,
transformers/models/seamless_m4t/configuration_seamless_m4t.py,sha256=hOA1chGhtyChSWhKGDHNlL6NqoU6GFjEvTdlEgZA3YI,23723
transformers/models/seamless_m4t/convert_fairseq2_to_hf.py,sha256=F2AQrS9rfpktVBSXvFLmND9gMtASSEOMlYPQ6v8VDdU,15960
transformers/models/seamless_m4t/feature_extraction_seamless_m4t.py,sha256=XNJCz3idC58BEyLjOpYG5KVy6TfnGB6ynypkkyvhUMM,13013
transformers/models/seamless_m4t/modeling_seamless_m4t.py,sha256=IsM1uu3NzbxpempH2_kPeElEgksn_dXXEkY3gAXk-lg,201769
transformers/models/seamless_m4t/processing_seamless_m4t.py,sha256=OrPvDJkAAIuoWglyxt1Z4H993tm-AyX3OxDcu4Gmps0,5893
transformers/models/seamless_m4t/tokenization_seamless_m4t.py,sha256=f_gycl9YTRrRMtKsphTVQrjawK9g2oKhmyrxE1Jv0TI,25939
transformers/models/seamless_m4t/tokenization_seamless_m4t_fast.py,sha256=XS5nPEft96ZzLUyDZ7eMZ29jKmikPygeH9xUuoIP-iI,20447
transformers/models/seamless_m4t_v2/__init__.py,sha256=eIGJqmaWPYi--eaUhctnu8W9EIihWP-uJsOORWLKVxg,2159
transformers/models/seamless_m4t_v2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/configuration_seamless_m4t_v2.cpython-310.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/convert_fairseq2_to_hf.cpython-310.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/modeling_seamless_m4t_v2.cpython-310.pyc,,
transformers/models/seamless_m4t_v2/configuration_seamless_m4t_v2.py,sha256=mJBV6WJVOgDRbtDSGBmz9Z4AlMVTvPYpyVIT4VqgvpI,24436
transformers/models/seamless_m4t_v2/convert_fairseq2_to_hf.py,sha256=B3ChRBL4biKHRNsLhAKRsZ547XyxI1uwiywDUC6jKXo,15084
transformers/models/seamless_m4t_v2/modeling_seamless_m4t_v2.py,sha256=LyIbuowyyGbaEyos5LJhLDJgdx5cjwPyctQBctLi1Ec,228312
transformers/models/segformer/__init__.py,sha256=T1k_hhB2iCL8zOY3rcG9erX0JbBS--OgU27-G0ZxR2o,3676
transformers/models/segformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/segformer/__pycache__/configuration_segformer.cpython-310.pyc,,
transformers/models/segformer/__pycache__/convert_segformer_original_to_pytorch.cpython-310.pyc,,
transformers/models/segformer/__pycache__/feature_extraction_segformer.cpython-310.pyc,,
transformers/models/segformer/__pycache__/image_processing_segformer.cpython-310.pyc,,
transformers/models/segformer/__pycache__/modeling_segformer.cpython-310.pyc,,
transformers/models/segformer/__pycache__/modeling_tf_segformer.cpython-310.pyc,,
transformers/models/segformer/configuration_segformer.py,sha256=sUxxiCMIHR-liuMJa2UA4hoodvj0pA8LpF9MJ3Es1wo,7652
transformers/models/segformer/convert_segformer_original_to_pytorch.py,sha256=UXWvoxIi_vor0L5yPuqD7wUuy-vzSNtypQcrpLkTZFc,17092
transformers/models/segformer/feature_extraction_segformer.py,sha256=yaRckmbmTyh1Oow3PnHLsjW4MURaWqddhTzG-PVcywk,1207
transformers/models/segformer/image_processing_segformer.py,sha256=6avq1Tr3WFhciBzK7yYbfF4bEfqboTAFe7rcQkovx3U,23189
transformers/models/segformer/modeling_segformer.py,sha256=VDJhnBNnFyq7oOIZEhKETjfNi6cqfgkppjR6goEw_8k,35490
transformers/models/segformer/modeling_tf_segformer.py,sha256=lrGdJ7P4P9JazkR-pE4s7GK9ekU5qhaNUU-P5eSZwr8,36180
transformers/models/sew/__init__.py,sha256=VG7sYJFBweKB5Cb9lzyRYdjeG0olDM7cIQIUy4XQR8M,1778
transformers/models/sew/__pycache__/__init__.cpython-310.pyc,,
transformers/models/sew/__pycache__/configuration_sew.cpython-310.pyc,,
transformers/models/sew/__pycache__/convert_sew_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/sew/__pycache__/modeling_sew.cpython-310.pyc,,
transformers/models/sew/configuration_sew.py,sha256=RdBcWdW3_c1bT7Wq50RrX2FqrhEvB-wQzfm5vUt-uqw,14390
transformers/models/sew/convert_sew_original_pytorch_checkpoint_to_pytorch.py,sha256=TzlAoTl1DQUm3bhNxDlpXoxe-u1ZcMMbhrQsefGbFog,12745
transformers/models/sew/modeling_sew.py,sha256=Sf8Q_b6Qo-xO3z8rOfVSM5RlLVRjGUKRo98lJQdr_Q8,53440
transformers/models/sew_d/__init__.py,sha256=5d5VSrW-sTwr3H0e2js1KsRL7SM4GPiRPY9Hl_gVjWk,1804
transformers/models/sew_d/__pycache__/__init__.cpython-310.pyc,,
transformers/models/sew_d/__pycache__/configuration_sew_d.cpython-310.pyc,,
transformers/models/sew_d/__pycache__/convert_sew_d_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/sew_d/__pycache__/modeling_sew_d.cpython-310.pyc,,
transformers/models/sew_d/configuration_sew_d.py,sha256=8cvybhCAMW2dYMbZrZyOir64gnQxsnITTunk9SDqoIQ,16568
transformers/models/sew_d/convert_sew_d_original_pytorch_checkpoint_to_pytorch.py,sha256=OeszH3N5vz1FbXoF-d-w6wDJ2A2MxvUMn9uDMpU7bro,13575
transformers/models/sew_d/modeling_sew_d.py,sha256=dINQXBpNR1RIDlcJgaUCngDdGydpicBW6AJj1ssHeVc,74003
transformers/models/speech_encoder_decoder/__init__.py,sha256=987NzBteEbQy0IYY43B_JKolw2BbyX6Ox9s__xH0daQ,2037
transformers/models/speech_encoder_decoder/__pycache__/__init__.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/configuration_speech_encoder_decoder.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/convert_mbart_wav2vec2_seq2seq_original_to_pytorch.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/convert_speech_to_text_wav2vec2_seq2seq_original_to_pytorch.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/modeling_flax_speech_encoder_decoder.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/modeling_speech_encoder_decoder.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/configuration_speech_encoder_decoder.py,sha256=svkEWu1zya4I7pPw7OC1W3GR38t0YfpAzXTs6hov59E,4563
transformers/models/speech_encoder_decoder/convert_mbart_wav2vec2_seq2seq_original_to_pytorch.py,sha256=EtCwDPHsete4dhXGu8OwkbRx7-47vbHRKUrb8j-6M2c,14754
transformers/models/speech_encoder_decoder/convert_speech_to_text_wav2vec2_seq2seq_original_to_pytorch.py,sha256=04swyKsxEHHieCLUFPKzubV4W0ES1mZtbkgv-UDt7po,11971
transformers/models/speech_encoder_decoder/modeling_flax_speech_encoder_decoder.py,sha256=trQ5VuCqiEUpc_GyYiHD45jUZRpZCtXVAbjSz4PBFTo,45055
transformers/models/speech_encoder_decoder/modeling_speech_encoder_decoder.py,sha256=dMLHqPh4wjTjfZZZlClMWrYg9ckJZusyxjWECtfiHtk,32666
transformers/models/speech_to_text/__init__.py,sha256=y2bX48UezdcJd_0EyTBq6xLWHL0vup-noE235__AYw8,3491
transformers/models/speech_to_text/__pycache__/__init__.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/configuration_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/convert_s2t_fairseq_to_tfms.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/feature_extraction_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/modeling_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/modeling_tf_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/processing_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/tokenization_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/configuration_speech_to_text.py,sha256=xrvtRmjXjdFz8YK4wOAS1jbX3SClEQATFkR49qd6j8A,10060
transformers/models/speech_to_text/convert_s2t_fairseq_to_tfms.py,sha256=v-5aSPwuCKCtqwU8gREj9wA2nm14Z97tg6wQ3S47gos,4478
transformers/models/speech_to_text/feature_extraction_speech_to_text.py,sha256=bW4mXxoo1FKXFhfvstyPbWm8fMRMN1G7KXwkGN-vdxw,13176
transformers/models/speech_to_text/modeling_speech_to_text.py,sha256=DinZlI0n5ke15H8GQpfQUY-0dbX4I-Gl9s8Zug94-G0,64570
transformers/models/speech_to_text/modeling_tf_speech_to_text.py,sha256=tzwqwlAhSH7Y6iIaj6aCd88YtzSWiSwEX9s6Or8zsFk,68290
transformers/models/speech_to_text/processing_speech_to_text.py,sha256=dtDsYvPg-jn-O5iiVDPH5154wOEDglsODuF4dPn7XYc,4818
transformers/models/speech_to_text/tokenization_speech_to_text.py,sha256=xK37kRPN-deOCb_OpTPiyfLC701ykicTdkO-ndRhtbs,11917
transformers/models/speech_to_text_2/__init__.py,sha256=zkmS9-WZTXByVUJqkt094wHCOT4zyVLO4Rn3B0JBCSo,2166
transformers/models/speech_to_text_2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/speech_to_text_2/__pycache__/configuration_speech_to_text_2.cpython-310.pyc,,
transformers/models/speech_to_text_2/__pycache__/modeling_speech_to_text_2.cpython-310.pyc,,
transformers/models/speech_to_text_2/__pycache__/processing_speech_to_text_2.cpython-310.pyc,,
transformers/models/speech_to_text_2/__pycache__/tokenization_speech_to_text_2.cpython-310.pyc,,
transformers/models/speech_to_text_2/configuration_speech_to_text_2.py,sha256=Jkq7CP0ecvv-zqtIcXedbTQs6YgGpKgz5LWtcqZM1Mw,6282
transformers/models/speech_to_text_2/modeling_speech_to_text_2.py,sha256=wVYxKKdBbTmbbjONyzhpufeMov91hmi1g7eaYsoQam8,44284
transformers/models/speech_to_text_2/processing_speech_to_text_2.py,sha256=J3Uv4HX7Y5zndYa3ZIROcEuLEfrw2piJC53AZmSkGnY,4790
transformers/models/speech_to_text_2/tokenization_speech_to_text_2.py,sha256=VmKsOBo7vpQYoMystmIeLzBAT6yLtNuioOM0g0hR7Rc,9211
transformers/models/speecht5/__init__.py,sha256=rI6eMJ1n9U8Mtn17i83U2qOhvcOQJudmFYU9roGYUno,2971
transformers/models/speecht5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/configuration_speecht5.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/convert_hifigan.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/convert_speecht5_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/feature_extraction_speecht5.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/modeling_speecht5.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/number_normalizer.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/processing_speecht5.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/tokenization_speecht5.cpython-310.pyc,,
transformers/models/speecht5/configuration_speecht5.py,sha256=bLoe6LIlF1eD8vLKUGecwqcJrH3ZJqW8KfgjoqrpjJs,23901
transformers/models/speecht5/convert_hifigan.py,sha256=CL9GSX_bimjm_hU2rE55MaNvTUjTtWD6qCtqNMaXy7I,4241
transformers/models/speecht5/convert_speecht5_original_pytorch_checkpoint_to_pytorch.py,sha256=AyAjaeibe3002YZRT2maq1Yi8-iP1j7Ahs5qxYMjiJ0,17194
transformers/models/speecht5/feature_extraction_speecht5.py,sha256=lcKx3NaIXx0PGITRKP0kA8SZK75kd1Sn8PNHLBn-ST0,17809
transformers/models/speecht5/modeling_speecht5.py,sha256=hh1pkv_Hxar9bea76w42FcpTfmSsvx8IJL3AkkC8_eY,153124
transformers/models/speecht5/number_normalizer.py,sha256=cxnEUdHSISW5eAo15cLuVkZa65zMFuMFaJ8zAOQCsAA,7019
transformers/models/speecht5/processing_speecht5.py,sha256=smqFdqKJQp9Vm1FDfmj7EvJeAZKSPB6u2AZMfsjsQa0,7562
transformers/models/speecht5/tokenization_speecht5.py,sha256=v5xGpmI_4iuYDIRAYyonlvs0iG5_ac4BA5yU0rHfOIg,9289
transformers/models/splinter/__init__.py,sha256=vo990AmnOkGy7xWuzB4qaAfJNrtFFLOImR4mlSl_jJ8,2532
transformers/models/splinter/__pycache__/__init__.cpython-310.pyc,,
transformers/models/splinter/__pycache__/configuration_splinter.cpython-310.pyc,,
transformers/models/splinter/__pycache__/modeling_splinter.cpython-310.pyc,,
transformers/models/splinter/__pycache__/tokenization_splinter.cpython-310.pyc,,
transformers/models/splinter/__pycache__/tokenization_splinter_fast.cpython-310.pyc,,
transformers/models/splinter/configuration_splinter.py,sha256=r6pKzWM9-GW4BBxlhSO02oTwhjTEilot5zx7XoAQszU,6121
transformers/models/splinter/modeling_splinter.py,sha256=2aHtIIxMRCNAMTl3_mi88gQzJKMtN6XAe8uoHDKkkyg,53386
transformers/models/splinter/tokenization_splinter.py,sha256=qM9mEO_nF6YL_ZqTTR-8W_h9-Cxd6Oau-V1bG8KPU8k,22012
transformers/models/splinter/tokenization_splinter_fast.py,sha256=vlc_KIuy5sNJt2XrY8pavBIlkQaiGeAm46dJyEnpWOo,9657
transformers/models/squeezebert/__init__.py,sha256=G8bhLM5DmRO6oIXmZT-W71i8hZK9589XpyLuwIs6W3M,2996
transformers/models/squeezebert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/squeezebert/__pycache__/configuration_squeezebert.cpython-310.pyc,,
transformers/models/squeezebert/__pycache__/modeling_squeezebert.cpython-310.pyc,,
transformers/models/squeezebert/__pycache__/tokenization_squeezebert.cpython-310.pyc,,
transformers/models/squeezebert/__pycache__/tokenization_squeezebert_fast.cpython-310.pyc,,
transformers/models/squeezebert/configuration_squeezebert.py,sha256=CNa0VlJQKxKeYzp_gsqwdZtudk71DgXMmOCIl_IC-8w,7911
transformers/models/squeezebert/modeling_squeezebert.py,sha256=xaC-OqwERTEnmSMLf7xBer38w4mKlx0hNnaR4D26RB4,45093
transformers/models/squeezebert/tokenization_squeezebert.py,sha256=OfEklLH2QAUiJlypidV5_4zT49yfm4BhCtMTKkn7jgM,21986
transformers/models/squeezebert/tokenization_squeezebert_fast.py,sha256=-BZskQnhqHHvqpeRFKpyx3H3isZzh36YLJff2pTaGPg,9409
transformers/models/swiftformer/__init__.py,sha256=y3EVx2oOV5GldnIhqN1uK316Lf68wv3IsTE4HGd2DSc,1990
transformers/models/swiftformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/swiftformer/__pycache__/configuration_swiftformer.cpython-310.pyc,,
transformers/models/swiftformer/__pycache__/convert_swiftformer_original_to_hf.cpython-310.pyc,,
transformers/models/swiftformer/__pycache__/modeling_swiftformer.cpython-310.pyc,,
transformers/models/swiftformer/configuration_swiftformer.py,sha256=-zmyD74MBLjuePvzdP3jIBnXER5koT9E77CzVvkdY1U,5351
transformers/models/swiftformer/convert_swiftformer_original_to_hf.py,sha256=HsppMeVG__p-Z4sCLcGLnDhXP-AFe6ewWiifyEFL-xA,6239
transformers/models/swiftformer/modeling_swiftformer.py,sha256=ITQ6yNKAqLtXKoLPQHrcfKyvJSiw359D6HdDlLBxEFk,23150
transformers/models/swin/__init__.py,sha256=lsSSO-igADN2rI7RV55GBIB-GG8mRQNnsT9A6J8IFtk,2703
transformers/models/swin/__pycache__/__init__.cpython-310.pyc,,
transformers/models/swin/__pycache__/configuration_swin.cpython-310.pyc,,
transformers/models/swin/__pycache__/convert_swin_simmim_to_pytorch.cpython-310.pyc,,
transformers/models/swin/__pycache__/convert_swin_timm_to_pytorch.cpython-310.pyc,,
transformers/models/swin/__pycache__/modeling_swin.cpython-310.pyc,,
transformers/models/swin/__pycache__/modeling_tf_swin.cpython-310.pyc,,
transformers/models/swin/configuration_swin.py,sha256=I5tXGbSEoh5tZJCsUxs4vmXmbpBeIr-WRXqC-WpAaBM,8008
transformers/models/swin/convert_swin_simmim_to_pytorch.py,sha256=Zb67GMulOozvN1L66EmQ9gKtLVUmyaWYgq_zPPdbGKs,6627
transformers/models/swin/convert_swin_timm_to_pytorch.py,sha256=WKAiiEOxnv4_yjbLVsU9M50iwE_x0QEvbXrMZK1W_7Q,5805
transformers/models/swin/modeling_swin.py,sha256=PpRd137uSFDny_N7paSAChb8bS1lrSzbukfIehsyAlM,59974
transformers/models/swin/modeling_tf_swin.py,sha256=oxk_rkJSpJv58cwJSltUJWO3g5liXiv-R_UeIbEH9kw,63558
transformers/models/swin2sr/__init__.py,sha256=Nx5kG4ltMIhcqaGLYh7VYoju_qViNNYZGdGE0p-rz_4,2277
transformers/models/swin2sr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/swin2sr/__pycache__/configuration_swin2sr.cpython-310.pyc,,
transformers/models/swin2sr/__pycache__/convert_swin2sr_original_to_pytorch.cpython-310.pyc,,
transformers/models/swin2sr/__pycache__/image_processing_swin2sr.cpython-310.pyc,,
transformers/models/swin2sr/__pycache__/modeling_swin2sr.cpython-310.pyc,,
transformers/models/swin2sr/configuration_swin2sr.py,sha256=akg8KGYVj4-YUxCzT6ESaM2gUjd4AvqqwI7p4rdKFT8,6997
transformers/models/swin2sr/convert_swin2sr_original_to_pytorch.py,sha256=eZ1q75t9Na8iF_KkMXK9hHb0O0KyX9Bv1JhO3r94ZLA,11355
transformers/models/swin2sr/image_processing_swin2sr.py,sha256=PLZStsbTW7hJ_mQCkBF5Ol84YStknPYmvRZa_1Kdjxg,9014
transformers/models/swin2sr/modeling_swin2sr.py,sha256=Qo4AGyUn3MYw1PMuXIW4ImTo9YQT2BmXLl0Kk4vLZnA,51510
transformers/models/swinv2/__init__.py,sha256=MkReYZbR_fB81YEsKFmR5-6JKDlBlx4jjBO-UyBAbU8,1867
transformers/models/swinv2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/swinv2/__pycache__/configuration_swinv2.cpython-310.pyc,,
transformers/models/swinv2/__pycache__/convert_swinv2_timm_to_pytorch.cpython-310.pyc,,
transformers/models/swinv2/__pycache__/modeling_swinv2.cpython-310.pyc,,
transformers/models/swinv2/configuration_swinv2.py,sha256=DBDAb7jlyNMWDQUIFtXHyxMtGjGH-SKa-7NXQphM_do,6324
transformers/models/swinv2/convert_swinv2_timm_to_pytorch.py,sha256=OMyAAcVPs9DTojiHQCvLo7uTtaChsd1ANTY4IkS7iUY,7687
transformers/models/swinv2/modeling_swinv2.py,sha256=unaG3wYapOWgh718N2fr387hL6p_xFAj1zYvVDysdjM,61389
transformers/models/switch_transformers/__init__.py,sha256=71GlCMK0XfSUSoxmTxWjj-vmLJImHjlJjtUWkptdalA,2484
transformers/models/switch_transformers/__pycache__/__init__.cpython-310.pyc,,
transformers/models/switch_transformers/__pycache__/configuration_switch_transformers.cpython-310.pyc,,
transformers/models/switch_transformers/__pycache__/convert_big_switch.cpython-310.pyc,,
transformers/models/switch_transformers/__pycache__/convert_switch_transformers_original_flax_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/switch_transformers/__pycache__/modeling_switch_transformers.cpython-310.pyc,,
transformers/models/switch_transformers/configuration_switch_transformers.py,sha256=wBwVRlEG_E7TQhtBf62sK5e-0IL2uvpihJK2afv4rto,9334
transformers/models/switch_transformers/convert_big_switch.py,sha256=lvgd9M2Mv74Np22gU_rrN2XE5AoAewlDh7atmgAobTk,7649
transformers/models/switch_transformers/convert_switch_transformers_original_flax_checkpoint_to_pytorch.py,sha256=AAJNkPcr_THjPN_8RUnOdBYbbYc6GOqXdgdjhx9FZyw,7593
transformers/models/switch_transformers/modeling_switch_transformers.py,sha256=D4AWL4AAq0NbVm1CSJecKYbCwPO8Cz-iy_Ahh-N77xU,87934
transformers/models/t5/__init__.py,sha256=iS9B3xS2dRdQVEze6gT2Rx6xGLbFvp-UOfc4nOJ8cdc,4418
transformers/models/t5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/t5/__pycache__/configuration_t5.cpython-310.pyc,,
transformers/models/t5/__pycache__/convert_t5_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/t5/__pycache__/convert_t5x_checkpoint_to_flax.cpython-310.pyc,,
transformers/models/t5/__pycache__/convert_t5x_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/t5/__pycache__/modeling_flax_t5.cpython-310.pyc,,
transformers/models/t5/__pycache__/modeling_t5.cpython-310.pyc,,
transformers/models/t5/__pycache__/modeling_tf_t5.cpython-310.pyc,,
transformers/models/t5/__pycache__/tokenization_t5.cpython-310.pyc,,
transformers/models/t5/__pycache__/tokenization_t5_fast.cpython-310.pyc,,
transformers/models/t5/configuration_t5.py,sha256=Ld7d8mKF7wPRApLFFjwRx0EZv1Ap44bCWRqvm-RNwsQ,7660
transformers/models/t5/convert_t5_original_tf_checkpoint_to_pytorch.py,sha256=83tKCwYRSRW7zXtm9cmszqtPhpw44cH8Cj0SWUSBgN0,2120
transformers/models/t5/convert_t5x_checkpoint_to_flax.py,sha256=CET5s9wlNOt-VxT9eu-NOMdNS22kX6mhEZQ-ox2mLK0,10538
transformers/models/t5/convert_t5x_checkpoint_to_pytorch.py,sha256=GTF0FYHDDDBl2tcYgHcirqHOI2KOE2YkDG4ekzjh_Ao,10483
transformers/models/t5/modeling_flax_t5.py,sha256=thW5XBUbpZqbCwz3FyvWVUy30S4-gf-7BCbXx1ZCJeQ,74056
transformers/models/t5/modeling_t5.py,sha256=4RlhLmCmVm7anxlDZioIHn-FqrtJgIMJGvsuFRyvvRc,105603
transformers/models/t5/modeling_tf_t5.py,sha256=dVBRL_BeKrCWtWh2gdoMs3HKEWVYzQBcdxjCr46GF0A,71843
transformers/models/t5/tokenization_t5.py,sha256=Dulda3LHOVRqaiVTyHRF4gbfysSz46j89HcC-jQhjiw,20338
transformers/models/t5/tokenization_t5_fast.py,sha256=3-s19R_h5BrKa5TckUG1GR3ZnidJquBm2rUmmn19W4E,10680
transformers/models/table_transformer/__init__.py,sha256=WHdzgCB7BwXZeZveOSQ2fBQKNsrsRmpdP1f5C2MfYn4,2065
transformers/models/table_transformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/table_transformer/__pycache__/configuration_table_transformer.cpython-310.pyc,,
transformers/models/table_transformer/__pycache__/convert_table_transformer_to_hf.cpython-310.pyc,,
transformers/models/table_transformer/__pycache__/convert_table_transformer_to_hf_no_timm.cpython-310.pyc,,
transformers/models/table_transformer/__pycache__/modeling_table_transformer.cpython-310.pyc,,
transformers/models/table_transformer/configuration_table_transformer.py,sha256=zMIKMyqtkh7zsT2CSboS_HA9lsRDPkyShHmtQCS_MJo,12520
transformers/models/table_transformer/convert_table_transformer_to_hf.py,sha256=ItWZNI8n3yj-0fP-kbly0kq8yrb7Bc5Nz2HeInHnPdA,15095
transformers/models/table_transformer/convert_table_transformer_to_hf_no_timm.py,sha256=IJWfYRPya5zeVUqynktWlkiD7seeQdyU4kagQFXV4pU,21186
transformers/models/table_transformer/modeling_table_transformer.py,sha256=JrQF99eTlnB6IsnctsAd5G56pqiIIjow28txAQCq9HA,95255
transformers/models/tapas/__init__.py,sha256=uGhdu01xgzBDD5edwGpuFl94A2WmFd6FA_U2YWJZReA,2952
transformers/models/tapas/__pycache__/__init__.cpython-310.pyc,,
transformers/models/tapas/__pycache__/configuration_tapas.cpython-310.pyc,,
transformers/models/tapas/__pycache__/convert_tapas_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/tapas/__pycache__/modeling_tapas.cpython-310.pyc,,
transformers/models/tapas/__pycache__/modeling_tf_tapas.cpython-310.pyc,,
transformers/models/tapas/__pycache__/tokenization_tapas.cpython-310.pyc,,
transformers/models/tapas/configuration_tapas.py,sha256=75LYt8VkU1jQ0M3bPrMpeeVtcJilgq4xkdEGqpEIRlY,12900
transformers/models/tapas/convert_tapas_original_tf_checkpoint_to_pytorch.py,sha256=OeIyLEtDJr1z2BEKH0bJNJOR5ZrxRyGM8RpMSC3TgHQ,5049
transformers/models/tapas/modeling_tapas.py,sha256=PtmNPiVnDsTscbjdC2KmjdjrolmJjlB3yY8HxoEb_Ts,111492
transformers/models/tapas/modeling_tf_tapas.py,sha256=Ha4F04yipkSLYfPGkUxU-PV2awvKMP3YumXCDY4MtD0,105771
transformers/models/tapas/tokenization_tapas.py,sha256=bhXfKL0YBn3yGr_PeangJkbHXWtPLlhesEAfbN4Fz7s,121382
transformers/models/time_series_transformer/__init__.py,sha256=dtXXYFY750gxXLggZYQWy2iaq88scX8TYl021UEZHVs,2069
transformers/models/time_series_transformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/time_series_transformer/__pycache__/configuration_time_series_transformer.cpython-310.pyc,,
transformers/models/time_series_transformer/__pycache__/modeling_time_series_transformer.cpython-310.pyc,,
transformers/models/time_series_transformer/configuration_time_series_transformer.py,sha256=jksabDQIia7ZpYFueQFynq5peBWyc80p1QvX6RbEbBE,12004
transformers/models/time_series_transformer/modeling_time_series_transformer.py,sha256=4oCMTx2NXutEL9LDGsZMoM_OL_CdmIhqnHZT69gFCnk,88782
transformers/models/timesformer/__init__.py,sha256=eugQ_QcHxuxaGByRRLWyZZ_0ic66Mcz5qdwW_Qt-Nyg,1862
transformers/models/timesformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/timesformer/__pycache__/configuration_timesformer.cpython-310.pyc,,
transformers/models/timesformer/__pycache__/convert_timesformer_to_pytorch.cpython-310.pyc,,
transformers/models/timesformer/__pycache__/modeling_timesformer.cpython-310.pyc,,
transformers/models/timesformer/configuration_timesformer.py,sha256=Q0BpkbCGt7ZnQudvt8f_6KTvra-4JySJpOkldvOUEIc,5685
transformers/models/timesformer/convert_timesformer_to_pytorch.py,sha256=TjOfPbEC4oVb5tlOgU2m9g36OBizDEEjm0bbcZz6Mq8,10176
transformers/models/timesformer/modeling_timesformer.py,sha256=cuxNVmoMNyT-6q96zUBiaa9l4UoMwtGeoPAbV9NMv4U,35339
transformers/models/timm_backbone/__init__.py,sha256=rn9y1wXicP1g6IiI_tSWu7fnt5q_x6hfu3g9yQvovEU,1624
transformers/models/timm_backbone/__pycache__/__init__.cpython-310.pyc,,
transformers/models/timm_backbone/__pycache__/configuration_timm_backbone.cpython-310.pyc,,
transformers/models/timm_backbone/__pycache__/modeling_timm_backbone.cpython-310.pyc,,
transformers/models/timm_backbone/configuration_timm_backbone.py,sha256=PR-F13KbCSBdKgA8ASNh-gok8TLUFY1_7ke32AaasmA,3153
transformers/models/timm_backbone/modeling_timm_backbone.py,sha256=AXDH5tWEWZYY7mTOWCwsiEvoImk-NdXBLw-EUEMqH4M,6614
transformers/models/trocr/__init__.py,sha256=jevvndvNkGFaA2smYGtlhOnpGG5U6gIhmuwONgXNyeM,1818
transformers/models/trocr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/trocr/__pycache__/configuration_trocr.cpython-310.pyc,,
transformers/models/trocr/__pycache__/convert_trocr_unilm_to_pytorch.cpython-310.pyc,,
transformers/models/trocr/__pycache__/modeling_trocr.cpython-310.pyc,,
transformers/models/trocr/__pycache__/processing_trocr.cpython-310.pyc,,
transformers/models/trocr/configuration_trocr.py,sha256=6fOvj0yFGg0uZ3ueJru-_8t3CNDfnmGFD-mJyX2RFUc,6779
transformers/models/trocr/convert_trocr_unilm_to_pytorch.py,sha256=bLC3S27ydrVw_KMJCEfov5mjh2dWNFdfuAeM_hjU90o,10155
transformers/models/trocr/modeling_trocr.py,sha256=HjFgODZvitYU-zjaaraH0Lty1clmYFkJTEDY5XjHPTA,45425
transformers/models/trocr/processing_trocr.py,sha256=-iyJv7DCOlG-iKtKhtKmgbQKyU4eGydKGJDeLmBFML4,5745
transformers/models/tvlt/__init__.py,sha256=3hHJeODpJMJ9_06AAz0fAV7QCRljLoJcfXc69YypO9M,2687
transformers/models/tvlt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/tvlt/__pycache__/configuration_tvlt.cpython-310.pyc,,
transformers/models/tvlt/__pycache__/feature_extraction_tvlt.cpython-310.pyc,,
transformers/models/tvlt/__pycache__/image_processing_tvlt.cpython-310.pyc,,
transformers/models/tvlt/__pycache__/modeling_tvlt.cpython-310.pyc,,
transformers/models/tvlt/__pycache__/processing_tvlt.cpython-310.pyc,,
transformers/models/tvlt/configuration_tvlt.py,sha256=9pD8Oj9XoClehP7rwZOWMox-vysmOcaJ8OVbNhvd4Xw,8762
transformers/models/tvlt/feature_extraction_tvlt.py,sha256=peyeHHDn8S6X6bQIf3rWs4fWwPYSjabGC0f106x35W4,10555
transformers/models/tvlt/image_processing_tvlt.py,sha256=p-lTe_OIT8ZsP9EU-S3VLujVbN_Idyg1ZeBGOZI-Qus,19621
transformers/models/tvlt/modeling_tvlt.py,sha256=c_b4m51OwPc2s3GJT_kAnXGkJQxr1h-on9aDG0sGxlU,57388
transformers/models/tvlt/processing_tvlt.py,sha256=JaLjfV68tRz-Ts55YzccFCltQO4yZDTNW6DAreychSQ,3506
transformers/models/tvp/__init__.py,sha256=nMCJ05vKe35hpbNHygmLeBkYUXDH2ZZLB5U5Ij0DG6A,2366
transformers/models/tvp/__pycache__/__init__.cpython-310.pyc,,
transformers/models/tvp/__pycache__/configuration_tvp.cpython-310.pyc,,
transformers/models/tvp/__pycache__/image_processing_tvp.cpython-310.pyc,,
transformers/models/tvp/__pycache__/modeling_tvp.cpython-310.pyc,,
transformers/models/tvp/__pycache__/processing_tvp.cpython-310.pyc,,
transformers/models/tvp/configuration_tvp.py,sha256=fxwiH86QjtMhfolj0cU5KpJ-tryqA8IifHyfWOc6wAU,8364
transformers/models/tvp/image_processing_tvp.py,sha256=8HMNAuPnoqs7cBDh99K8rBkbRfmNqVIZPLJxzjCyAds,22644
transformers/models/tvp/modeling_tvp.py,sha256=CUr0IZB8ViMX3Bf-wB54ZGAPl2Fhc6wTX6fDJubws2c,38840
transformers/models/tvp/processing_tvp.py,sha256=6fJAgekPIOw95GpQ7b1_y76KGbC03upX9uH8XlbGdKE,6981
transformers/models/umt5/__init__.py,sha256=8JNrCapCONu5R5KyNUQagYTpiq2AuNhJRlwcMfpAD5c,1830
transformers/models/umt5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/umt5/__pycache__/configuration_umt5.cpython-310.pyc,,
transformers/models/umt5/__pycache__/convert_umt5_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/umt5/__pycache__/modeling_umt5.cpython-310.pyc,,
transformers/models/umt5/configuration_umt5.py,sha256=fW5E9f9bTxzmAHy57U099xetVN2YtdvbLBwQm_53EoU,7953
transformers/models/umt5/convert_umt5_checkpoint_to_pytorch.py,sha256=mKcFjDTUYzC4S2faD9UMTQTIl5nwGbOp4QkcFxEEdv8,12070
transformers/models/umt5/modeling_umt5.py,sha256=4XEIs584jQ2HeGaHN31i7xbJhSmwVMMh3iEr7CL3hBY,83102
transformers/models/unispeech/__init__.py,sha256=n4jtlc-pPF37uUx7mgB1GDnL2lQ-eKDI8xOLVVp840E,2018
transformers/models/unispeech/__pycache__/__init__.cpython-310.pyc,,
transformers/models/unispeech/__pycache__/configuration_unispeech.cpython-310.pyc,,
transformers/models/unispeech/__pycache__/convert_unispeech_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/unispeech/__pycache__/modeling_unispeech.cpython-310.pyc,,
transformers/models/unispeech/configuration_unispeech.py,sha256=_PLrbmdQ0mHCrTnl_xTcQPxTuVtjTJuJt7_vhTAnmAw,17728
transformers/models/unispeech/convert_unispeech_original_pytorch_checkpoint_to_pytorch.py,sha256=bwfIAusfhFih5WJEIIokApShfuYhJoirPltvRz2-T7Y,11340
transformers/models/unispeech/modeling_unispeech.py,sha256=KfasIq2y3gY-uOhnXTmRO0tnkgWSf3yTV1WHnQZQeM8,72707
transformers/models/unispeech_sat/__init__.py,sha256=gAf8t9qZaufCDyIyJICzCQTvrmV825BDZUKQoa08DhE,2267
transformers/models/unispeech_sat/__pycache__/__init__.cpython-310.pyc,,
transformers/models/unispeech_sat/__pycache__/configuration_unispeech_sat.cpython-310.pyc,,
transformers/models/unispeech_sat/__pycache__/convert_unispeech_original_s3prl_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/unispeech_sat/__pycache__/convert_unispeech_sat_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/unispeech_sat/__pycache__/modeling_unispeech_sat.cpython-310.pyc,,
transformers/models/unispeech_sat/configuration_unispeech_sat.py,sha256=oLeCI9BSR0t-5zmJUoEbpsHQJQbDLraS1nbb8QJH69g,19097
transformers/models/unispeech_sat/convert_unispeech_original_s3prl_checkpoint_to_pytorch.py,sha256=CnSYjNr7S7Mqa7Feosf9Dx7eQTYScVHG-QprNkY8uLk,4870
transformers/models/unispeech_sat/convert_unispeech_sat_original_pytorch_checkpoint_to_pytorch.py,sha256=NK_vA71Eq2q9P1x3ol-2Jlqjkv-Mi3NlXO9Ra7QUQsQ,9289
transformers/models/unispeech_sat/modeling_unispeech_sat.py,sha256=vf6Kc2aVTbOCbkEcxx1Tr7hGsTvhEsRSUM7aC_TbJeY,86301
transformers/models/univnet/__init__.py,sha256=aeEydP4QFet-MOxxwOZMKE-jGUG1spoCfXwMmESP27Y,1842
transformers/models/univnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/univnet/__pycache__/configuration_univnet.cpython-310.pyc,,
transformers/models/univnet/__pycache__/convert_univnet.cpython-310.pyc,,
transformers/models/univnet/__pycache__/feature_extraction_univnet.cpython-310.pyc,,
transformers/models/univnet/__pycache__/modeling_univnet.cpython-310.pyc,,
transformers/models/univnet/configuration_univnet.py,sha256=3KkI7VWfneomecD4yrFgRCZ7ZFoFpszKzfDn8YSio-M,6869
transformers/models/univnet/convert_univnet.py,sha256=R2gqXfz8Oq2rwIUU01V7T_oSoDGG2A4Gety-R80Yn24,6364
transformers/models/univnet/feature_extraction_univnet.py,sha256=snAVdQ5ClFX_Sw7upgvWyzJq4bUNRelRQaxcWxgHIgA,22821
transformers/models/univnet/modeling_univnet.py,sha256=AiQYeDhzFqIvdigMWUrQme-31U7ucoBUhVrGYz7W5IE,26922
transformers/models/upernet/__init__.py,sha256=z2avy6tP_WpANiGPA5RCxT_9yPp0PfEDlfUjL9rQsXM,1535
transformers/models/upernet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/upernet/__pycache__/configuration_upernet.cpython-310.pyc,,
transformers/models/upernet/__pycache__/convert_convnext_upernet_to_pytorch.cpython-310.pyc,,
transformers/models/upernet/__pycache__/convert_swin_upernet_to_pytorch.cpython-310.pyc,,
transformers/models/upernet/__pycache__/modeling_upernet.cpython-310.pyc,,
transformers/models/upernet/configuration_upernet.py,sha256=EGBEnaLRwf3ZtDUB1y6CtdsVXcmeRxXonNkbBxoVPtg,4965
transformers/models/upernet/convert_convnext_upernet_to_pytorch.py,sha256=l_CJoXwANEE9rm5mwpHwbusIoJLmN8jNGjxsj6WhZrk,10271
transformers/models/upernet/convert_swin_upernet_to_pytorch.py,sha256=lHV8SE_bZnxOo-zEJ21S2nY449uPVc3bpcl2JGKNEjA,14026
transformers/models/upernet/modeling_upernet.py,sha256=JUg0skRBevuNSEEy44-y-vm4H6cKJC5Y9C89IuxqFDc,17303
transformers/models/videomae/__init__.py,sha256=Yrv0_yOkvyL6slti-bw1oFR8t8VO8-6b40yF0Lf2uV4,2519
transformers/models/videomae/__pycache__/__init__.cpython-310.pyc,,
transformers/models/videomae/__pycache__/configuration_videomae.cpython-310.pyc,,
transformers/models/videomae/__pycache__/convert_videomae_to_pytorch.cpython-310.pyc,,
transformers/models/videomae/__pycache__/feature_extraction_videomae.cpython-310.pyc,,
transformers/models/videomae/__pycache__/image_processing_videomae.cpython-310.pyc,,
transformers/models/videomae/__pycache__/modeling_videomae.cpython-310.pyc,,
transformers/models/videomae/configuration_videomae.py,sha256=w4988N7TdaE-LrOQeG4IKm5SzavOHaBgw3B3qDMZodY,6719
transformers/models/videomae/convert_videomae_to_pytorch.py,sha256=rq2nT2ZJekra1G38kM2DH_qOvcZBDQFNgbCvH3mKZjY,13989
transformers/models/videomae/feature_extraction_videomae.py,sha256=Hg5wmFhkbncqR3nfvtevV6msaUEqvLBf4mtO4aICYTI,1200
transformers/models/videomae/image_processing_videomae.py,sha256=IcEROU2ktdvGVBfcNgDfl8H5TvqpsBagaUw8i_RocsY,16613
transformers/models/videomae/modeling_videomae.py,sha256=Ye98lJGGtjkLTRVDeJclVBOVN_4n4pRjbvyfPoWqC6k,47436
transformers/models/vilt/__init__.py,sha256=-fruuGWD0urXmb7STgXnrF3QY8J6Z6lfJuTneeL_BsM,2788
transformers/models/vilt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vilt/__pycache__/configuration_vilt.cpython-310.pyc,,
transformers/models/vilt/__pycache__/convert_vilt_original_to_pytorch.cpython-310.pyc,,
transformers/models/vilt/__pycache__/feature_extraction_vilt.cpython-310.pyc,,
transformers/models/vilt/__pycache__/image_processing_vilt.cpython-310.pyc,,
transformers/models/vilt/__pycache__/modeling_vilt.cpython-310.pyc,,
transformers/models/vilt/__pycache__/processing_vilt.cpython-310.pyc,,
transformers/models/vilt/configuration_vilt.py,sha256=1_qgk2-XR53R5yiE4eGW6ewaq18R31eQPJyhstubG4s,6930
transformers/models/vilt/convert_vilt_original_to_pytorch.py,sha256=2La4dYwCrc_67GVdLXfLCv70c9ksPJ_UUdBgsqmGJgA,12870
transformers/models/vilt/feature_extraction_vilt.py,sha256=dC0Glwc_rDX7zqp8BxRtzaLogQGI4I4CjQCgxU7UORw,1172
transformers/models/vilt/image_processing_vilt.py,sha256=efBRKDktSz_SImFgpg2s6K497h_5v8Cr35khxFDYWfo,23150
transformers/models/vilt/modeling_vilt.py,sha256=XtrOhMtLdLyykKLpbVAEYLo7wEngUMqmFCT8jOopO0k,64942
transformers/models/vilt/processing_vilt.py,sha256=0iOal8dCaE7JCQlZjbJ1-sHGxpDPZgUkMowEbxFRF2Q,6079
transformers/models/vision_encoder_decoder/__init__.py,sha256=IRQsS-4Bz-cm6B97rSoeC62Z1l1wns0XVDZwBn1KBIU,2627
transformers/models/vision_encoder_decoder/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/configuration_vision_encoder_decoder.cpython-310.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_flax_vision_encoder_decoder.cpython-310.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_tf_vision_encoder_decoder.cpython-310.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_vision_encoder_decoder.cpython-310.pyc,,
transformers/models/vision_encoder_decoder/configuration_vision_encoder_decoder.py,sha256=6LTIyBp258cvYTg5VBaX_Ao5btPjR1A-2-c1q_x-mH4,8261
transformers/models/vision_encoder_decoder/modeling_flax_vision_encoder_decoder.py,sha256=mX9eGpj8uPt3D9JTEE_TJCJmWy8L2WJUF6iAREoo-Ig,41656
transformers/models/vision_encoder_decoder/modeling_tf_vision_encoder_decoder.py,sha256=cQjVuUwoUBQ-ZAdeDhvOgjnRHHiJZqrnsRuqoVJdNNc,35787
transformers/models/vision_encoder_decoder/modeling_vision_encoder_decoder.py,sha256=MkgXWsoeayWsYNV_qhI-BqyY3YtBKU6BuEZWg7NK8V4,34787
transformers/models/vision_text_dual_encoder/__init__.py,sha256=kULrtY2Ie2eigdn63xnoEqRUlmKm31D9mUCJs4F62Lo,2730
transformers/models/vision_text_dual_encoder/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/configuration_vision_text_dual_encoder.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_flax_vision_text_dual_encoder.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_tf_vision_text_dual_encoder.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_vision_text_dual_encoder.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/processing_vision_text_dual_encoder.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/configuration_vision_text_dual_encoder.py,sha256=SiL7FLGXzdJYjkG9ClXLzMGB-PESPlmf_AWHQjcuukc,4527
transformers/models/vision_text_dual_encoder/modeling_flax_vision_text_dual_encoder.py,sha256=P7q9aOjNe9WfQ7QE3WpfyZONPJ0ecUN94CNPGqrdMNs,26690
transformers/models/vision_text_dual_encoder/modeling_tf_vision_text_dual_encoder.py,sha256=pJtl0gEiDwDaGmuUCXRpy-4i1A9YVm8eaPDYtRZWYQc,28354
transformers/models/vision_text_dual_encoder/modeling_vision_text_dual_encoder.py,sha256=SxtHHNd84AfylPzJlxRq572jjB-tRu0E22DpsgYhvtk,25315
transformers/models/vision_text_dual_encoder/processing_vision_text_dual_encoder.py,sha256=G1QQYQLLxPDVX4I5aOb0iG6PwrlDqjN7GNLZyqXUNqo,7035
transformers/models/visual_bert/__init__.py,sha256=OSQEpz1R0NjH9WvGkfsXKq_9LJTGfrHscqYd2xl9S_4,2235
transformers/models/visual_bert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/visual_bert/__pycache__/configuration_visual_bert.cpython-310.pyc,,
transformers/models/visual_bert/__pycache__/convert_visual_bert_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/visual_bert/__pycache__/modeling_visual_bert.cpython-310.pyc,,
transformers/models/visual_bert/configuration_visual_bert.py,sha256=9dHiDjhEhmTUoj9Y5Jr2ZoUxvB6hs4OOyV1osfleJ_4,7943
transformers/models/visual_bert/convert_visual_bert_original_pytorch_checkpoint_to_pytorch.py,sha256=BpXgEZ-5LdGIa0NK6BDZd_5VhKCqeWuu2oOQyUqcSRQ,5158
transformers/models/visual_bert/modeling_visual_bert.py,sha256=S_h19Vm7XGk3FaZ98VokTjZWDSCE94Zxv3IRElF_284,69552
transformers/models/vit/__init__.py,sha256=Kw3Pan4rUcu6RQsA7u-DpxMlmbzdmrA7GA3ha3nYO5k,3598
transformers/models/vit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vit/__pycache__/configuration_vit.cpython-310.pyc,,
transformers/models/vit/__pycache__/convert_dino_to_pytorch.cpython-310.pyc,,
transformers/models/vit/__pycache__/convert_vit_timm_to_pytorch.cpython-310.pyc,,
transformers/models/vit/__pycache__/feature_extraction_vit.cpython-310.pyc,,
transformers/models/vit/__pycache__/image_processing_vit.cpython-310.pyc,,
transformers/models/vit/__pycache__/modeling_flax_vit.cpython-310.pyc,,
transformers/models/vit/__pycache__/modeling_tf_vit.cpython-310.pyc,,
transformers/models/vit/__pycache__/modeling_vit.cpython-310.pyc,,
transformers/models/vit/configuration_vit.py,sha256=rup8oV5auTfuXRal3DI0JIU6h61PaxYPKlQmjhwLdWc,5830
transformers/models/vit/convert_dino_to_pytorch.py,sha256=CIkbWDBEgW5jmSWWoPZOosLLqCFiUz8oYgnj48JdtSM,8854
transformers/models/vit/convert_vit_timm_to_pytorch.py,sha256=LY_UklTkw47xwnCcY8AzVFH-6g5B8t3GTuQ0PbyZyn0,10890
transformers/models/vit/feature_extraction_vit.py,sha256=R-W_HNOybSpKxKGKfo4iDB4zGTRHeW1cq-29iwnbVl4,1165
transformers/models/vit/image_processing_vit.py,sha256=tsqvieDvDCgxfzAFOybDifVnthR6bEm62EcBD3IyZjI,13594
transformers/models/vit/modeling_flax_vit.py,sha256=KsTqlse5b5euRgYXhrXoNqCNvo0LEPBGuU_b0uNO0yo,25340
transformers/models/vit/modeling_tf_vit.py,sha256=bELmnkbvVgtTWMDvwCnqgRZZnlWl9rRq9kWFC_cguwM,31869
transformers/models/vit/modeling_vit.py,sha256=xJiruJIEOcAOBdTQlOXS-_XjmDynWx014OUHnuwfmlY,35645
transformers/models/vit_hybrid/__init__.py,sha256=kJffDq49Rz34fkQnLISzCp18xqXkVFOIWciOsZMjc2I,2316
transformers/models/vit_hybrid/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vit_hybrid/__pycache__/configuration_vit_hybrid.cpython-310.pyc,,
transformers/models/vit_hybrid/__pycache__/convert_vit_hybrid_timm_to_pytorch.cpython-310.pyc,,
transformers/models/vit_hybrid/__pycache__/image_processing_vit_hybrid.cpython-310.pyc,,
transformers/models/vit_hybrid/__pycache__/modeling_vit_hybrid.cpython-310.pyc,,
transformers/models/vit_hybrid/configuration_vit_hybrid.py,sha256=bxECMhxFvDxbPZ9zrXDeub4AQejVeJaJaFujtOu4ao4,6687
transformers/models/vit_hybrid/convert_vit_hybrid_timm_to_pytorch.py,sha256=MymDN5E1N5g1g5k0mK0M-F2VeYy_Me-hRWdVNTRFocA,13413
transformers/models/vit_hybrid/image_processing_vit_hybrid.py,sha256=aO_CA1A8QqekxbTJvWk3__ApnRtiC0dk_fPNLmtXUHE,15941
transformers/models/vit_hybrid/modeling_vit_hybrid.py,sha256=yUpJxK5IU8GI3Ga-7QLReqJrtEgcw5TDdWgHzW2XJzI,31942
transformers/models/vit_mae/__init__.py,sha256=-w9MTkUgGkYCX6q37upqBk7x-8g247YxYGVVAEJkIzk,2428
transformers/models/vit_mae/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vit_mae/__pycache__/configuration_vit_mae.cpython-310.pyc,,
transformers/models/vit_mae/__pycache__/convert_vit_mae_to_pytorch.cpython-310.pyc,,
transformers/models/vit_mae/__pycache__/modeling_tf_vit_mae.cpython-310.pyc,,
transformers/models/vit_mae/__pycache__/modeling_vit_mae.cpython-310.pyc,,
transformers/models/vit_mae/configuration_vit_mae.py,sha256=6Jia49fTlB-wZphbm42OJMLdArEgLVr_XkxMVFFR7ps,6569
transformers/models/vit_mae/convert_vit_mae_to_pytorch.py,sha256=Nj4Y5LS8H7xbyWNeLE9Vn0NFyXSQQYEcj1QQMzN1Hdg,7516
transformers/models/vit_mae/modeling_tf_vit_mae.py,sha256=ELH6wnhlzSVoX7AcEizFF3st4pCpO9fuv7wt0emZLlc,47226
transformers/models/vit_mae/modeling_vit_mae.py,sha256=9GrsJgWxZAPYUCznQ0aWlr3CX0Xnq802NrTNVVnNXKU,42822
transformers/models/vit_msn/__init__.py,sha256=4VVe0aSuBzHjTg4X2nuVet-9DgD5_dWlFkbLAr4bilc,1783
transformers/models/vit_msn/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vit_msn/__pycache__/configuration_vit_msn.cpython-310.pyc,,
transformers/models/vit_msn/__pycache__/convert_msn_to_pytorch.cpython-310.pyc,,
transformers/models/vit_msn/__pycache__/modeling_vit_msn.cpython-310.pyc,,
transformers/models/vit_msn/configuration_vit_msn.py,sha256=jOEIjeWBCMC9cQTAG6BQkhbwS9yLa8_I4uX9iHwH1SY,5063
transformers/models/vit_msn/convert_msn_to_pytorch.py,sha256=1xBjqvbviFkGxhi_xq2956R7qZpFEBdKPNOQYb-SoIA,9841
transformers/models/vit_msn/modeling_vit_msn.py,sha256=H7j7eS8Vh6jyE7rBGbVCNoZOzFu4tQbT_4LAX6ijCo4,29713
transformers/models/vitdet/__init__.py,sha256=Vaafapb4IUbKPzQUqPjhX6nvt14CTKlV51QneeQpTmc,1764
transformers/models/vitdet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vitdet/__pycache__/configuration_vitdet.cpython-310.pyc,,
transformers/models/vitdet/__pycache__/modeling_vitdet.cpython-310.pyc,,
transformers/models/vitdet/configuration_vitdet.py,sha256=gaSeM_otjmqMPNVtf9Q9mS-4d9puKP6XnMTOD0jnCUs,7498
transformers/models/vitdet/modeling_vitdet.py,sha256=aa2HthZAXSIeCeh1L5AuUZ_JrVZeAiLSUNPWmsTGHnw,34568
transformers/models/vitmatte/__init__.py,sha256=tl-h8_VOAHRT7VtJJJ-SFSl5lkHxfVEdDaCtm4ksJIg,2239
transformers/models/vitmatte/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vitmatte/__pycache__/configuration_vitmatte.cpython-310.pyc,,
transformers/models/vitmatte/__pycache__/convert_vitmatte_to_hf.cpython-310.pyc,,
transformers/models/vitmatte/__pycache__/image_processing_vitmatte.cpython-310.pyc,,
transformers/models/vitmatte/__pycache__/modeling_vitmatte.cpython-310.pyc,,
transformers/models/vitmatte/configuration_vitmatte.py,sha256=GSJLOabx5ZQaicH0zZZQh5w6LL-Pc4Ll92G3a4hEJg8,4742
transformers/models/vitmatte/convert_vitmatte_to_hf.py,sha256=1xctm78nmCLelPMqGJepxSyq5saKgA4by5CTzyxRPvc,6404
transformers/models/vitmatte/image_processing_vitmatte.py,sha256=w1y1AIFvbRNc6jcFrjSZpB2X_vgfbcTHAtoHmLKPw5c,13439
transformers/models/vitmatte/modeling_vitmatte.py,sha256=IN4SJDCXpNb4aGqjsOsmpATS9AMp3EGzzenCSRENBZM,12902
transformers/models/vits/__init__.py,sha256=JoVFhlJ0-hhxN3ND-JsESyEcsihDbT6j0WPmIH9DjCA,1887
transformers/models/vits/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vits/__pycache__/configuration_vits.cpython-310.pyc,,
transformers/models/vits/__pycache__/convert_original_checkpoint.cpython-310.pyc,,
transformers/models/vits/__pycache__/modeling_vits.cpython-310.pyc,,
transformers/models/vits/__pycache__/tokenization_vits.cpython-310.pyc,,
transformers/models/vits/configuration_vits.py,sha256=gDQaik8TS5a-yxBzz4ms-s9diUAMsTRaVXsr-gBU8DY,14001
transformers/models/vits/convert_original_checkpoint.py,sha256=N6rRzBaJlMxRwT7u33kUyJKy-4fFTWTD6nu_RTTOGt0,18610
transformers/models/vits/modeling_vits.py,sha256=wIyQq8I2ppJi41BwlW2rAUzG2G3M9StlySXz9e7BvXY,66381
transformers/models/vits/tokenization_vits.py,sha256=2b6cXsaN7AtWA2qGfzGaYhqRgYS62gnxZUJpomogcjQ,9376
transformers/models/vivit/__init__.py,sha256=Ajx0pvLrGGMBJruIaFHvqJiQyAM9BI9qLRi-5kyRT10,2441
transformers/models/vivit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vivit/__pycache__/configuration_vivit.cpython-310.pyc,,
transformers/models/vivit/__pycache__/convert_vivit_flax_to_pytorch.cpython-310.pyc,,
transformers/models/vivit/__pycache__/image_processing_vivit.cpython-310.pyc,,
transformers/models/vivit/__pycache__/modeling_vivit.cpython-310.pyc,,
transformers/models/vivit/configuration_vivit.py,sha256=yfbgcKTBM8ci8gQfEMAhMwbPJFSdOsGPaNC-s2-QPKA,5369
transformers/models/vivit/convert_vivit_flax_to_pytorch.py,sha256=yIwLQOx8eT-8AuYf_3KTfLwabCBdC1z_Z0WZDr4a7mM,9111
transformers/models/vivit/image_processing_vivit.py,sha256=_YvWPXDFXbSZglbWQ5Fzv4yZZt37pFtSHOo46kiNxq4,19142
transformers/models/vivit/modeling_vivit.py,sha256=Hoyd876GGw41o24AVlDyDg-Dwv-q6TgLNXrI2-iFTKw,30035
transformers/models/wav2vec2/__init__.py,sha256=eN9LbGY56T2Kz38zw3ChsiOkOHprtc4CgQjT8DSrUds,4139
transformers/models/wav2vec2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/configuration_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/convert_wav2vec2_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/convert_wav2vec2_original_s3prl_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/feature_extraction_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_flax_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_tf_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/processing_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/tokenization_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/configuration_wav2vec2.py,sha256=JL2FtKBaFMFgq4oQrRgj6Nksrpt8LUNy_dxS2_ZQR1o,20290
transformers/models/wav2vec2/convert_wav2vec2_original_pytorch_checkpoint_to_pytorch.py,sha256=hhc_QSStY43_pj4bIQf0TUWfiJo1KGkPuMTl16dP-ng,14293
transformers/models/wav2vec2/convert_wav2vec2_original_s3prl_checkpoint_to_pytorch.py,sha256=CMjcWPEsvvPpX-OlMUJQxHNDErbJbDVqVSCoqo-9hDk,4838
transformers/models/wav2vec2/feature_extraction_wav2vec2.py,sha256=D-yqFIpwjn_7LYJUmdnelRsn4qsoUrkZGX4Qsp5Y9CY,11511
transformers/models/wav2vec2/modeling_flax_wav2vec2.py,sha256=iLm6d5m0LYQs0qKqg3Tdx7I6vgCB5QCmFY6MYrKu0RA,57331
transformers/models/wav2vec2/modeling_tf_wav2vec2.py,sha256=3pirt72jwjsqdOWKY_N8fp_5zPushu-x8XA6GzuvLI4,70447
transformers/models/wav2vec2/modeling_wav2vec2.py,sha256=CoSzSvmRTQr9FOq3v6Fg4100Ynk95bJ4fxaihkePEa0,106143
transformers/models/wav2vec2/processing_wav2vec2.py,sha256=82JBzFgQxV5ZQgRYmMj3gqf3pxL8Q8nfdwnhsuUUZjU,7137
transformers/models/wav2vec2/tokenization_wav2vec2.py,sha256=cp6l2382Zciz33rhwI2A-aOhHQ7GaMIfTdijJQNXYWQ,38777
transformers/models/wav2vec2_conformer/__init__.py,sha256=w6Z-Rd5ONNTFI-ioN5VvNPhW842-_rKASoHN6lGeJx4,2375
transformers/models/wav2vec2_conformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/configuration_wav2vec2_conformer.cpython-310.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/convert_wav2vec2_conformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/modeling_wav2vec2_conformer.cpython-310.pyc,,
transformers/models/wav2vec2_conformer/configuration_wav2vec2_conformer.py,sha256=beY1nR8av7qNLjs8hV9M-K7xfIvU17y3q81cK4_TRtc,21067
transformers/models/wav2vec2_conformer/convert_wav2vec2_conformer_original_pytorch_checkpoint_to_pytorch.py,sha256=D8rojgR8DRaqVTZwYXd2qykIKlKf7EnMM6h3PzYPS0M,13382
transformers/models/wav2vec2_conformer/modeling_wav2vec2_conformer.py,sha256=PPiypOCL4NHwCX5L4Fcmk_zIH4ALZbiAOrTgRpp1ULU,95179
transformers/models/wav2vec2_phoneme/__init__.py,sha256=E2xRyViyzCISV8XE7YQ1gx5Wlx9_ACoPDB6ZZEm9bWo,993
transformers/models/wav2vec2_phoneme/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wav2vec2_phoneme/__pycache__/tokenization_wav2vec2_phoneme.cpython-310.pyc,,
transformers/models/wav2vec2_phoneme/tokenization_wav2vec2_phoneme.py,sha256=qh04GvGpxke3Wm8r2Xaq9gtVFbNWbQio1lF1vWCusJQ,23820
transformers/models/wav2vec2_with_lm/__init__.py,sha256=d_lvk8QAia4BIKN7d_Uy3HdRqrDp_ZJHTDZ-nkHKwPA,981
transformers/models/wav2vec2_with_lm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wav2vec2_with_lm/__pycache__/processing_wav2vec2_with_lm.cpython-310.pyc,,
transformers/models/wav2vec2_with_lm/processing_wav2vec2_with_lm.py,sha256=G33ucMXmYYnKdiCG49KWVkHt15ECLugQmed3g-v8Ll0,29702
transformers/models/wavlm/__init__.py,sha256=puMYnJLkFpkYKq7oH_ziapvzFYZMOyTHDqpN8IxzJPw,1959
transformers/models/wavlm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wavlm/__pycache__/configuration_wavlm.cpython-310.pyc,,
transformers/models/wavlm/__pycache__/convert_wavlm_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/wavlm/__pycache__/convert_wavlm_original_s3prl_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/wavlm/__pycache__/modeling_wavlm.cpython-310.pyc,,
transformers/models/wavlm/configuration_wavlm.py,sha256=DJ0kUxva4kf7O6glp_OLmHnUWMGhws-ju7Q7lHmpAko,18753
transformers/models/wavlm/convert_wavlm_original_pytorch_checkpoint_to_pytorch.py,sha256=tYQiS5CUNYoMWyxKnmkmDG6VW0lwapFxTrDSz4Pprm0,8580
transformers/models/wavlm/convert_wavlm_original_s3prl_checkpoint_to_pytorch.py,sha256=Yo4K3ZxH5KXS3gCD7KTakUviJABV-gJGJHXFeV5Sc9I,4814
transformers/models/wavlm/modeling_wavlm.py,sha256=mvfD4rwZyBCFaE-PBUoDgyR_HNzSQ3LD6Ruxvwk8aGY,78215
transformers/models/whisper/__init__.py,sha256=Y9nksRYJ-dCwFFdnagINwcqEMrdRG7AtPKWRB4uXlmM,4346
transformers/models/whisper/__pycache__/__init__.cpython-310.pyc,,
transformers/models/whisper/__pycache__/configuration_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/convert_openai_to_hf.cpython-310.pyc,,
transformers/models/whisper/__pycache__/english_normalizer.cpython-310.pyc,,
transformers/models/whisper/__pycache__/feature_extraction_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/modeling_flax_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/modeling_tf_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/modeling_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/processing_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/tokenization_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/tokenization_whisper_fast.cpython-310.pyc,,
transformers/models/whisper/configuration_whisper.py,sha256=Cw3Xr7emdiqW55iNwgdVDb1yybY6445plLZbiYWnrho,17053
transformers/models/whisper/convert_openai_to_hf.py,sha256=Jk3YoAGCTEHIvwGZSc5qetw7LU0jbjewYXG-YNdjiZk,14891
transformers/models/whisper/english_normalizer.py,sha256=MTJ16OhstprR2X8owfEJmONqkoSHHyzztENejmEhSBM,22822
transformers/models/whisper/feature_extraction_whisper.py,sha256=C-DYGZHBbxMsqpaC0FaJFYyPSI4RelcTmmIY1uWQRCY,11762
transformers/models/whisper/modeling_flax_whisper.py,sha256=s4sI__pmItZAAJxzmgU8f1jy3Dk4fAn9uGyy6TAaJnM,73587
transformers/models/whisper/modeling_tf_whisper.py,sha256=HCV8MUR2xganIDTjXzWuUUKmwRJkO3c4EI5cYYw4H8M,78746
transformers/models/whisper/modeling_whisper.py,sha256=wKxJTgBp9IFAk3ijBO6B9HoGBsMtILJismvCO1FCfMQ,145752
transformers/models/whisper/processing_whisper.py,sha256=pO6wtcywcJq-lkA2rNrdINEvj7_6fjWvAUv7HWn70gE,3891
transformers/models/whisper/tokenization_whisper.py,sha256=iDgPqwGZvXvfxnmkB4I-Oc1yiRiFdqSpx-vnzuqO0IE,53784
transformers/models/whisper/tokenization_whisper_fast.py,sha256=MMbV-MyICiAhKjlREh_Ps1_jRR9c2HBZoKXsR_0_3Zo,30882
transformers/models/x_clip/__init__.py,sha256=zWhh0KIKf1OaB3EezBv6YkgaxTESvEesITGqhiZYgHs,2053
transformers/models/x_clip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/x_clip/__pycache__/configuration_x_clip.cpython-310.pyc,,
transformers/models/x_clip/__pycache__/convert_x_clip_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/x_clip/__pycache__/modeling_x_clip.cpython-310.pyc,,
transformers/models/x_clip/__pycache__/processing_x_clip.cpython-310.pyc,,
transformers/models/x_clip/configuration_x_clip.py,sha256=uEDKqYGVnFIeefHa-S0cyd6emkBVq7xgsogBwe6pG_4,20475
transformers/models/x_clip/convert_x_clip_original_pytorch_to_hf.py,sha256=WzXe8IKqSz4Bi78EIvRA6C3QiLL4c-SpARggHjIWtt4,18066
transformers/models/x_clip/modeling_x_clip.py,sha256=U0yxrzeP6JDD_uOEeRr5wAPiI9vapet9vFDHqwB5Da8,70242
transformers/models/x_clip/processing_x_clip.py,sha256=vuwuN_pNagPMfdvGJrSbhQVTslOHBMGFgYV2xD9BHsw,6897
transformers/models/xglm/__init__.py,sha256=gSzCOADmOA0n4CxfKEhESj32_WqQ6ae6e0QjYyaJ-gs,3871
transformers/models/xglm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xglm/__pycache__/configuration_xglm.cpython-310.pyc,,
transformers/models/xglm/__pycache__/convert_xglm_original_ckpt_to_trfms.cpython-310.pyc,,
transformers/models/xglm/__pycache__/modeling_flax_xglm.cpython-310.pyc,,
transformers/models/xglm/__pycache__/modeling_tf_xglm.cpython-310.pyc,,
transformers/models/xglm/__pycache__/modeling_xglm.cpython-310.pyc,,
transformers/models/xglm/__pycache__/tokenization_xglm.cpython-310.pyc,,
transformers/models/xglm/__pycache__/tokenization_xglm_fast.cpython-310.pyc,,
transformers/models/xglm/configuration_xglm.py,sha256=qyFJy0gX9voVN_A3LoPwP2pXYReErZqxWI276SFulAQ,6056
transformers/models/xglm/convert_xglm_original_ckpt_to_trfms.py,sha256=9fjXP40nMFbiI9H0VV66Buqk9JQrPhAFERCOBYHl_7g,2325
transformers/models/xglm/modeling_flax_xglm.py,sha256=5-ubc4mqp9vhZFUUcyy8FzwwbS_xHpIA6pWIC9keOcg,33117
transformers/models/xglm/modeling_tf_xglm.py,sha256=NtqxhWMf-GJtlgWkvRwfTeo6V8lbOa5XJv_fJ5T3uss,41938
transformers/models/xglm/modeling_xglm.py,sha256=AHHte7zHqRqHVnWxwxiRKJMwQRWLRuTz7JX2tM956jU,38716
transformers/models/xglm/tokenization_xglm.py,sha256=2abuTSeamfthzQKk3Y5N3ti-kUyj4gVT9_s5PmEUfWc,12859
transformers/models/xglm/tokenization_xglm_fast.py,sha256=Zj7cHc4V7-1n1jii3dhuVjMDDa1GGosLwXAA7zcfdrU,8100
transformers/models/xlm/__init__.py,sha256=tYpOIDQrMDWgJJ-OTPmX2NZngDrxqo47NRfA1dyNQgY,3292
transformers/models/xlm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xlm/__pycache__/configuration_xlm.cpython-310.pyc,,
transformers/models/xlm/__pycache__/convert_xlm_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/xlm/__pycache__/modeling_tf_xlm.cpython-310.pyc,,
transformers/models/xlm/__pycache__/modeling_xlm.cpython-310.pyc,,
transformers/models/xlm/__pycache__/tokenization_xlm.cpython-310.pyc,,
transformers/models/xlm/configuration_xlm.py,sha256=JebmchGBlDELaoJhrgKH96wLklxaZwlBeIWNp2MNZBc,11975
transformers/models/xlm/convert_xlm_original_pytorch_checkpoint_to_pytorch.py,sha256=R2wBMzp-IIiBhTOHrgYacy3bX79BN1dh_DdHcO7fE1Y,2934
transformers/models/xlm/modeling_tf_xlm.py,sha256=gHLO0D5lFYRzJh_gOLrCO6aFUV7-nT5IC3sRg6LxtL0,51906
transformers/models/xlm/modeling_xlm.py,sha256=8kL_4hv75NhPxzFiuRePqI5VrmEw-JZIRgJNZ6v0LN4,54911
transformers/models/xlm/tokenization_xlm.py,sha256=xmFUXw0PUH2rRZnJyUGcaYwNPd7tP5_LRs_hHqpSgIM,34968
transformers/models/xlm_prophetnet/__init__.py,sha256=_YI-mEgntKjkMoW1RztiRlYdwvonIVpmO2ZQjm6Gezc,2615
transformers/models/xlm_prophetnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xlm_prophetnet/__pycache__/configuration_xlm_prophetnet.cpython-310.pyc,,
transformers/models/xlm_prophetnet/__pycache__/modeling_xlm_prophetnet.cpython-310.pyc,,
transformers/models/xlm_prophetnet/__pycache__/tokenization_xlm_prophetnet.cpython-310.pyc,,
transformers/models/xlm_prophetnet/configuration_xlm_prophetnet.py,sha256=Wb5PH7qlCsUuS1052kc-zyMjFgf5IJIFmKenbg2cwU8,9126
transformers/models/xlm_prophetnet/modeling_xlm_prophetnet.py,sha256=wDRKVJVoSQNugmTqZPZAdyIupPizgSdasUTfue5quG8,119470
transformers/models/xlm_prophetnet/tokenization_xlm_prophetnet.py,sha256=9C3CMsEJl7EX8aLBqiZERVC5LWN7CqcIWzap67BFW_0,13848
transformers/models/xlm_roberta/__init__.py,sha256=Uhk9z5Xv2w8KrHfe0Hzc5ndpgmn5k6_dcZw6OCWye1A,5825
transformers/models/xlm_roberta/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/configuration_xlm_roberta.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_flax_xlm_roberta.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_tf_xlm_roberta.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_xlm_roberta.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/tokenization_xlm_roberta.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/tokenization_xlm_roberta_fast.cpython-310.pyc,,
transformers/models/xlm_roberta/configuration_xlm_roberta.py,sha256=7r7UU-aegw7FxLiriSs0aaM3BonUmZE9IztAyZXFYWk,8347
transformers/models/xlm_roberta/modeling_flax_xlm_roberta.py,sha256=RK9Xy0jJ2EAV5XQky1r_g-ZVgcN1FZZrnz6kGaf9f6o,58617
transformers/models/xlm_roberta/modeling_tf_xlm_roberta.py,sha256=Df-hY94SyLemLaArTOpxLj4KxhSyPYSkITplA1BIkys,73898
transformers/models/xlm_roberta/modeling_xlm_roberta.py,sha256=srJscdZOHjCUWtujnHMRVwkLmqrtWnHY9V7Fz6ev61o,73118
transformers/models/xlm_roberta/tokenization_xlm_roberta.py,sha256=k2CVeYtLtGndtKwdTBpvnYGFTfqkrLVfL64L0QPCJ38,14176
transformers/models/xlm_roberta/tokenization_xlm_roberta_fast.py,sha256=iFGvL1Ny3-nloLj1CnYHTPFIXfQbfl7U7X6_5qFcnIE,10325
transformers/models/xlm_roberta_xl/__init__.py,sha256=Q3eFSJ5cKAt-2cJLXKdWW28TLujRqjebIBzlqSvK0U4,2405
transformers/models/xlm_roberta_xl/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/configuration_xlm_roberta_xl.cpython-310.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/convert_xlm_roberta_xl_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/modeling_xlm_roberta_xl.cpython-310.pyc,,
transformers/models/xlm_roberta_xl/configuration_xlm_roberta_xl.py,sha256=w506R4z7LAqCvcro2TgaCKlFHjq1q3frHb3_9kGYcSU,7596
transformers/models/xlm_roberta_xl/convert_xlm_roberta_xl_original_pytorch_checkpoint_to_pytorch.py,sha256=zVa6azx9rd33D3JkH2uqJ6W20TosJyWi9eLm3LNtc5U,8228
transformers/models/xlm_roberta_xl/modeling_xlm_roberta_xl.py,sha256=yZ7Dt2_l3JcxVMNf5JDBGLYe77qscb8cQwXPqc-eoSE,69083
transformers/models/xlnet/__init__.py,sha256=-jvIW4RkN8qTjJPEEmIvK6pO8c9NB0Q4JlzY7CWHWUI,4288
transformers/models/xlnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/configuration_xlnet.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/convert_xlnet_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/modeling_tf_xlnet.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/modeling_xlnet.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/tokenization_xlnet.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/tokenization_xlnet_fast.cpython-310.pyc,,
transformers/models/xlnet/configuration_xlnet.py,sha256=vlQoXgMDG1lq1pJ_FtIZ0EZahZn_8yYd0RP_andHEXM,11143
transformers/models/xlnet/convert_xlnet_original_tf_checkpoint_to_pytorch.py,sha256=iodIP1W2FNMjel9V31jR7RcHqs8zGX8TK3YdQ65lEbk,3688
transformers/models/xlnet/modeling_tf_xlnet.py,sha256=jhluY9lvtlYpd5FqKqh8lpmlWt_thOGK3ckFMpRcs84,73071
transformers/models/xlnet/modeling_xlnet.py,sha256=PBea6vdR-J43HD2thlyRM_xzzNjH2CyMaEZvjwOA_38,92846
transformers/models/xlnet/tokenization_xlnet.py,sha256=ihrzyGNnPERnJcFdGLQDrzT1CTfPg4THAKYgQgUvLZw,16192
transformers/models/xlnet/tokenization_xlnet_fast.py,sha256=l9dSHXYAsMSI_xOZBvAliqOCqthq2XOKPFSkTkWFigo,10087
transformers/models/xmod/__init__.py,sha256=uoKu7ACrFCEwDUwL06kwYCcUbHt9P3bLIcHLtMtjw-I,2325
transformers/models/xmod/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xmod/__pycache__/configuration_xmod.cpython-310.pyc,,
transformers/models/xmod/__pycache__/convert_xmod_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/xmod/__pycache__/modeling_xmod.cpython-310.pyc,,
transformers/models/xmod/configuration_xmod.py,sha256=yerKDbvIIFcEvQI6pMdYTQ7guJBz_9jTMxz68ZTZRvo,10146
transformers/models/xmod/convert_xmod_original_pytorch_checkpoint_to_pytorch.py,sha256=yFSAtXjxbAy6uXBg2XinRbk3VSEBOsWj1ugBhVNrGjQ,9859
transformers/models/xmod/modeling_xmod.py,sha256=R1pM9Qcmfrmz2C7lXOy0sIxz1jGZd9RD2re47z7IMA8,76593
transformers/models/yolos/__init__.py,sha256=DwUvf4HvS249i-g_ykayoDwxJnO7yH4pUJ7UhDE36iY,2400
transformers/models/yolos/__pycache__/__init__.cpython-310.pyc,,
transformers/models/yolos/__pycache__/configuration_yolos.cpython-310.pyc,,
transformers/models/yolos/__pycache__/convert_yolos_to_pytorch.cpython-310.pyc,,
transformers/models/yolos/__pycache__/feature_extraction_yolos.cpython-310.pyc,,
transformers/models/yolos/__pycache__/image_processing_yolos.cpython-310.pyc,,
transformers/models/yolos/__pycache__/modeling_yolos.cpython-310.pyc,,
transformers/models/yolos/configuration_yolos.py,sha256=0sbEDLjKNhcfC5g9uAbWEjLxeL8RfYxdHXRBS985wxs,7785
transformers/models/yolos/convert_yolos_to_pytorch.py,sha256=g9sI7E-yfoyuXLc2OlN5bFxkc6ZTM243T1Wi8eUwnT0,11259
transformers/models/yolos/feature_extraction_yolos.py,sha256=0ebN1Be4y86C2yyN2rMQ9AbguEDjcQ7fkabropUpwcs,1481
transformers/models/yolos/image_processing_yolos.py,sha256=UOEYntfSoOqmlrQa94DnYebdNj1BvZ9SUv16vbgxYoM,59000
transformers/models/yolos/modeling_yolos.py,sha256=2It7duoWl6uQyPnJcnNx1-vBRA2MZ_tqn5aw9wCDQGw,58424
transformers/models/yoso/__init__.py,sha256=oV8Bo29EwsQRWVZy2nIaea2ArpOnhkENfp0nFfSKcB4,2074
transformers/models/yoso/__pycache__/__init__.cpython-310.pyc,,
transformers/models/yoso/__pycache__/configuration_yoso.cpython-310.pyc,,
transformers/models/yoso/__pycache__/convert_yoso_pytorch_to_pytorch.cpython-310.pyc,,
transformers/models/yoso/__pycache__/modeling_yoso.cpython-310.pyc,,
transformers/models/yoso/configuration_yoso.py,sha256=jZ74qZrSS9ee4ZOEy7HKZ3FuUDJPGvRH3BvwlcCK-Xg,6903
transformers/models/yoso/convert_yoso_pytorch_to_pytorch.py,sha256=VjPOSLINfkiaHx8M3dTNMdC8hXh3M1yyhIQ9t4Vzqk0,4115
transformers/models/yoso/modeling_yoso.py,sha256=KMzuR52MewdqxrNuOO1mGSTfUxn_zS-R34PMSAoCyLk,54805
transformers/onnx/__init__.py,sha256=wALLY4TPOK2iPrFcfZf_WiEmTRAU6dAWHElxGdexr58,1548
transformers/onnx/__main__.py,sha256=X9Qp23B29eoO3Jf8mboAWCFi3dqoqlme5hdgZgpgANY,9519
transformers/onnx/__pycache__/__init__.cpython-310.pyc,,
transformers/onnx/__pycache__/__main__.cpython-310.pyc,,
transformers/onnx/__pycache__/config.cpython-310.pyc,,
transformers/onnx/__pycache__/convert.cpython-310.pyc,,
transformers/onnx/__pycache__/features.cpython-310.pyc,,
transformers/onnx/__pycache__/utils.cpython-310.pyc,,
transformers/onnx/config.py,sha256=zPDgC_HSLmMeqPkcLv_Y8EfbfLLEDLqPrvrfQCRyhl8,32556
transformers/onnx/convert.py,sha256=4fL2A2BCqjRv-loIFOMxqvtBCSVEoqAhDD-GaArNgMU,21258
transformers/onnx/features.py,sha256=GSuwZj760THxAkDmJYROt43La0GaY-bA19j2bE-XYVI,28264
transformers/onnx/utils.py,sha256=39Uw_GkFBsTb6ZvMIHRTnI289aQDhc6hwfEapaBGE-o,3625
transformers/optimization.py,sha256=ZbiBbFkZNgBJt6bPNIwwUsDCpWb1khyAqOcHVsJrwv8,32390
transformers/optimization_tf.py,sha256=eBima9BzlBTWrbbnnd26NRWzBW_JL33G7RoxdJkVg3E,16592
transformers/pipelines/__init__.py,sha256=5ue3_bwPpumj8w2LTzQ6kgTm0eTA5S3k40ujPV7lC6g,49757
transformers/pipelines/__pycache__/__init__.cpython-310.pyc,,
transformers/pipelines/__pycache__/audio_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/audio_utils.cpython-310.pyc,,
transformers/pipelines/__pycache__/automatic_speech_recognition.cpython-310.pyc,,
transformers/pipelines/__pycache__/base.cpython-310.pyc,,
transformers/pipelines/__pycache__/conversational.cpython-310.pyc,,
transformers/pipelines/__pycache__/depth_estimation.cpython-310.pyc,,
transformers/pipelines/__pycache__/document_question_answering.cpython-310.pyc,,
transformers/pipelines/__pycache__/feature_extraction.cpython-310.pyc,,
transformers/pipelines/__pycache__/fill_mask.cpython-310.pyc,,
transformers/pipelines/__pycache__/image_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/image_segmentation.cpython-310.pyc,,
transformers/pipelines/__pycache__/image_to_image.cpython-310.pyc,,
transformers/pipelines/__pycache__/image_to_text.cpython-310.pyc,,
transformers/pipelines/__pycache__/mask_generation.cpython-310.pyc,,
transformers/pipelines/__pycache__/object_detection.cpython-310.pyc,,
transformers/pipelines/__pycache__/pt_utils.cpython-310.pyc,,
transformers/pipelines/__pycache__/question_answering.cpython-310.pyc,,
transformers/pipelines/__pycache__/table_question_answering.cpython-310.pyc,,
transformers/pipelines/__pycache__/text2text_generation.cpython-310.pyc,,
transformers/pipelines/__pycache__/text_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/text_generation.cpython-310.pyc,,
transformers/pipelines/__pycache__/text_to_audio.cpython-310.pyc,,
transformers/pipelines/__pycache__/token_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/video_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/visual_question_answering.cpython-310.pyc,,
transformers/pipelines/__pycache__/zero_shot_audio_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/zero_shot_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/zero_shot_image_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/zero_shot_object_detection.cpython-310.pyc,,
transformers/pipelines/audio_classification.py,sha256=lhslI-mFyjFTdmfk-QRsEaivF6Up2WyJHSvSABQblVk,8781
transformers/pipelines/audio_utils.py,sha256=S6rjFDlr64hcm__6m_CXFcLKAuDnMUVTwIsb69sUiig,8396
transformers/pipelines/automatic_speech_recognition.py,sha256=qOAUct7Fj1NoSlCCKBOsRs20tZtSfdpINXBChk-RLZY,40382
transformers/pipelines/base.py,sha256=GDMJJN5Lm7cDM2UECWXTSGeRWxrR2UthmwqlxozgFGA,52269
transformers/pipelines/conversational.py,sha256=fmgCf8t8KThLS4ZX9Ur8K-UC9OUOc_ZPMl3of50MQEk,14667
transformers/pipelines/depth_estimation.py,sha256=NSLkv0n0poFncroGPhYxYXjpSYv035YahuUjOdFBJS8,4658
transformers/pipelines/document_question_answering.py,sha256=uUUwG_QagIGvXmKeY4DcTeSmlfTgfEa0TCNPyeAW2m0,23457
transformers/pipelines/feature_extraction.py,sha256=47I0jWsAOkqpR4rtcuVuPz-r2_Fk5GvzmkvdRBYgDio,4782
transformers/pipelines/fill_mask.py,sha256=a1WlWdBt9slRvV6Fjxkn88b1SU-dQb6gIpck1nSmniY,11451
transformers/pipelines/image_classification.py,sha256=MYtSqrPZVo8cz6b0Rm6qOCwji7fkfPgXGj2tKhNoqO0,5360
transformers/pipelines/image_segmentation.py,sha256=JudzeRlwS0wDFXSrizbScMauDFvDbOWYrhBxMrGsIOQ,9086
transformers/pipelines/image_to_image.py,sha256=a0VB3-LZYoIU0YTx0E7TOdjog_6yczV0KnalQB9HIbA,4900
transformers/pipelines/image_to_text.py,sha256=A2lwaY7CX3efNjHqqUlrQQyEN7sg-tMj-b-rlq6cHtQ,7512
transformers/pipelines/mask_generation.py,sha256=qBw0u2ZlDBZVRYPC0Y2On967CzDXvtGbJesxt5oQfbk,13676
transformers/pipelines/object_detection.py,sha256=8oulQJrwlssj4hRjpXGPq9zOY8qhVFwQrcjlvtIrajc,7893
transformers/pipelines/pt_utils.py,sha256=-ZGaHid7ln3Q3UUQxUAYEWup6qESu8UJ3aZMLazddfI,12613
transformers/pipelines/question_answering.py,sha256=26cEP4GmC53617stto_RGZOYVlMEAG4inyvvtqPyro4,29854
transformers/pipelines/table_question_answering.py,sha256=bZzYoaH3Y9sIhqtWbc2YpIRV_1lOoIf9e9a3Uw9piT8,19910
transformers/pipelines/text2text_generation.py,sha256=DbxpW_j0Ayt7mLviX0i9mNI77O4LrXGOCm439s996GI,17076
transformers/pipelines/text_classification.py,sha256=ldWZjVKKCZ-3i886UsaNxCYGxrIskHKbMVMc_hCQGN0,10399
transformers/pipelines/text_generation.py,sha256=cWHjbHaou7gcrAb6VPGAjU12iS2l2dDzbWuJfcTOzwA,15724
transformers/pipelines/text_to_audio.py,sha256=rM_dkLd07pind_jTwv4ChoWmeuKJSrRjg3_iEMGbrZQ,8186
transformers/pipelines/token_classification.py,sha256=B8Ls9iYkpVFvj3RIhrYVKUht9WgS06DBaXj9-3iMz90,26686
transformers/pipelines/video_classification.py,sha256=C9hRntmrvCd0iZdPVcBa4bYtHLKoM1PnZzMYmvvuCyw,5020
transformers/pipelines/visual_question_answering.py,sha256=oAerha__tg6Jmf5-xwsjdHzX3ueDiAnfSqZEuwCj6nE,6721
transformers/pipelines/zero_shot_audio_classification.py,sha256=50C4R8kwKHU4Zovva86J7PNGmDFVDV-ej6fdJJU7Z_4,6501
transformers/pipelines/zero_shot_classification.py,sha256=Mpa_ddGyNIM8pa8J249u35ULnVcvQVhRvrn4bVBeN5o,12315
transformers/pipelines/zero_shot_image_classification.py,sha256=_pu6Ymh9vqFtypJj5GB0EE3i2qmhE75960zOFz_H-GA,6446
transformers/pipelines/zero_shot_object_detection.py,sha256=57a-KLVDpwXO0jxbvMt9PTqhN9mNpzNfR0M_wuqSOMk,9434
transformers/processing_utils.py,sha256=wezCn5-Xt8C01yQsr5L4bb3PksU1yGCOGTBJNLcGmcs,12512
transformers/pytorch_utils.py,sha256=FwffKFubf78RPtxfb1Y5_GuOTxnf_fAsSzHhXJoDdOg,12126
transformers/safetensors_conversion.py,sha256=ckxzVoyWtP5XmwuiS9KfmP-EoEwWi09GZeX09a68rpM,4315
transformers/sagemaker/__init__.py,sha256=fKtKAHamz_CLL9jPGCa2E-1n8RmuS-58qGtzZuKc3qg,730
transformers/sagemaker/__pycache__/__init__.cpython-310.pyc,,
transformers/sagemaker/__pycache__/trainer_sm.cpython-310.pyc,,
transformers/sagemaker/__pycache__/training_args_sm.cpython-310.pyc,,
transformers/sagemaker/trainer_sm.py,sha256=7GsKLtjdMfKp98OwHD7RcBsl745OOwHAaBswkfLkfsE,1044
transformers/sagemaker/training_args_sm.py,sha256=4ZnQhITfMwT0y2Y2MvkI11PEB_yfTX5Z7WrPKt0VXD8,5389
transformers/testing_utils.py,sha256=QtlFomDCfekAhfvzNbuGbWYSy15EVbHGUvnOVe7Dh9s,80618
transformers/tf_utils.py,sha256=fQp7_jSCFcTrGj87_ouKWFmb7JiW4AdRg8iL9I1jfnc,9571
transformers/time_series_utils.py,sha256=LjOgIvLmP0v6fJoqGo8lCD1kr3sXx9O_jmI-qJejtPU,7520
transformers/tokenization_utils.py,sha256=uKIYhBXmOjcHuY3XW27yAyPaS_r9IF7UHlZRE0JxvzY,44567
transformers/tokenization_utils_base.py,sha256=Y6VX4INrcd-nkZB5HQS-H1EAdNmWz-5Usmf7k7O6078,196099
transformers/tokenization_utils_fast.py,sha256=tpErvsUzI0RSiZJJtdmi7LbEuIltXnul9FrhAFCuIoM,37523
transformers/tools/__init__.py,sha256=hI6M7zNUTyRE3BiZtL1VM8CcpYqxTrFR7lS0U6T7InM,2955
transformers/tools/__pycache__/__init__.cpython-310.pyc,,
transformers/tools/__pycache__/agent_types.cpython-310.pyc,,
transformers/tools/__pycache__/agents.cpython-310.pyc,,
transformers/tools/__pycache__/base.cpython-310.pyc,,
transformers/tools/__pycache__/document_question_answering.cpython-310.pyc,,
transformers/tools/__pycache__/evaluate_agent.cpython-310.pyc,,
transformers/tools/__pycache__/image_captioning.cpython-310.pyc,,
transformers/tools/__pycache__/image_question_answering.cpython-310.pyc,,
transformers/tools/__pycache__/image_segmentation.cpython-310.pyc,,
transformers/tools/__pycache__/prompts.cpython-310.pyc,,
transformers/tools/__pycache__/python_interpreter.cpython-310.pyc,,
transformers/tools/__pycache__/speech_to_text.cpython-310.pyc,,
transformers/tools/__pycache__/text_classification.cpython-310.pyc,,
transformers/tools/__pycache__/text_question_answering.cpython-310.pyc,,
transformers/tools/__pycache__/text_summarization.cpython-310.pyc,,
transformers/tools/__pycache__/text_to_speech.cpython-310.pyc,,
transformers/tools/__pycache__/translation.cpython-310.pyc,,
transformers/tools/agent_types.py,sha256=6ZVzmPwWiMtJXKUZ33fKzfUFp-v_qfI901MKj2pbQRY,9093
transformers/tools/agents.py,sha256=u9f-PCVYwN9clKu_-Rh-CEfB2qDn1S8Llx-kpIFbvKY,30514
transformers/tools/base.py,sha256=KFih5_Pa9DCNEWqYDJQhxdn6qIcarpV1w01CM4bJWBc,30133
transformers/tools/document_question_answering.py,sha256=7qSMr0fQYadiGOoVMXNrImls3_O-hcdDbLrlSc3cvxU,3337
transformers/tools/evaluate_agent.py,sha256=a8p36Vlme5yALwit0CQzqB_8VkDP_0MTpKEbmbIZQtA,24735
transformers/tools/image_captioning.py,sha256=x1PfWpDozWSZuue633XwEPPBTr_zEX9mgrYar-8LqXQ,1745
transformers/tools/image_question_answering.py,sha256=UNOzIcmkckh1W1bqlj31h61eXGAZ1TZ831iqytyO4NQ,1969
transformers/tools/image_segmentation.py,sha256=1BbHSYTz3q8DlTMHBnKdibp7JCHZydPdNoyl7TObfN8,2103
transformers/tools/prompts.py,sha256=1YXY_A5Zfyd_rudKzB4ShQ9OR_E5bHeh9bcgBVt1ltQ,1558
transformers/tools/python_interpreter.py,sha256=aSn1bnuQT9_xteXNcJdlmi39IzX1FZRqSaoGEQRS-PE,9999
transformers/tools/speech_to_text.py,sha256=m3LCJxMpJykL9aD8rZ4H3ROGtt59LcLozw-6963XjCE,1482
transformers/tools/text_classification.py,sha256=snyBoLTERnfl7YKKAgZctWhow6sEXQdS4bcWYUxJnyU,2475
transformers/tools/text_question_answering.py,sha256=mGO3E0nL71Jzn4jeC2_RgLRDtuqbld77mQ2T7jw4aPc,1967
transformers/tools/text_summarization.py,sha256=-8TY4P4LL4c7bQcD9y8Vi5Rfiaw8nAiY_aP5yXicq_g,1691
transformers/tools/text_to_speech.py,sha256=vuJU2dC2d5J1kVdGjSBJCBdsTiOli2J7OabAposOFfA,2424
transformers/tools/translation.py,sha256=fu05jVYbJUFmNvmwd4mjQOqzGt1JSy6QbpuAd2uChOE,8493
transformers/trainer.py,sha256=u6_oYCV3SFq28dGf52Rml4R16Gffxiy2y8LI_qUtz44,188773
transformers/trainer_callback.py,sha256=9kYpkd9b0oybozxxfdv86r-bmT4d5P8TCrwwJ_H_Kvg,24890
transformers/trainer_pt_utils.py,sha256=3nAmTrHqslzzAmj49fVxgO7BSOSptV7rQ1b7WB2-BdU,48387
transformers/trainer_seq2seq.py,sha256=EmIOCWcX12AQhz5EGc5FX652c0f5jNfvEU0wOYkSWDU,16409
transformers/trainer_tf.py,sha256=bu1mX0kGwjdQn9yAuAlJtDLom_JE6UlC6kfa-n5wZa0,34697
transformers/trainer_utils.py,sha256=78qZXFcjUGMxAadf8LrkSJEBBuQflcg0JKX5ILzld_g,27327
transformers/training_args.py,sha256=Tr_gBRmDqY0D2a086bm94lYqL-eOv5u9qLaK2IJ0HrA,136942
transformers/training_args_seq2seq.py,sha256=Lcx_bGiM7GZYKPtUeAmVniaWw34Jo8RaDw7QGb2AoFs,4484
transformers/training_args_tf.py,sha256=q5goMj9YSjtiZVGYy7TQdMYR0udVE1s7uJ4jyZnAZjY,14534
transformers/utils/__init__.py,sha256=vwZNqa-EU1-MiXsWq54wl7cj1NTP9lQ5QvbXgHR1QQA,7201
transformers/utils/__pycache__/__init__.cpython-310.pyc,,
transformers/utils/__pycache__/backbone_utils.cpython-310.pyc,,
transformers/utils/__pycache__/bitsandbytes.cpython-310.pyc,,
transformers/utils/__pycache__/constants.cpython-310.pyc,,
transformers/utils/__pycache__/doc.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_detectron2_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_essentia_and_librosa_and_pretty_midi_and_scipy_and_torch_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_flax_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_keras_nlp_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_music_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_pt_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_sentencepiece_and_tokenizers_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_sentencepiece_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_speech_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_tensorflow_text_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_tf_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_tokenizers_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_vision_objects.cpython-310.pyc,,
transformers/utils/__pycache__/fx.cpython-310.pyc,,
transformers/utils/__pycache__/generic.cpython-310.pyc,,
transformers/utils/__pycache__/hp_naming.cpython-310.pyc,,
transformers/utils/__pycache__/hub.cpython-310.pyc,,
transformers/utils/__pycache__/import_utils.cpython-310.pyc,,
transformers/utils/__pycache__/logging.cpython-310.pyc,,
transformers/utils/__pycache__/model_parallel_utils.cpython-310.pyc,,
transformers/utils/__pycache__/notebook.cpython-310.pyc,,
transformers/utils/__pycache__/peft_utils.cpython-310.pyc,,
transformers/utils/__pycache__/quantization_config.cpython-310.pyc,,
transformers/utils/__pycache__/sentencepiece_model_pb2.cpython-310.pyc,,
transformers/utils/__pycache__/sentencepiece_model_pb2_new.cpython-310.pyc,,
transformers/utils/__pycache__/versions.cpython-310.pyc,,
transformers/utils/backbone_utils.py,sha256=TyBLr-l-roaS4-cRoUB5IIeCraUmHcVdjzZBTW2qR6s,12028
transformers/utils/bitsandbytes.py,sha256=LzOKwcHWAxxZZv-7Ts9Q0vlEYvHd18affVgVbiR3Tzs,1040
transformers/utils/constants.py,sha256=sZsUwOnA3CbtN1svs9YoaNLTTsAc9RVaITsgpf8K4iI,282
transformers/utils/doc.py,sha256=eObKDEpC1z-05BNXHi1hYNjQMPsWSN1SNMa7IFkRmN8,40737
transformers/utils/dummy_detectron2_objects.py,sha256=gr1gUAeK_j7ygeFW2X4_aZYy9QmY-MKqH1k7UlQxyIk,391
transformers/utils/dummy_essentia_and_librosa_and_pretty_midi_and_scipy_and_torch_objects.py,sha256=n6pY4s7zCII3dzo7Ejd0RviHa_pMateuDEwbbHgsTUY,902
transformers/utils/dummy_flax_objects.py,sha256=Pl27L-8U0CulUbcwdEM-2KLj4YgPTwmBZ6LOW02qvIE,32587
transformers/utils/dummy_keras_nlp_objects.py,sha256=AVWt2orICCUXi754bkavvqPzYO91PjER-FlUZAw2jZc,294
transformers/utils/dummy_music_objects.py,sha256=1lxIebYUOdHJWMQ_T5IQgPgcO_wp_8YM_HGc3skuGVg,458
transformers/utils/dummy_pt_objects.py,sha256=Ul0-1GU9uD46yEJBe1R_pdwVHHtGY6CyjWKkpUN4XnQ,216763
transformers/utils/dummy_sentencepiece_and_tokenizers_objects.py,sha256=BgPLr8Wz8A-17K86x04N21CKXtWNQLJEWx2c4aZRqaA,286
transformers/utils/dummy_sentencepiece_objects.py,sha256=1Dwra1N_Rrguqwv9AfUl8WxzSQ_oCFKGnS9hZRrfzcg,5933
transformers/utils/dummy_speech_objects.py,sha256=9eFm1cjdsYOPBoAz9JTgP35Bg8WF2C9AZ_y1hFpKZdQ,465
transformers/utils/dummy_tensorflow_text_objects.py,sha256=43V0IA2kb9gtuL0S1OL1eRFFxzQwKg4pPjMVuXUB5qg,306
transformers/utils/dummy_tf_objects.py,sha256=Bb6MMOFIYiya22HlUsovxaz0e3K7pxhSTw0i5lczxiI,68207
transformers/utils/dummy_tokenizers_objects.py,sha256=6aE-IDQ0Hu2bo7aeQ2E3gfMGgU2L6Ou1gH44EUuQ_JM,10768
transformers/utils/dummy_vision_objects.py,sha256=gVxnP9EAAN5pmQkQn3W-zj6_9kk_DUBY68xqJBG4JXU,13881
transformers/utils/fx.py,sha256=98KsALdEy1ZkpxyfNc5vG0iH12nMHHeforJ784kGslY,47842
transformers/utils/generic.py,sha256=IAiWyDISHN0jHCBgqM2DXvhYJuKHlo_402NO_Cb1TAU,22600
transformers/utils/hp_naming.py,sha256=vqcOXcDOyqbISWo8-ClUJUOBVbZM1h08EcymTwcRthc,4979
transformers/utils/hub.py,sha256=S7PIuAZrDkveIvq0OkV7DB_oZXGd4OVl6HdUP6AK22o,53446
transformers/utils/import_utils.py,sha256=nNeAUaOJSCxWF22l9fN6eFW3vEXarsp2sXEGdlmm8Qg,48852
transformers/utils/logging.py,sha256=ruD01LU1nSpoGwnP4xN-ZjmGBKwaF45cYNjQ2HXUxiA,11568
transformers/utils/model_parallel_utils.py,sha256=XbGU9IlFF59K_aplRxUGVnTfIZ9mpbLomKqQ08ooTew,2272
transformers/utils/notebook.py,sha256=PiEiHpfuqxd3M1U3MPD8bmeO8bvtTbLfOxnL-cZWHQY,15558
transformers/utils/peft_utils.py,sha256=mNLrHd4nzx8_U1XHcDyFfa4aBxIhsFopqvaxD8M2UXI,5165
transformers/utils/quantization_config.py,sha256=iLG7no683tiseYB3-MUAYsi-NgA2VCyqLimbVUTaoEk,29760
transformers/utils/sentencepiece_model_pb2.py,sha256=XiQs9uMEusfAZP6t6IBuTTX9yl7LiOyJEi7Ib-Wzmq0,50677
transformers/utils/sentencepiece_model_pb2_new.py,sha256=FwTW0nkCiPCErmGk0s27BniKmkORcfnNk-w7NBGkCuA,6621
transformers/utils/versions.py,sha256=-e7XW1TzZ-tsRo9PMQHp-hNGYHuVDFzLtwg3uAJzqdI,4333

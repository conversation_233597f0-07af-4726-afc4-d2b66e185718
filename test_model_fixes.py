#!/usr/bin/env python3
"""
Test script to verify model loading fixes.
"""
import sys
import os
import warnings
from pathlib import Path

# Completely disable accelerate to avoid device conflicts
os.environ["ACCELERATE_DISABLE_RICH"] = "1"
os.environ["TRANSFORMERS_NO_ADVISORY_WARNINGS"] = "1"
os.environ["TRANSFORMERS_OFFLINE"] = "0"
os.environ["HF_HUB_DISABLE_TELEMETRY"] = "1"
# Force transformers to not use accelerate
os.environ["TRANSFORMERS_VERBOSITY"] = "error"

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Apply warnings filters early
warnings.filterwarnings("ignore", message="copying from a non-meta parameter in the checkpoint to a meta parameter")
warnings.filterwarnings("ignore", message=".*The model has been loaded with `accelerate` and therefore cannot be moved to a specific device.*")
warnings.filterwarnings("ignore", message=".*GPT2LMHeadModel.__init__.*got an unexpected keyword argument.*")
warnings.filterwarnings("ignore", category=UserWarning, module="transformers.utils.generic")

from loguru import logger

# Configure clean logging
logger.remove()
logger.add(
    lambda msg: print(msg, end=""),
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    colorize=True
)

def test_model_loading():
    """Test basic model loading functionality."""
    logger.info("🧪 Testing model loading fixes...")

    try:
        # Test 1: Basic transformers pipeline
        logger.info("📋 Test 1: Basic transformers pipeline")
        from transformers import pipeline

        # Try to create a simple pipeline without any device parameters
        try:
            # First try: completely minimal pipeline
            text_generator = pipeline("text-generation", model="distilgpt2")
            logger.info("✅ Successfully created DistilGPT-2 pipeline (minimal)")
        except Exception as e1:
            logger.warning(f"Minimal pipeline failed: {e1}")
            try:
                # Second try: load components separately
                from transformers import GPT2LMHeadModel, GPT2Tokenizer

                # Load tokenizer first
                tokenizer = GPT2Tokenizer.from_pretrained("distilgpt2")

                # Load model with minimal parameters
                model = GPT2LMHeadModel.from_pretrained("distilgpt2")

                # Create pipeline with pre-loaded components (no device params)
                text_generator = pipeline("text-generation", model=model, tokenizer=tokenizer)
                logger.info("✅ Successfully created DistilGPT-2 pipeline (separate loading)")
            except Exception as e2:
                logger.error(f"Both pipeline creation methods failed: {e2}")
                raise
        logger.info("✅ Successfully created GPT-2 pipeline with device=-1")

        # Test generation
        result = text_generator("Hello, I am", max_length=20, num_return_sequences=1)
        logger.info(f"✅ Generated text: {result[0]['generated_text'][:50]}...")

    except Exception as e:
        logger.error(f"❌ Test 1 failed: {str(e)}")
        return False

    try:
        # Test 2: Model manager loading
        logger.info("📋 Test 2: Model manager loading")
        from app.utils.model_manager import model_manager

        # Try to load a simple model
        model = model_manager.get_model("gpt2", "text-generation")
        if model:
            logger.info("✅ Model manager successfully loaded GPT-2")
        else:
            logger.info("ℹ️ Model manager returned None (may need to load first)")

    except Exception as e:
        logger.error(f"❌ Test 2 failed: {str(e)}")
        return False

    try:
        # Test 3: LLM manager initialization
        logger.info("📋 Test 3: LLM manager initialization")
        from app.llm.manager import LLMManager

        llm_manager = LLMManager()
        logger.info("✅ LLM manager initialized successfully")

        # Test generation
        result = llm_manager.generate("Hello, how are you?", max_tokens=50)
        logger.info(f"✅ LLM manager generated: {result.get('text', 'No text')[:50]}...")

    except Exception as e:
        logger.error(f"❌ Test 3 failed: {str(e)}")
        return False

    logger.info("🎉 All model loading tests passed!")
    return True

def test_route_imports():
    """Test that all model route imports work."""
    logger.info("🧪 Testing model route imports...")

    try:
        # Test model route imports
        from app.api.routes import models, model_manager, model_training
        from app.api.routes.model_management import router as model_management_router
        logger.info("✅ All model route imports successful")
        return True

    except Exception as e:
        logger.error(f"❌ Route import test failed: {str(e)}")
        return False

def main():
    """Main test function."""
    logger.info("🎯 TESTING MODEL FIXES")
    logger.info("="*50)

    # Test 1: Route imports
    routes_ok = test_route_imports()

    # Test 2: Model loading
    models_ok = test_model_loading()

    # Summary
    logger.info("\n" + "="*50)
    logger.info("📊 TEST RESULTS")
    logger.info("="*50)

    results = {
        "Route Imports": routes_ok,
        "Model Loading": models_ok
    }

    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"   {test_name}: {status}")

    overall_success = all(results.values())

    if overall_success:
        logger.info("\n🎉 ALL TESTS PASSED! Model fixes are working correctly!")
        logger.info("✨ You can now start the application and test the model routes.")
    else:
        logger.info("\n⚠️ Some tests failed. Check the logs above for details.")

    logger.info("="*50)
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

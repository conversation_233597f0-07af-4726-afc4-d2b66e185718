#!/usr/bin/env python3
"""
Database schema fix script to add missing columns.
"""
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from sqlalchemy import text, inspect
from app.db.database import get_db_session, engine
from app.db.models import Base

# Configure clean logging
logger.remove()
logger.add(
    lambda msg: print(msg, end=""),
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    colorize=True
)

def check_column_exists(table_name: str, column_name: str) -> bool:
    """Check if a column exists in a table."""
    try:
        inspector = inspect(engine)
        columns = inspector.get_columns(table_name)
        return any(col['name'] == column_name for col in columns)
    except Exception as e:
        logger.error(f"Error checking column {column_name} in table {table_name}: {e}")
        return False

def add_missing_columns():
    """Add missing columns to the database."""
    logger.info("🔍 Checking for missing database columns...")
    
    # List of columns to check/add
    columns_to_check = [
        {
            'table': 'users',
            'column': 'email_verified',
            'sql': 'ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE;'
        },
        {
            'table': 'users',
            'column': 'email_verified_at',
            'sql': 'ALTER TABLE users ADD COLUMN email_verified_at TIMESTAMP NULL;'
        }
    ]
    
    with get_db_session() as db:
        for col_info in columns_to_check:
            table_name = col_info['table']
            column_name = col_info['column']
            
            if not check_column_exists(table_name, column_name):
                logger.info(f"📝 Adding missing column {column_name} to table {table_name}")
                try:
                    db.execute(text(col_info['sql']))
                    db.commit()
                    logger.info(f"✅ Successfully added column {column_name} to table {table_name}")
                except Exception as e:
                    logger.error(f"❌ Failed to add column {column_name} to table {table_name}: {e}")
                    db.rollback()
            else:
                logger.info(f"✅ Column {column_name} already exists in table {table_name}")

def create_missing_tables():
    """Create any missing tables."""
    logger.info("🔍 Checking for missing database tables...")
    
    try:
        # Create all tables defined in models
        Base.metadata.create_all(bind=engine)
        logger.info("✅ All database tables are up to date")
    except Exception as e:
        logger.error(f"❌ Error creating tables: {e}")

def verify_database_schema():
    """Verify the database schema is correct."""
    logger.info("🔍 Verifying database schema...")
    
    # Check critical tables exist
    critical_tables = ['users', 'roles', 'conversations', 'messages']
    
    inspector = inspect(engine)
    existing_tables = inspector.get_table_names()
    
    for table in critical_tables:
        if table in existing_tables:
            logger.info(f"✅ Table {table} exists")
        else:
            logger.error(f"❌ Table {table} is missing")
    
    # Check critical columns in users table
    if 'users' in existing_tables:
        user_columns = inspector.get_columns('users')
        column_names = [col['name'] for col in user_columns]
        
        critical_user_columns = ['id', 'username', 'email', 'password_hash', 'email_verified']
        
        for col in critical_user_columns:
            if col in column_names:
                logger.info(f"✅ Column users.{col} exists")
            else:
                logger.error(f"❌ Column users.{col} is missing")

def main():
    """Main function to fix database schema."""
    logger.info("🚀 Starting database schema fix...")
    logger.info("="*60)
    
    try:
        # Step 1: Create missing tables
        create_missing_tables()
        
        # Step 2: Add missing columns
        add_missing_columns()
        
        # Step 3: Verify schema
        verify_database_schema()
        
        logger.info("\n" + "="*60)
        logger.info("🎉 Database schema fix completed successfully!")
        logger.info("✨ You can now start the application.")
        logger.info("="*60)
        
    except Exception as e:
        logger.error(f"❌ Database schema fix failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test script to demonstrate the clean logging improvements.
"""
import asyncio
import time
from loguru import logger

# Configure clean logging
logger.remove()
logger.add(
    lambda msg: print(msg, end=""),
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    colorize=True
)

async def simulate_clean_startup():
    """Simulate the clean startup process with improved logging."""
    
    # Step 1: Application startup
    logger.info("🚀 Application started successfully!")
    logger.info("📡 FastAPI server: http://localhost:8000")
    logger.info("📊 Admin dashboard: http://localhost:3001")
    logger.info("🔄 Background model loading in progress...")
    
    await asyncio.sleep(1)
    
    # Step 2: Model loading progress
    models = [
        "distil-whisper/distil-small.en",
        "sentence-transformers/all-MiniLM-L6-v2", 
        "TinyLlama/TinyLlama-1.1B-Chat-v1.0",
        "microsoft/DialoGPT-medium",
        "facebook/blenderbot-400M-distill"
    ]
    
    total_models = len(models)
    
    for i, model in enumerate(models):
        logger.info(f"📥 Loading model {i+1}/{total_models}: {model.split('/')[-1]}")
        await asyncio.sleep(0.5)  # Simulate loading time
        progress = ((i + 1) / total_models) * 100
        logger.info(f"✅ Model loaded: {model.split('/')[-1]} ({progress:.1f}% complete)")
        await asyncio.sleep(0.3)
    
    # Step 3: Clean status updates
    logger.info("🎉 Background model loading complete!")
    
    # Step 4: Periodic status (clean and informative)
    for minute in range(1, 4):
        await asyncio.sleep(2)  # Simulate time passing
        logger.info(f"📊 ✅ API Healthy | ⏱️ Uptime: {minute:02d}:00 | 🎉 All models loaded")
    
    logger.info("✨ Application running smoothly with clean logs!")

async def simulate_old_noisy_logs():
    """Simulate the old noisy logging for comparison."""
    
    logger.info("=== OLD NOISY LOGS (for comparison) ===")
    
    # Simulate the old repetitive logs
    for i in range(5):
        logger.info("FastAPI stderr: 2025-05-30 20:06:11.957 | INFO | app.messaging.kafka_client:consume_messages:996 - Creating consumer for topic simba_train_data with group train_data_processor (attempt 1/3)")
        logger.info("FastAPI stderr: 2025-05-30 20:06:11.957 | INFO | app.messaging.kafka_client:consume_messages:1022 - Starting consumer for topic simba_train_data (attempt 1/3)")
        logger.info("Application running - FastAPI is active, models are being loaded in background")
        await asyncio.sleep(0.2)
    
    logger.info("=== END OLD NOISY LOGS ===")

async def main():
    """Main demonstration function."""
    
    print("\n" + "="*80)
    print("🎯 LOGGING IMPROVEMENTS DEMONSTRATION")
    print("="*80)
    
    print("\n📋 BEFORE: Noisy, repetitive logs")
    print("-" * 40)
    await simulate_old_noisy_logs()
    
    await asyncio.sleep(1)
    
    print("\n📋 AFTER: Clean, informative logs")
    print("-" * 40)
    await simulate_clean_startup()
    
    print("\n" + "="*80)
    print("✅ IMPROVEMENTS SUMMARY:")
    print("  1. ❌ Removed double logging (FastAPI stderr: wrapper)")
    print("  2. ❌ Removed repetitive 'Application running' messages every 10s")
    print("  3. ✅ Added clean startup messages with emojis")
    print("  4. ✅ Added model loading progress with percentages")
    print("  5. ✅ Added informative status updates every 30s (instead of 10s)")
    print("  6. ✅ Added uptime tracking and health status")
    print("  7. ✅ Added graceful shutdown messages")
    print("="*80)

if __name__ == "__main__":
    asyncio.run(main())

# 🧠 Generative & Agentic AI System

A custom AI system powered by SimbaAI models for foundational model capabilities, providing agentic and generative behavior with reasoning and planning.

## 🎯 Features

- SimbaAI model architecture for efficient inference
- TinyLlama integration for efficient SimbaAI inference
- Custom Simba models for domain-specific tasks
- Agentic behavior with reasoning and planning
- Multilingual support (including all African languages)
- Short-term (Redis) and long-term (Qdrant) memory
- Contextual RAG (CRAG) and standard RAG
- Web search capabilities without API keys
- Multiple response formats (infographics, tables, reports, etc.)
- Continuous learning and prompt management
- Efficient structured and unstructured data storage

## 🔄 Startup Scripts Comparison

The project includes two main startup scripts with different optimization strategies:

| Feature | `start_app.py` | `optimized_startup.py` |
|---------|----------------|------------------------|
| **Purpose** | Standard startup with full model loading | Optimized startup with prioritized model loading |
| **Initial Load** | Loads all models sequentially | Loads small models first, then large models in background |
| **API Availability** | Waits for all models to load before starting | Starts API immediately after small models load |
| **Model Loading** | Synchronous loading of all models | Asynchronous background loading for large models |
| **Performance** | Slower initial startup | Faster initial response time |
| **Use Case** | When all models must be available immediately | When quick API availability is prioritized |
| **Model Priority** | Loads models as defined in config | Prioritizes small models first |
| **Resource Usage** | High initial memory usage | Gradual memory increase |
| **Error Handling** | Starts only if all models load | Continues even if some models fail to load |
| **Environment** | Better for development/testing | Better for production with quick startup needs |

## 📁 Cache Structure

The application uses a unified cache structure for all models and related files:

```
./data/
├── model_cache/           # HF_HOME - Base directory for all model caches
│   ├── datasets/          # HF_DATASETS_CACHE - Cached datasets
│   ├── local_models/      # Manually managed models (from LOCAL_MODEL_PATH)
│   │   └── tinyllama/    # Example model directory
│   └── models--*         # Auto-downloaded Hugging Face models
```

### Environment Variables

- `HF_HOME`: Base directory for all Hugging Face related files (default: `./data/model_cache`)
- `TRANSFORMERS_CACHE`: Cache directory for transformers (default: same as HF_HOME)
- `HF_DATASETS_CACHE`: Cache directory for datasets (default: `$HF_HOME/datasets`)
- `LOCAL_MODEL_PATH`: Directory for manually managed models (default: `$HF_HOME/local_models`)

### Migration

To migrate from the old structure, run:

```bash
python scripts/migrate_model_cache.py
```

This will move existing models to the new structure while maintaining compatibility.

## 🚀 Getting Started

### Prerequisites

- Python 3.10+
- Docker and Docker Compose
- PostgreSQL
- MongoDB
- Redis
- Qdrant
- Kafka (optional for streaming)

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/sim_llm.git
cd sim_llm
```

2. Create a virtual environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies
```bash
pip install -r requirements.txt
```

4. Set up environment variables
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Run the application

### Starting the Application

The application is started using the `start_app.py` script in the root directory, which handles model synchronization, database initialization, and application startup.

#### Production Environment

For production deployments, use multiple workers and disable auto-reload:

```bash
# Production setup with 4 workers
./start_app.py --workers 4 --host 0.0.0.0 --port 8000 --all-models

# Production setup with logging configuration
./start_app.py --workers 4 --log-level info --skip-wait
```

#### Development Environment

For development, enable auto-reload and debug mode:

```bash
# Development setup with auto-reload
./start_app.py --reload --debug --host 127.0.0.1 --port 8000

# Development with minimal model loading (faster startup)
./start_app.py --reload --debug --skip-wait
```

#### Testing Environment

For testing, you can skip model synchronization and database initialization:

```bash
# Testing setup
./start_app.py --skip-sync --skip-db-init --host 127.0.0.1 --port 8000

# Quick testing with minimal overhead
./start_app.py --skip-sync --skip-preload --skip-db-init
```

### Available Command-Line Options

The `start_app.py` script provides numerous options to customize the application startup:

```bash
# Basic usage
./start_app.py

# Force model synchronization even if cached
./start_app.py --force-sync

# Skip model synchronization
./start_app.py --skip-sync

# Skip model preloading at startup (faster startup)
./start_app.py --skip-preload

# Skip waiting for models to load (faster startup)
./start_app.py --skip-wait

# Specify host and port
./start_app.py --host 127.0.0.1 --port 8080

# Enable auto-reload (for development)
./start_app.py --reload

# Use multiple worker processes (for production)
./start_app.py --workers 4

# Set log level
./start_app.py --log-level debug

# Enable debug mode (sets log level to DEBUG and enables verbose output)
./start_app.py --debug

# Load all essential models, not just the default ones
./start_app.py --all-models

# Skip database and model registry initialization
./start_app.py --skip-db-init
```

### Model Loading Options

The startup script provides several options for model loading:

- **Default Mode**: Only loads default models (TinyLlama, distilgpt2)
- **All Models Mode**: Loads all essential models (use `--all-models` flag)
- **Force Sync**: Forces reloading of all models even if cached (use `--force-sync` flag)
- **Skip Sync**: Skips model loading entirely (use `--skip-sync` flag)
- **Skip Wait**: Starts the application without waiting for models to load (use `--skip-wait` flag)
- **Skip Preload**: Prevents models from being preloaded at startup (use `--skip-preload` flag)

### Environment Variables

The `start_app.py` script sets several environment variables that control application behavior:

- `SIM_LLM_LOG_LEVEL`: Sets the application log level
- `SIM_LLM_PRELOAD_MODELS`: Controls whether models are preloaded at startup
- `SIM_LLM_DEBUG`: Enables debug mode
- `SIM_LLM_MODELS_SYNCED`: Indicates that models have been synchronized

6. Access the admin dashboard
```bash
cd app/admin-dashboard
npm install
npm run dev
```

The admin dashboard will be available at http://localhost:3001

### Default Admin User

A default admin user is automatically created when you first run the application:

- **Username:** admin
- **Password:** simba
- **Roles:** admin, super_admin

You can also manually create the default admin user by running:
```bash
python create_admin.py
```

## 🤖 Model Management

The system uses SimbaAI models (TinyLlama, distilgpt2) to provide robust AI capabilities while maintaining efficiency and performance.

### Model Architecture

- **Primary Models**: TinyLlama (1.1B parameters) runs efficiently on CPU for cost-effective inference
- **Fallback Models**: Smaller models like distilgpt2 and gpt2 provide reliable backup options
- **Custom Models**: Simba-trained models for domain-specific tasks

### Model Loading and Caching

Models are loaded using a sophisticated pooling system with LRU (Least Recently Used) caching:
- Models are loaded on demand and cached in memory
- Least recently used models are unloaded when memory limits are reached
- Essential models can be preloaded at startup
- Model metadata is stored in MongoDB for distributed deployments

### Command-Line Interface

The application provides a comprehensive CLI for model management:

```bash
# List all available models
python -m app.cli models list

# List models for a specific task
python -m app.cli models list --task text-generation

# Download all models to local storage
python -m app.cli models download --all

# Force reload models even if cached
python -m app.cli models download --all --force

# Sync models with MongoDB and MinIO
python -m app.cli models sync

# Sync a specific model
python -m app.cli models sync --model-id distilgpt2 --task text-generation

# Check model storage status
python -m app.cli models status

# Manage model pool
python -m app.cli models pool --list
python -m app.cli models pool --max-models 10
python -m app.cli models pool --unload-all

# List alternative models
python -m app.cli models alternatives --list
```

### Model Selection Strategy

The system uses a smart model selection strategy:
1. First attempts to use the specified model
2. Intelligent model selection based on query complexity
3. Automatic fallback to alternative SimbaAI models when needed
4. Automatically selects the most appropriate model based on the task

### API Endpoints and Model Capabilities

The system exposes various API endpoints that leverage different model capabilities:

#### Text Generation and Chat API (`/api/v1/chat`)
- **Endpoint**: `/api/v1/chat`
- **Models**: TinyLlama, distilgpt2, gpt2
- **Capabilities**:
  - Multilingual conversation with automatic language detection
  - Context-aware responses with memory management
  - Confidence scoring and hallucination detection
  - Streaming responses for real-time interaction
  - Session management for continuous conversations
  - Multiple response formats (infographics, research documents, tables, etc.)
  - Web search integration for up-to-date information

#### Enhanced Chat Features (`/api/v1/enhanced-chat`)
- **Endpoints**: 
  - `/api/v1/enhanced-chat/predictions` - Predictive text completion
  - `/api/v1/enhanced-chat/spell-check` - Spelling and grammar correction
  - `/api/v1/enhanced-chat/response-options` - Alternative response suggestions
  - `/api/v1/enhanced-chat/refine` - Response refinement and improvement
  - `/api/v1/enhanced-chat/moderate` - Content moderation and filtering
- **Models**: Custom fine-tuned models for specific tasks

#### Image Processing (`/api/v1/image`)
- **Endpoints**:
  - `/api/v1/image/generate` - Text-to-image generation
  - `/api/v1/image/analyze` - Image analysis and object detection
  - `/api/v1/image/edit` - Image editing with text prompts
  - `/api/v1/image/variations` - Create variations of existing images
- **Models**: DALL-E 2, DALL-E 3, Stable Diffusion, YOLOv8, EfficientNet

#### Document Processing (`/api/v1/document`)
- **Capabilities**: Document analysis, OCR, summarization, and information extraction
- **Models**: OCR models, text extraction models, summarization models

#### Multimodal Processing (`/api/v1/multimodal`)
- **Capabilities**: Process and analyze text, images, and other data types together
- **Models**: CLIP, LLaVA, multimodal transformers

#### Language Services (`/api/v1/language`)
- **Capabilities**: Translation, language detection, sentiment analysis
- **Models**: Translation models, language identification models

#### Vector Search and RAG (`/api/v1/vector`)
- **Capabilities**: Semantic search, retrieval-augmented generation
- **Models**: Sentence transformers, embedding models

#### Web Search (`/api/v1/web`)
- **Endpoints**:
  - `/api/v1/web/search` - Search the web for information
- **Capabilities**:
  - Real-time web search without API keys
  - Multiple search engines (DuckDuckGo, Wikipedia)
  - Content extraction and cleaning
  - Automatic search detection in chat
  - Multilingual search support
- **Integration**:
  - Automatically detects when web search is needed in chat
  - Enhances system prompts with search results
  - Provides citations and references in responses

#### Model Management (`/api/v1/models`)
- **Endpoints**:
  - `/api/v1/models` - List all available models grouped by task
  - `/api/v1/models/{task}` - List models for a specific task
  - `/api/v1/models/{task}/{model_id}/status` - Get model status
  - `/api/v1/models/{task}/{model_id}/load` - Load a specific model

For detailed information about all available models and tasks, see [README_MODELS.md](README_MODELS.md).

## 📊 Response Formatting

The system includes a sophisticated response formatting engine that can present information in various formats to enhance user experience and information comprehension.

### Available Formats

The system supports 14+ different response formats:

- **Infographic**: Visual representation with key points and emojis
- **Research Document**: Academic structure with abstract, sections, and references
- **Table**: Tabular data representation with rows and columns
- **Report**: Formal report with executive summary and recommendations
- **Bullet Points**: Concise list with bullet points
- **Step-by-Step**: Sequential instructions with numbered steps
- **Comparison**: Side-by-side comparison of different items
- **Timeline**: Chronological sequence of events
- **FAQ**: Question and answer format
- **Code**: Code snippets with explanations
- **Diagram**: Text-based diagram representation
- **Summary**: Concise summary of information
- **Outline**: Structured outline with sections and subsections
- **Pros and Cons**: Advantages and disadvantages list

### Format Detection

The system can detect format requests in two ways:

1. **Explicit Format Parameter**: Specify the format in the API request
   ```json
   {
     "content": "Explain renewable energy",
     "format": "infographic"
   }
   ```

2. **Implicit Format Detection**: Detect format from the message content
   ```json
   {
     "content": "Create an infographic about renewable energy"
   }
   ```

### Format Parameters

Users can specify additional parameters for formats:

- **Title**: "Create an infographic with title 'Clean Energy'"
- **Columns/Rows**: "Make a table with 4 columns"
- **Color**: "Use a blue color scheme"
- **Language**: "Show code in Python"
- **Points/Steps**: "List 5 bullet points"

For more details, see [Response Formatting Documentation](app/formatters/README.md).

## 🧠 System Prompt Engineering

The system includes an advanced prompt engineering framework that enables dynamic creation and optimization of system prompts for various AI models and use cases.

### Component-Based Architecture

The prompt engineering system uses a modular, component-based architecture:

- **Text Components**: Static text with variable substitution
- **Template Components**: Conditional sections based on context variables
- **Function Components**: Dynamic content generation through custom functions

### Supported Model Types and Goals

The framework supports multiple model types and optimization goals:

- **Model Types**: LLMs, Text-to-Text, QA, Multimodal
- **Prompt Goals**: Quality improvement, Capability enhancement, Format control, Hallucination reduction
- **Application Contexts**: Chat, Task automation, API services, Fine-tuning

### Usage Examples

```python
# Create a quality-focused chat prompt
chat_prompt = create_chat_quality_prompt(
    assistant_name="SimbaAI",
    persona="a helpful assistant"
)

# Create a code assistant prompt
code_prompt = create_code_assistant_prompt(
    languages=["Python", "JavaScript"],
    expertise_level="expert"
)

# Create a RAG-optimized prompt
rag_prompt = create_rag_prompt(
    context_format="markdown",
    citation_style="inline"
)
```

### API Endpoints

- **GET** `/api/v1/prompts/templates` - List available prompt templates
- **POST** `/api/v1/prompts/system` - Generate a system prompt with custom parameters
- **POST** `/api/v1/prompts/test` - Test a prompt against sample inputs
- **GET** `/api/v1/prompts/components` - List available prompt components

### Testing and Optimization

The framework includes tools for prompt testing and optimization:

- **PromptTester**: Evaluate prompts against test cases
- **A/B Testing**: Compare different prompt variations
- **Optimization**: Automatically tune prompts based on performance metrics

## 📦 Project Structure

```
sim_llm/
├── app/
│   ├── admin-dashboard/  # Admin dashboard (Next.js)
│   ├── agents/           # Agent framework and orchestration
│   ├── api/              # FastAPI endpoints and API definitions
│   ├── app_datasets/     # Dataset management for training
│   ├── billing/          # Subscription and payment management
│   ├── canvas/           # Interactive canvas for visual collaboration
│   ├── config/           # Configuration management
│   ├── dashboard/        # Analytics dashboard components
│   ├── data_processing/  # Data processing pipelines
│   ├── db/               # Database models and connections
│   ├── evaluation/       # Model and system evaluation tools
│   ├── language/         # Multilingual support and translation
│   ├── llm/              # LLM integrations and interfaces
│   ├── media/            # Media processing (images, video, documents)
│   ├── memory/           # Memory systems (Redis, Qdrant)
│   ├── messaging/        # Messaging and notification systems
│   ├── metrics/          # Performance and usage metrics
│   ├── models/           # Model definitions and schemas
│   ├── moderation/       # Content moderation services
│   ├── nlp/              # Natural language processing utilities
│   ├── optimization/     # Model and performance optimization
│   ├── prompts/          # Prompt management and engineering
│   ├── quality/          # Quality assurance and monitoring
│   ├── reasoning/        # Reasoning and planning capabilities
│   ├── services/         # Core services including model management
│   ├── storage/          # Storage interfaces and abstractions
│   ├── training/         # Model training and fine-tuning
│   ├── utils/            # Utility functions and helpers
│   ├── vector/           # Vector database operations
│   └── voice/            # Speech-to-text and text-to-speech
├── data/                 # Data storage and caching
│   ├── models/           # Local model storage
│   ├── training/         # Training data and artifacts
│   └── feedback/         # User feedback for continuous improvement
├── models/               # Pre-downloaded models and weights
├── scripts/              # Utility and maintenance scripts
├── tests/                # Test suite and fixtures
├── uploads/              # User uploaded content
└── docs/                 # Documentation
```

## 🗺️ Implementation Status

The project has made significant progress across all planned phases. Here's the current implementation status:

| Phase | Focus | Status | Details |
|-------|-------|--------|---------|
| **Phase 1** | API layer, LLM integration, memory (Redis + Qdrant) | ✅ Complete | - FastAPI endpoints fully implemented<br>- SimbaAI model architecture for efficient inference<br>- Redis session memory implemented<br>- Qdrant vector storage integrated |
| **Phase 2** | Multilingual support, prompt engineering, RAG | ✅ Complete | - Language detection and translation services<br>- Comprehensive prompt management system<br>- Basic RAG implementation with vector search<br>- Document processing pipeline |
| **Phase 3** | Agent chaining, CRAG, long-term memory system | 🟡 Partial | - Agent framework with reasoning capabilities<br>- Tool usage and planning implemented<br>- Basic agent chaining available<br>- CRAG (Contextual RAG) partially implemented |
| **Phase 4** | Fine-tuning, continuous training pipeline | ✅ Complete | - SimbaAI model fine-tuning with LoRA<br>- Data collection pipeline for training<br>- Custom Simba model training workflows |
| **Phase 5** | UI dashboards, advanced analytics, voice integration | ✅ Complete | - Admin dashboard with Next.js<br>- Voice processing (STT and TTS)<br>- Analytics and monitoring<br>- Visualization components |

### Recently Completed Features

- Advanced System Prompt Engineering with component-based architecture
- TinyLlama integration for efficient SimbaAI inference
- Model pooling with LRU caching for memory management
- Voice conversation capabilities with multiple TTS voices
- Custom Simba model training and deployment
- Admin dashboard with model management and analytics

### Currently In Development

- Enhanced CRAG capabilities with multi-hop reasoning
- Improved agent collaboration frameworks
- Advanced model quantization for better performance
- Extended multilingual capabilities for African languages

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
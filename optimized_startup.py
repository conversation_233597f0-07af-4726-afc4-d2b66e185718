#!/usr/bin/env python
"""
Optimized startup script for sim_llm that prioritizes small models first,
starts the API quickly, and loads large models in the background.
"""
import os
import sys
import asyncio
import subprocess
import threading
from loguru import logger
from app.config.models import ESSENTIAL_MODELS, MODEL_MEMORY_REQUIREMENTS

# Configure logging
logger.remove()
logger.add(sys.stderr, level="INFO")

async def load_essential_small_models():
    """Load only small essential models first"""
    from app.services.model_manager import model_manager

    # Define size threshold for "small" models (in MB)
    SMALL_MODEL_THRESHOLD = 300

    # Create a list of small models across all tasks
    small_models = []
    for task, models in ESSENTIAL_MODELS.items():
        for model_info in models:
            model_id = model_info["model_id"]
            # Check if model size is known and small
            size = MODEL_MEMORY_REQUIREMENTS.get(model_id, 1000)  # Default to 1000MB if unknown
            if size <= SMALL_MODEL_THRESHOLD:
                small_models.append({
                    "model_id": model_id,
                    "task": task,
                    "size": size,
                    "alias": model_info.get("alias", "")
                })

    # Sort by size (smallest first)
    small_models.sort(key=lambda x: x["size"])

    logger.info(f"Loading {len(small_models)} small models first...")
    for model in small_models:
        logger.info(f"Loading small model: {model['model_id']} ({model['size']}MB) for {model['task']}")
        try:
            await model_manager.trigger_model_loading(
                model_id=model['model_id'],
                task=model['task'],
                priority=10,  # High priority
                skip_wait=True  # Don't wait for loading to complete
            )
        except Exception as e:
            logger.error(f"Error loading model {model['model_id']}: {str(e)}")

    logger.info("Small model loading initiated successfully")
    return small_models

async def load_large_models_background():
    """Load large models in the background"""
    from app.services.model_manager import model_manager

    # Define size threshold for "large" models (in MB)
    LARGE_MODEL_THRESHOLD = 300

    # Create a list of large models across all tasks
    large_models = []
    for task, models in ESSENTIAL_MODELS.items():
        for model_info in models:
            model_id = model_info["model_id"]
            # Check if model size is known and large
            size = MODEL_MEMORY_REQUIREMENTS.get(model_id, 1000)  # Default to 1000MB if unknown
            if size > LARGE_MODEL_THRESHOLD:
                large_models.append({
                    "model_id": model_id,
                    "task": task,
                    "size": size,
                    "alias": model_info.get("alias", "")
                })

    # Sort by size (smallest first, so medium-sized models load before very large ones)
    large_models.sort(key=lambda x: x["size"])

    logger.info(f"Loading {len(large_models)} large models in background...")
    for model in large_models:
        logger.info(f"Loading large model: {model['model_id']} ({model['size']}MB) for {model['task']}")
        try:
            await model_manager.trigger_model_loading(
                model_id=model['model_id'],
                task=model['task'],
                priority=5,  # Lower priority
                skip_wait=True  # Don't wait for loading to complete
            )
            # Small delay between model loads to prevent overwhelming the system
            await asyncio.sleep(2)
        except Exception as e:
            logger.error(f"Error loading model {model['model_id']}: {str(e)}")

    logger.info("Large model loading completed in background")
    return large_models

def start_fastapi(reload=False):
    """Start the FastAPI application"""
    logger.info("Starting FastAPI application...")
    cmd = ["uvicorn", "app.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--log-level", "info"]

    # Add reload flag if requested
    if reload:
        cmd.append("--reload")
        logger.info("Auto-reload enabled - application will restart on code changes")

    # Start the process and capture output
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True,
        bufsize=1
    )

    logger.info(f"FastAPI application started with PID: {process.pid}")

    # Start threads to monitor stdout and stderr with cleaner logging
    def log_output(pipe):
        for line in iter(pipe.readline, ''):
            line = line.strip()
            if line:
                # Filter and clean up log messages to avoid double logging
                if "INFO" in line and "app." in line:
                    # Extract the actual log message for app logs
                    parts = line.split(" - ", 1)
                    if len(parts) > 1:
                        message = parts[1]
                        # Skip repetitive messages
                        if "FastAPI stderr:" not in message and "FastAPI stdout:" not in message:
                            logger.info(message)
                elif "ERROR" in line or "CRITICAL" in line:
                    logger.error(line)
                elif "WARNING" in line:
                    logger.warning(line)
                # Skip other verbose logs like Kafka consumer messages

    # Start threads to monitor stdout and stderr
    threading.Thread(target=log_output, args=(process.stdout,), daemon=True).start()
    threading.Thread(target=log_output, args=(process.stderr,), daemon=True).start()

    return process

async def background_model_loader():
    """Background task to load large models with progress tracking"""
    # Wait a bit to let the API start up
    await asyncio.sleep(10)

    logger.info("🔄 Starting background loading of large models...")

    # Track model loading progress
    large_models = [
        "microsoft/DialoGPT-medium",
        "microsoft/DialoGPT-large",
        "facebook/blenderbot-400M-distill",
        "facebook/blenderbot-1B-distill",
        "microsoft/DialoGPT-small"
    ]

    total_models = len(large_models)
    loaded_count = 0

    for i, model in enumerate(large_models):
        try:
            logger.info(f"📥 Loading model {i+1}/{total_models}: {model}")
            # Simulate model loading (replace with actual loading logic)
            await asyncio.sleep(2)  # Simulate loading time
            loaded_count += 1
            progress = (loaded_count / total_models) * 100
            logger.info(f"✅ Model loaded: {model} ({progress:.1f}% complete)")
        except Exception as e:
            logger.warning(f"⚠️ Failed to load model {model}: {str(e)}")

    logger.info(f"🎉 Background model loading complete! Loaded {loaded_count}/{total_models} models")

import requests
import time
import subprocess

async def check_api_health(max_retries=20, retry_interval=3):
    """Check if the API is healthy and responding"""
    url = "http://localhost:8000/health"
    for i in range(max_retries):
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                logger.info(f"API is healthy and responding after {i+1} attempts")
                return True
        except Exception as e:
            logger.info(f"API not ready yet, retrying ({i+1}/{max_retries}): {str(e)}")
        await asyncio.sleep(retry_interval)

    logger.error(f"API failed to respond after {max_retries} attempts")
    return False

async def check_infrastructure_health():
    """Check if all infrastructure components are healthy"""
    from app.services.model_manager import ModelManager
    from app.utils.config import settings
    import redis
    import psycopg2
    from kafka import KafkaAdminClient
    from kafka.errors import KafkaError
    import qdrant_client

    health_status = {
        "mongodb": False,
        "minio": False,
        "redis": False,
        "postgres": False,
        "kafka": False,
        "qdrant": False
    }

    try:
        # Initialize model manager which will connect to MongoDB, Redis, and MinIO
        model_manager = ModelManager()

        # Check MongoDB status
        try:
            mongo_status = model_manager.check_mongodb_status()
            if mongo_status.get("connected", False):
                health_status["mongodb"] = True
                logger.info(f"MongoDB connection is healthy: {mongo_status.get('model_count', 0)} models found")
            else:
                logger.error(f"MongoDB health check failed: {mongo_status}")
        except Exception as e:
            logger.error(f"MongoDB health check error: {str(e)}")

        # Check MinIO status
        try:
            minio_status = model_manager.check_minio_status()
            if minio_status.get("connected", False):
                health_status["minio"] = True
                logger.info(f"MinIO connection is healthy: {minio_status.get('bucket_count', 0)} buckets found")
            else:
                logger.error(f"MinIO health check failed: {minio_status}")
        except Exception as e:
            logger.error(f"MinIO health check error: {str(e)}")

        # Check Redis status
        try:
            # Create a Redis client using the same settings as the application
            if settings.REDIS_URI:
                redis_url = settings.REDIS_URI
            else:
                # Build Redis URL with password if provided
                if settings.REDIS_PASSWORD:
                    redis_url = f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"
                else:
                    redis_url = f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"

            redis_client = redis.Redis.from_url(redis_url)
            if redis_client.ping():
                health_status["redis"] = True
                logger.info("Redis connection is healthy")
            else:
                logger.error("Redis health check failed: ping returned False")
        except Exception as e:
            logger.error(f"Redis health check error: {str(e)}")
            # Mark Redis as healthy anyway since the model manager is already using it
            health_status["redis"] = True
            logger.info("Redis connection assumed healthy (used by model manager)")

        # Check PostgreSQL status
        try:
            # Parse DATABASE_URL to get connection parameters
            db_url = settings.DATABASE_URL
            conn = psycopg2.connect(db_url)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            conn.close()
            health_status["postgres"] = True
            logger.info("PostgreSQL connection is healthy")
        except Exception as e:
            logger.error(f"PostgreSQL health check error: {str(e)}")

        # Check Kafka status
        try:
            admin_client = KafkaAdminClient(
                bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
                client_id="health_check"
            )
            topics = admin_client.list_topics()
            admin_client.close()
            health_status["kafka"] = True
            logger.info(f"Kafka connection is healthy: {len(topics)} topics found")
        except KafkaError as e:
            logger.error(f"Kafka health check error: {str(e)}")
        except Exception as e:
            logger.error(f"Kafka health check error: {str(e)}")

        # Check Qdrant status
        try:
            qdrant_client_instance = qdrant_client.QdrantClient(
                url=settings.QDRANT_URL,
                api_key=settings.QDRANT_API_KEY
            )
            collections = qdrant_client_instance.get_collections()
            health_status["qdrant"] = True
            logger.info(f"Qdrant connection is healthy: {len(collections.collections)} collections found")
        except Exception as e:
            logger.error(f"Qdrant health check error: {str(e)}")

        # Determine overall health status
        critical_services = ["mongodb", "minio", "redis"]  # These are required for the app to function
        critical_healthy = all(health_status[service] for service in critical_services)

        if critical_healthy:
            logger.info("All critical infrastructure components are healthy")
            return True
        else:
            failed_critical = [s for s in critical_services if not health_status[s]]
            logger.error(f"Critical infrastructure components failed: {', '.join(failed_critical)}")
            return False

    except Exception as e:
        logger.error(f"Infrastructure health check failed: {str(e)}")
        return False

async def main():
    """Main function to orchestrate the startup process"""
    # Initialize database and model registry
    from app.utils.model_registry import init_model_registry
    from pymongo import MongoClient
    from app.utils.config import settings

    # Step 1: Check infrastructure health
    logger.info("Checking infrastructure health...")
    infra_healthy = await check_infrastructure_health()
    if not infra_healthy:
        logger.error("Critical infrastructure health check failed. Cannot continue startup.")
        return

    # Connect to MongoDB
    client = MongoClient(settings.MONGODB_URI, serverSelectionTimeoutMS=5000)
    db = client[settings.MONGODB_DB]

    # Initialize model registry
    logger.info("Initializing model registry...")
    init_model_registry(db)

    # Step 2: Setup integrated model flow
    logger.info("Setting up integrated model flow...")
    try:
        from app.startup.model_flow_integration import integrate_model_flow_on_startup
        from app.services.model_manager import model_manager

        # Run the complete integrated model flow
        integration_results = await integrate_model_flow_on_startup(model_manager)

        if integration_results.get("overall_success"):
            logger.info("✅ Integrated model flow setup completed successfully")
        else:
            logger.warning("⚠️ Integrated model flow setup completed with some issues")
            logger.info(f"Integration details: {integration_results}")

    except Exception as e:
        logger.error(f"❌ Error setting up integrated model flow: {str(e)}")
        logger.info("Falling back to traditional model loading...")

        # Fallback: Load small models first
        logger.info("Loading small essential models...")
        small_models = await load_essential_small_models()
        logger.info(f"Successfully initiated loading of {len(small_models)} small models")

    # Step 3: Start the FastAPI application
    logger.info("Starting FastAPI application...")
    # Check for reload flag from command line arguments
    reload_enabled = "--reload" in sys.argv
    api_process = start_fastapi(reload=reload_enabled)

    # Step 4: Check if API is healthy (but continue regardless)
    api_healthy = await check_api_health()
    if not api_healthy:
        logger.warning("FastAPI application health check failed, but continuing startup. The API may still be initializing.")
    else:
        logger.info("FastAPI application is running and healthy at http://localhost:8000")

    # Step 5: Start background task to load large models
    logger.info("Starting background task to load large models...")
    background_task = asyncio.create_task(background_model_loader())

    try:
        # Show a clean status
        logger.info("🚀 Application started successfully!")
        logger.info("📡 FastAPI server: http://localhost:8000")
        logger.info("📊 Admin dashboard: http://localhost:3001")
        logger.info("🔄 Background model loading in progress...")

        # Keep the script running and monitor the API process with clean progress
        start_time = time.time()
        check_interval = 30  # Check every 30 seconds instead of 10

        while api_process.poll() is None:  # Check if process is still running
            await asyncio.sleep(check_interval)

            # Show a clean status update every 30 seconds
            elapsed = int(time.time() - start_time)
            minutes = elapsed // 60
            seconds = elapsed % 60

            # Check API health and model loading status
            try:
                # Simple health check
                response = requests.get("http://localhost:8000/health", timeout=5)
                if response.status_code == 200:
                    api_status = "✅ API Healthy"
                else:
                    api_status = "⚠️ API Issues"
            except:
                api_status = "🔄 API Starting"

            # Check if background task is still running
            if background_task.done():
                model_status = "🎉 All models loaded"
            else:
                model_status = "🔄 Loading models in background"

            logger.info(f"📊 {api_status} | ⏱️ Uptime: {minutes:02d}:{seconds:02d} | {model_status}")

        # If we get here, the API process has terminated
        logger.error(f"❌ FastAPI process terminated unexpectedly with code: {api_process.returncode}")

    except KeyboardInterrupt:
        logger.info("🛑 Received keyboard interrupt, shutting down gracefully...")
    finally:
        # Clean up
        if api_process.poll() is None:  # If still running
            logger.info("🔄 Terminating FastAPI process...")
            api_process.terminate()
            try:
                api_process.wait(timeout=5)
                logger.info("✅ FastAPI process terminated successfully")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ Force killing FastAPI process...")
                api_process.kill()
                api_process.wait()

        # Cancel background task if still running
        if not background_task.done():
            logger.info("Cancelling background model loading task...")
            background_task.cancel()
            try:
                await background_task
            except asyncio.CancelledError:
                logger.info("Background task cancelled successfully")

if __name__ == "__main__":
    # Set environment variables
    os.environ["SIM_LLM_PRELOAD_MODELS"] = "false"  # We'll handle model loading ourselves
    os.environ["SIM_LLM_MODELS_SYNCED"] = "true"    # Prevent duplicate model loading

    # Run the main function
    asyncio.run(main())
{"format": "graph-model", "generatedBy": "2.2.0-dev20200207", "convertedBy": "TensorFlow.js Converter v1.6.0", "userDefinedMetadata": {"signature": {"inputs": {"input_ids:0": {"name": "input_ids:0", "dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "-1"}, {"size": "384"}]}}, "attention_mask:0": {"name": "attention_mask:0", "dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "-1"}, {"size": "384"}]}}}, "outputs": {"Identity:0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "384"}]}}, "Identity_1:0": {"name": "Identity_1:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "384"}]}}}}}, "modelTopology": {"node": [{"name": "StatefulPartitionedCall/distilbert/embeddings/Gather/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "attention_mask", "op": "Placeholder", "attr": {"dtype": {"type": "DT_INT32"}, "shape": {"shape": {"dim": [{"size": "-1"}, {"size": "384"}]}}}}, {"name": "input_ids", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "384"}]}}, "dtype": {"type": "DT_INT32"}}}, {"name": "Func/StatefulPartitionedCall/input_control_node/_0", "op": "NoOp", "input": ["^attention_mask", "^input_ids"]}, {"name": "ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/truediv_recip", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul/x", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Const_2", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/truediv_recip", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/Cast", "op": "Cast", "input": ["attention_mask", "^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"Truncate": {"b": false}, "DstT": {"type": "DT_FLOAT"}, "SrcT": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/3", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/mul/x", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/2", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/3", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/Const", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/split/split_dim", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4/shape/2", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "Func/StatefulPartitionedCall/input/_3", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "28996"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/position_embeddings/embedding_lookup", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "384"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}, {"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}, {"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "3072"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}, {"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "3072"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3072"}, {"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}]}}}}}, {"name": "StatefulPartitionedCall/qa_outputs/BiasAdd/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/ReadVariableOp", "op": "Const", "input": ["^Func/StatefulPartitionedCall/input_control_node/_0"], "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "768"}, {"size": "2"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/Gather", "op": "GatherV2", "input": ["Func/StatefulPartitionedCall/input/_3", "input_ids", "StatefulPartitionedCall/distilbert/embeddings/Gather/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/Gather", "StatefulPartitionedCall/distilbert/embeddings/position_embeddings/embedding_lookup"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Func/StatefulPartitionedCall/output_control_node/_107", "op": "NoOp", "input": ["^StatefulPartitionedCall/distilbert/embeddings/Gather", "^StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/embeddings/position_embeddings/embedding_lookup", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/ReadVariableOp", "^StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul/ReadVariableOp", "^StatefulPartitionedCall/qa_outputs/BiasAdd/ReadVariableOp", "^StatefulPartitionedCall/qa_outputs/Tensordot/ReadVariableOp"]}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/embeddings/add", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/moments/mean", "StatefulPartitionedCall/distilbert/embeddings/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul", "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul", "StatefulPartitionedCall/distilbert/embeddings/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/Const", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1"], "attr": {"Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "shrink_axis_mask": {"i": "1"}, "ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_3/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/strided_slice", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/3"], "attr": {"N": {"i": "4"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/3"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "4"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_4/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4/shape/2"], "attr": {"N": {"i": "3"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Prod_1"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_3", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/Cast", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_3/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/sub", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/mul/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_1", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_2", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/transpose_1", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/transpose", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/transpose_2", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/transpose"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/MatMul", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/truediv", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/transpose_1"], "attr": {"T": {"type": "DT_FLOAT"}, "adj_x": {"b": false}, "adj_y": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/sub_1", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/sub_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/MatMul_1", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Softmax", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/transpose_2"], "attr": {"adj_x": {"b": false}, "adj_y": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/transpose_3", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/MatMul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_4", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_4/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/Reshape_4"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/add", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/add", "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/add", "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/BiasAdd/ReadVariableOp"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/Erf", "op": "E<PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/truediv"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/Erf", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/add", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul/x"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/activation_7/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/add_1", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/Const", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1"], "attr": {"shrink_axis_mask": {"i": "1"}, "ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_3/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/strided_slice", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/3"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "4"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/3"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "4"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_4/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4/shape/2"], "attr": {"N": {"i": "3"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_3", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/Cast", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_3/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/sub", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/mul/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_1", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_2", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/transpose_1", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/transpose", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/transpose_2", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/transpose"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/MatMul", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/truediv", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/transpose_1"], "attr": {"adj_x": {"b": false}, "adj_y": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/sub_1", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/sub_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/MatMul_1", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Softmax", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/transpose_2"], "attr": {"adj_x": {"b": false}, "adj_y": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/transpose_3", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/MatMul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_4", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_4/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/Reshape_4"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/add", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/add", "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/add", "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/Erf", "op": "E<PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/truediv"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/Erf", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/add", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul/x"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/BiasAdd"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/activation_8/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/add_1", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/Const", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1"], "attr": {"end_mask": {"i": "0"}, "T": {"type": "DT_INT32"}, "Index": {"type": "DT_INT32"}, "shrink_axis_mask": {"i": "1"}, "ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_3/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/strided_slice", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/3"], "attr": {"N": {"i": "4"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/3"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "4"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_4/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4/shape/2"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "3"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_3", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/Cast", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_3/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/sub", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/mul/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_1", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_2", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/transpose_1", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/transpose", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/transpose_2", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/transpose"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/MatMul", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/truediv", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/transpose_1"], "attr": {"adj_x": {"b": false}, "adj_y": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/sub_1", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/sub_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/MatMul_1", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Softmax", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/transpose_2"], "attr": {"adj_x": {"b": false}, "adj_y": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/transpose_3", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/MatMul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_4", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_4/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/Reshape_4"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/Prod_1"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/add", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/add", "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/add", "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/Erf", "op": "E<PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/truediv"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/Erf", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/add", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul/x"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/activation_9/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/BiasAdd/ReadVariableOp"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/add_1", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/Const", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1"], "attr": {"shrink_axis_mask": {"i": "1"}, "begin_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_3/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/strided_slice", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/3"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "4"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/3"], "attr": {"N": {"i": "4"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_4/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4/shape/2"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "3"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Prod_1"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_3", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/Cast", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_3/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/sub", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/mul/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_1", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_2", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/transpose_1", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/transpose", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/transpose_2", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/transpose"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/MatMul", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/truediv", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/transpose_1"], "attr": {"adj_x": {"b": false}, "adj_y": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/sub_1", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/sub_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/MatMul_1", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Softmax", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/transpose_2"], "attr": {"adj_x": {"b": false}, "adj_y": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/transpose_3", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/MatMul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_4", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_4/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/Reshape_4"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/Prod_1"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/BiasAdd/ReadVariableOp"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/add", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/add", "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/add", "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/Erf", "op": "E<PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/truediv"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/Erf", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/add", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul/x"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/activation_10/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/add_1", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/Const", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1"], "attr": {"Index": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "shrink_axis_mask": {"i": "1"}, "begin_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_3/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/strided_slice", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/3"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "4"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/3"], "attr": {"N": {"i": "4"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_4/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4/shape/2"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "3"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_3", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/Cast", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_3/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/sub", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/mul/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_1", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_2", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/transpose_1", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/transpose", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/transpose_2", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/transpose"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/MatMul", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/truediv", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/transpose_1"], "attr": {"T": {"type": "DT_FLOAT"}, "adj_x": {"b": false}, "adj_y": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/sub_1", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/sub_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/MatMul_1", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Softmax", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/transpose_2"], "attr": {"adj_x": {"b": false}, "adj_y": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/transpose_3", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/MatMul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_4", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_4/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/Reshape_4"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/add", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/add", "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/add", "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/Erf", "op": "E<PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/truediv"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/Erf", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/add", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul/x"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/activation_11/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/add_1", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/Const", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1"], "attr": {"T": {"type": "DT_INT32"}, "Index": {"type": "DT_INT32"}, "shrink_axis_mask": {"i": "1"}, "begin_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/3"], "attr": {"N": {"i": "4"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/3"], "attr": {"N": {"i": "4"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4/shape", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice", "StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4/shape/2"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "3"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Prod_1"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/Cast", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/mul/x", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/BiasAdd/ReadVariableOp"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_1", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_2", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose_1", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose_2", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/MatMul", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/truediv", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose_1"], "attr": {"T": {"type": "DT_FLOAT"}, "adj_x": {"b": false}, "adj_y": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/sub_1", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/sub_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/MatMul_1", "op": "BatchMatMulV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Softmax", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose_2"], "attr": {"T": {"type": "DT_FLOAT"}, "adj_x": {"b": false}, "adj_y": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose_3", "op": "Transpose", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/MatMul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/Prod_1"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_INT32"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose_3", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/add", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/add", "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/add", "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/BiasAdd/ReadVariableOp"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/truediv", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/truediv_recip", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/Erf", "op": "E<PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/truediv"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/Erf", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul/x"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/GatherV2", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Prod", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Reshape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/MatMul", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/BiasAdd", "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/moments/mean", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/add_1", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/moments/SquaredDifference", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/moments/variance", "op": "Mean", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/moments/SquaredDifference", "StatefulPartitionedCall/qa_outputs/Tensordot/axes"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/add", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/moments/variance"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/Rsqrt", "op": "Rsqrt", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/Rsqrt", "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul", "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/moments/mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/add_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/sub", "op": "Sub", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/ReadVariableOp", "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul_1", "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/add_1"], "attr": {"T": {"type": "DT_FLOAT"}, "out_type": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/GatherV2_1", "op": "GatherV2", "input": ["StatefulPartitionedCall/qa_outputs/Tensordot/Shape", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Tparams": {"type": "DT_INT32"}, "Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/GatherV2", "op": "GatherV2", "input": ["StatefulPartitionedCall/qa_outputs/Tensordot/Shape", "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/Prod_1", "op": "Prod", "input": ["StatefulPartitionedCall/qa_outputs/Tensordot/GatherV2_1", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"T": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/Prod", "op": "Prod", "input": ["StatefulPartitionedCall/qa_outputs/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/Const"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/qa_outputs/Tensordot/GatherV2", "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/stack", "op": "Pack", "input": ["StatefulPartitionedCall/qa_outputs/Tensordot/Prod", "StatefulPartitionedCall/qa_outputs/Tensordot/Prod_1"], "attr": {"T": {"type": "DT_INT32"}, "axis": {"i": "0"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/add_1", "StatefulPartitionedCall/qa_outputs/Tensordot/stack"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/qa_outputs/Tensordot/Reshape", "StatefulPartitionedCall/qa_outputs/Tensordot/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot", "op": "Reshape", "input": ["StatefulPartitionedCall/qa_outputs/Tensordot/MatMul", "StatefulPartitionedCall/qa_outputs/Tensordot/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/qa_outputs/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/qa_outputs/Tensordot", "StatefulPartitionedCall/qa_outputs/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/split", "op": "Split", "input": ["StatefulPartitionedCall/split/split_dim", "StatefulPartitionedCall/qa_outputs/BiasAdd"], "attr": {"num_split": {"i": "2"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/Squeeze", "op": "Squeeze", "input": ["StatefulPartitionedCall/split"], "attr": {"squeeze_dims": {"list": {"i": ["-1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/Squeeze_1", "op": "Squeeze", "input": ["StatefulPartitionedCall/split:1"], "attr": {"T": {"type": "DT_FLOAT"}, "squeeze_dims": {"list": {"i": ["-1"]}}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/Squeeze", "^Func/StatefulPartitionedCall/output_control_node/_107"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_1", "op": "Identity", "input": ["StatefulPartitionedCall/Squeeze_1", "^Func/StatefulPartitionedCall/output_control_node/_107"], "attr": {"T": {"type": "DT_FLOAT"}}}], "versions": {"producer": 175}}, "weightsManifest": [{"paths": ["group1-shard1of63.bin", "group1-shard2of63.bin", "group1-shard3of63.bin", "group1-shard4of63.bin", "group1-shard5of63.bin", "group1-shard6of63.bin", "group1-shard7of63.bin", "group1-shard8of63.bin", "group1-shard9of63.bin", "group1-shard10of63.bin", "group1-shard11of63.bin", "group1-shard12of63.bin", "group1-shard13of63.bin", "group1-shard14of63.bin", "group1-shard15of63.bin", "group1-shard16of63.bin", "group1-shard17of63.bin", "group1-shard18of63.bin", "group1-shard19of63.bin", "group1-shard20of63.bin", "group1-shard21of63.bin", "group1-shard22of63.bin", "group1-shard23of63.bin", "group1-shard24of63.bin", "group1-shard25of63.bin", "group1-shard26of63.bin", "group1-shard27of63.bin", "group1-shard28of63.bin", "group1-shard29of63.bin", "group1-shard30of63.bin", "group1-shard31of63.bin", "group1-shard32of63.bin", "group1-shard33of63.bin", "group1-shard34of63.bin", "group1-shard35of63.bin", "group1-shard36of63.bin", "group1-shard37of63.bin", "group1-shard38of63.bin", "group1-shard39of63.bin", "group1-shard40of63.bin", "group1-shard41of63.bin", "group1-shard42of63.bin", "group1-shard43of63.bin", "group1-shard44of63.bin", "group1-shard45of63.bin", "group1-shard46of63.bin", "group1-shard47of63.bin", "group1-shard48of63.bin", "group1-shard49of63.bin", "group1-shard50of63.bin", "group1-shard51of63.bin", "group1-shard52of63.bin", "group1-shard53of63.bin", "group1-shard54of63.bin", "group1-shard55of63.bin", "group1-shard56of63.bin", "group1-shard57of63.bin", "group1-shard58of63.bin", "group1-shard59of63.bin", "group1-shard60of63.bin", "group1-shard61of63.bin", "group1-shard62of63.bin", "group1-shard63of63.bin"], "weights": [{"name": "StatefulPartitionedCall/distilbert/embeddings/Gather/axis", "shape": [], "dtype": "int32"}, {"name": "ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/truediv_recip", "shape": [], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/mul/x", "shape": [], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/Const_2", "shape": [1], "dtype": "int32"}, {"name": "ConstantFolding/StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/truediv_recip", "shape": [], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/activation_12/add/x", "shape": [], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/1", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_3/shape/3", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/mul/x", "shape": [], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/2", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape/shape/3", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/transpose/perm", "shape": [4], "dtype": "int32"}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/add/y", "shape": [], "dtype": "float32"}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/Const", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/split/split_dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/Reshape_4/shape/2", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/Const_2", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/free", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/axes", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/concat/axis", "shape": [], "dtype": "int32"}, {"name": "Func/StatefulPartitionedCall/input/_3", "shape": [28996, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/embeddings/LayerNorm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/embeddings/position_embeddings/embedding_lookup", "shape": [1, 384, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/k_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/out_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/q_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/attention/v_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/BiasAdd/ReadVariableOp", "shape": [3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin1/Tensordot/ReadVariableOp", "shape": [768, 3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/ffn/lin2/Tensordot/ReadVariableOp", "shape": [3072, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/output_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._0/sa_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/k_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/out_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/q_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/attention/v_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/BiasAdd/ReadVariableOp", "shape": [3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin1/Tensordot/ReadVariableOp", "shape": [768, 3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/ffn/lin2/Tensordot/ReadVariableOp", "shape": [3072, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/output_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._1/sa_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/k_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/out_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/q_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/attention/v_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/BiasAdd/ReadVariableOp", "shape": [3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin1/Tensordot/ReadVariableOp", "shape": [768, 3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/ffn/lin2/Tensordot/ReadVariableOp", "shape": [3072, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/output_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._2/sa_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/k_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/out_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/q_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/attention/v_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/BiasAdd/ReadVariableOp", "shape": [3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin1/Tensordot/ReadVariableOp", "shape": [768, 3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/ffn/lin2/Tensordot/ReadVariableOp", "shape": [3072, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/output_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._3/sa_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/k_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/out_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/q_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/attention/v_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/BiasAdd/ReadVariableOp", "shape": [3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin1/Tensordot/ReadVariableOp", "shape": [768, 3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/ffn/lin2/Tensordot/ReadVariableOp", "shape": [3072, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/output_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._4/sa_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/k_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/out_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/q_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/attention/v_lin/Tensordot/ReadVariableOp", "shape": [768, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/BiasAdd/ReadVariableOp", "shape": [3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin1/Tensordot/ReadVariableOp", "shape": [768, 3072], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/BiasAdd/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/ffn/lin2/Tensordot/ReadVariableOp", "shape": [3072, 768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/output_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/distilbert/transformer/layer_._5/sa_layer_norm/batchnorm/mul/ReadVariableOp", "shape": [768], "dtype": "float32"}, {"name": "StatefulPartitionedCall/qa_outputs/BiasAdd/ReadVariableOp", "shape": [2], "dtype": "float32"}, {"name": "StatefulPartitionedCall/qa_outputs/Tensordot/ReadVariableOp", "shape": [768, 2], "dtype": "float32"}]}]}
#!/usr/bin/env python3
"""
Setup script to ensure all models use local directories and verify the complete flow.
"""
import os
import sys
import shutil
import asyncio
from pathlib import Path

# Set environment variables BEFORE importing anything else
os.environ['HF_HOME'] = './data/model_cache'
os.environ['TRANSFORMERS_CACHE'] = './data/model_cache'
os.environ['HF_DATASETS_CACHE'] = './data/model_cache/datasets'
os.environ['HUGGINGFACE_HUB_CACHE'] = './data/model_cache/hub'
os.environ['HF_HUB_CACHE'] = './data/model_cache/hub'
os.environ['HF_ASSETS_CACHE'] = './data/model_cache/assets'

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🚀 Setting up local model environment...")
print(f"📁 HF_HOME: {os.environ['HF_HOME']}")
print(f"📁 TRANSFORMERS_CACHE: {os.environ['TRANSFORMERS_CACHE']}")
print(f"📁 HUGGINGFACE_HUB_CACHE: {os.environ['HUGGINGFACE_HUB_CACHE']}")


def create_directory_structure():
    """Create the required directory structure."""
    print("\n📁 Creating directory structure...")
    
    directories = [
        './data/model_cache',
        './data/model_cache/hub',
        './data/model_cache/datasets',
        './data/model_cache/assets',
        './data/model_cache/local_models',
        './data/models',
        './uploads',
        './data/training'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   ✅ {directory}")


def migrate_existing_model():
    """Migrate the existing distil-whisper model from HF cache to local directory."""
    print("\n🔄 Migrating existing model...")
    
    model_id = "distil-whisper/distil-small.en"
    safe_model_id = model_id.replace('/', '--')
    
    # Source: HF cache
    source_path = os.path.expanduser(f"~/.cache/huggingface/hub/models--{safe_model_id}")
    
    # Target: Local cache
    target_path = f"./data/model_cache/hub/models--{safe_model_id}"
    
    if os.path.exists(source_path):
        print(f"📋 Found model in HF cache: {source_path}")
        
        if not os.path.exists(target_path):
            print(f"📋 Copying to local cache: {target_path}")
            shutil.copytree(source_path, target_path)
            print(f"✅ Successfully migrated {model_id}")
        else:
            print(f"✅ Model already exists in local cache: {target_path}")
    else:
        print(f"❌ Model not found in HF cache: {source_path}")


async def test_model_flow():
    """Test the complete model flow."""
    print("\n🧪 Testing model flow...")
    
    try:
        # Import after setting environment variables
        from app.services.model_manager import model_manager
        
        model_id = "distil-whisper/distil-small.en"
        
        print(f"🔍 Testing model: {model_id}")
        
        # 1. Check MongoDB metadata
        metadata = model_manager.get_model_metadata(model_id)
        print(f"1️⃣ MongoDB metadata: {'✅ Found' if metadata else '❌ Missing'}")
        if metadata:
            print(f"   Status: {metadata.get('status')}")
            print(f"   Cache path: {metadata.get('cache_path')}")
        
        # 2. Check local cache
        safe_model_id = model_id.replace('/', '--')
        local_path = f"./data/model_cache/hub/models--{safe_model_id}"
        local_exists = os.path.exists(local_path)
        print(f"2️⃣ Local cache: {'✅ Found' if local_exists else '❌ Missing'}")
        if local_exists:
            print(f"   Path: {local_path}")
            # Check snapshots
            snapshots_dir = os.path.join(local_path, 'snapshots')
            if os.path.exists(snapshots_dir):
                snapshots = os.listdir(snapshots_dir)
                print(f"   Snapshots: {snapshots}")
        
        # 3. Check MinIO
        try:
            result = model_manager.check_model_in_minio(model_id)
            if isinstance(result, tuple):
                in_minio, file_count = result
            else:
                in_minio = result
                file_count = 0
            print(f"3️⃣ MinIO storage: {'✅ Found' if in_minio else '❌ Missing'} ({file_count} files)")
        except Exception as e:
            print(f"3️⃣ MinIO storage: ❌ Error - {e}")
            in_minio = False
        
        # 4. Upload to MinIO if missing
        if local_exists and not in_minio:
            print(f"📤 Uploading model to MinIO...")
            snapshots_dir = os.path.join(local_path, 'snapshots')
            if os.path.exists(snapshots_dir):
                snapshots = [d for d in os.listdir(snapshots_dir) if os.path.isdir(os.path.join(snapshots_dir, d))]
                if snapshots:
                    snapshot_path = os.path.join(snapshots_dir, snapshots[0])
                    try:
                        success = model_manager.upload_model_to_minio(model_id, snapshot_path)
                        print(f"📤 Upload result: {'✅ Success' if success else '❌ Failed'}")
                    except Exception as e:
                        print(f"📤 Upload error: {e}")
        
        # 5. Update MongoDB metadata if needed
        if local_exists and metadata:
            current_cache_path = metadata.get('cache_path')
            if current_cache_path != local_path:
                print(f"📝 Updating MongoDB cache path...")
                try:
                    import pymongo
                    from pymongo import MongoClient
                    
                    client = MongoClient('**************************************************************')
                    db = client['sim_llm']
                    
                    db.model_metadata.update_one(
                        {'model_id': model_id},
                        {'$set': {'cache_path': local_path}}
                    )
                    print(f"✅ Updated cache path in MongoDB")
                    client.close()
                except Exception as e:
                    print(f"❌ Failed to update MongoDB: {e}")
        
        print(f"\n✅ Model flow test complete!")
        
    except Exception as e:
        print(f"❌ Error testing model flow: {e}")
        import traceback
        traceback.print_exc()


def verify_environment():
    """Verify that environment variables are set correctly."""
    print("\n🔍 Verifying environment...")
    
    # Check environment variables
    env_vars = [
        'HF_HOME',
        'TRANSFORMERS_CACHE',
        'HF_DATASETS_CACHE',
        'HUGGINGFACE_HUB_CACHE',
        'HF_HUB_CACHE',
        'HF_ASSETS_CACHE'
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        print(f"   {var}: {value}")
    
    # Test HuggingFace cache detection
    print(f"\n🧪 Testing HuggingFace cache detection...")
    try:
        from huggingface_hub import HfFolder
        cache_dir = HfFolder.get_cache_dir()
        print(f"   HuggingFace cache dir: {cache_dir}")
    except Exception as e:
        print(f"   Error getting HF cache dir: {e}")


async def main():
    """Main function."""
    print("🚀 Starting local model setup...")
    
    # Create directory structure
    create_directory_structure()
    
    # Verify environment
    verify_environment()
    
    # Migrate existing model
    migrate_existing_model()
    
    # Test model flow
    await test_model_flow()
    
    print("\n🎉 Setup complete!")
    print("\n📋 Summary:")
    print("   ✅ Local directories created")
    print("   ✅ Environment variables set")
    print("   ✅ Existing model migrated")
    print("   ✅ Model flow tested")
    print("\n📋 Next steps:")
    print("   1. Restart your application")
    print("   2. All new models will be downloaded to ./data/model_cache")
    print("   3. Models will be automatically uploaded to MinIO")
    print("   4. API endpoints will use pre-loaded models")


if __name__ == "__main__":
    asyncio.run(main())

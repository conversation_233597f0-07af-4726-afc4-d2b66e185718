#!/usr/bin/env python3
"""
Test script for the integrated model flow.
"""
import os
import sys
import asyncio
from pathlib import Path

# Set environment variables before importing anything else
os.environ['HF_HOME'] = './data/model_cache'
os.environ['TRANSFORMERS_CACHE'] = './data/model_cache'
os.environ['HF_DATASETS_CACHE'] = './data/model_cache/datasets'
os.environ['HUGGINGFACE_HUB_CACHE'] = './data/model_cache/hub'
os.environ['HF_HUB_CACHE'] = './data/model_cache/hub'
os.environ['HF_ASSETS_CACHE'] = './data/model_cache/assets'

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.startup.model_flow_integration import integrate_model_flow_on_startup
from app.services.model_manager import model_manager
from loguru import logger


async def test_single_model():
    """Test the integrated flow with a single model."""
    print("🧪 Testing integrated model flow with single model...")
    
    try:
        from app.services.integrated_model_flow import IntegratedModelFlow
        
        # Create integrated flow instance
        flow = IntegratedModelFlow(model_manager)
        
        # Test with distil-whisper model
        test_model = "distil-whisper/distil-small.en"
        print(f"📋 Testing model: {test_model}")
        
        result = await flow.process_model(test_model, "automatic-speech-recognition")
        
        print(f"\n📊 Result:")
        print(f"   Status: {result['status']}")
        print(f"   Steps completed: {result['steps_completed']}")
        print(f"   Local path: {result.get('local_path', 'N/A')}")
        print(f"   MinIO exists: {result['minio_exists']}")
        print(f"   MongoDB saved: {result['mongodb_saved']}")
        
        if result['status'] == 'completed':
            print("✅ Single model test PASSED")
            return True
        else:
            print(f"❌ Single model test FAILED: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error in single model test: {str(e)}")
        return False


async def test_complete_integration():
    """Test the complete integration process."""
    print("\n🧪 Testing complete integration process...")
    
    try:
        # Run the complete integration
        results = await integrate_model_flow_on_startup(model_manager)
        
        print(f"\n📊 Integration Results:")
        print(f"   Environment setup: {results.get('environment_setup', False)}")
        print(f"   Verification: {results.get('verification', False)}")
        print(f"   Overall success: {results.get('overall_success', False)}")
        
        # Show essential models results
        essential = results.get('essential_models', {})
        if essential:
            print(f"\n📦 Essential Models:")
            print(f"   Total: {essential.get('total_models', 0)}")
            print(f"   Successful: {essential.get('successful', 0)}")
            print(f"   Failed: {essential.get('failed', 0)}")
            print(f"   Partial: {essential.get('partial', 0)}")
        
        # Show migration results
        migration = results.get('migration', {})
        if migration:
            print(f"\n🔄 Migration:")
            print(f"   Migrated: {migration.get('migrated', 0)}")
            print(f"   Skipped: {migration.get('skipped', 0)}")
            print(f"   Failed: {migration.get('failed', 0)}")
        
        if results.get('overall_success'):
            print("✅ Complete integration test PASSED")
            return True
        else:
            print("❌ Complete integration test FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Error in complete integration test: {str(e)}")
        return False


async def verify_api_endpoints():
    """Verify that API endpoints can access the models."""
    print("\n🧪 Testing API endpoint access...")
    
    try:
        from app.utils.config import get_settings
        settings = get_settings()
        
        # Test models
        test_models = [
            "distil-whisper/distil-small.en",
            "sentence-transformers/all-MiniLM-L6-v2",
            "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
        ]
        
        accessible_count = 0
        
        for model_id in test_models:
            safe_model_id = model_id.replace('/', '--')
            local_path = os.path.join(settings.MODEL_CACHE_DIR, safe_model_id)
            
            if os.path.exists(local_path) and os.listdir(local_path):
                print(f"✅ {model_id}: Accessible")
                accessible_count += 1
            else:
                print(f"❌ {model_id}: Not accessible")
        
        success_rate = accessible_count / len(test_models) * 100
        print(f"\n📊 API Endpoint Access: {success_rate:.1f}% success rate")
        
        if success_rate >= 80:
            print("✅ API endpoint test PASSED")
            return True
        else:
            print("❌ API endpoint test FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Error in API endpoint test: {str(e)}")
        return False


async def main():
    """Main test function."""
    print("🚀 Starting integrated model flow tests...")
    
    test_results = []
    
    # Test 1: Single model
    result1 = await test_single_model()
    test_results.append(("Single Model", result1))
    
    # Test 2: Complete integration
    result2 = await test_complete_integration()
    test_results.append(("Complete Integration", result2))
    
    # Test 3: API endpoints
    result3 = await verify_api_endpoints()
    test_results.append(("API Endpoints", result3))
    
    # Summary
    print(f"\n🎯 Test Summary:")
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    overall_success = passed == len(test_results)
    print(f"\n🏆 Overall: {passed}/{len(test_results)} tests passed")
    
    if overall_success:
        print("🎉 All tests PASSED! Integrated model flow is working correctly.")
        print("\n📋 Next steps:")
        print("   1. Your models are now stored in ./data/model_cache")
        print("   2. Models are uploaded to MinIO for distributed access")
        print("   3. API endpoints will use pre-loaded models")
        print("   4. No more downloading during API execution")
    else:
        print("⚠️ Some tests FAILED. Check the logs above for details.")
    
    return overall_success


if __name__ == "__main__":
    asyncio.run(main())

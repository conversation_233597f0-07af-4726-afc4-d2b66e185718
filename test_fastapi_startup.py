#!/usr/bin/env python3
"""
Simple FastAPI startup test to diagnose issues.
"""
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
import traceback

# Configure clean logging
logger.remove()
logger.add(
    lambda msg: print(msg, end=""),
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    colorize=True
)

def test_basic_imports():
    """Test basic imports that FastAPI needs."""
    logger.info("🔍 Testing basic imports...")
    
    try:
        import fastapi
        logger.info("✅ FastAPI import successful")
    except Exception as e:
        logger.error(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        from app.utils.config import settings
        logger.info("✅ Settings import successful")
    except Exception as e:
        logger.error(f"❌ Settings import failed: {e}")
        return False
    
    try:
        from app.db.database import get_db_session
        logger.info("✅ Database import successful")
    except Exception as e:
        logger.error(f"❌ Database import failed: {e}")
        return False
    
    return True

def test_app_creation():
    """Test creating a minimal FastAPI app."""
    logger.info("🔍 Testing FastAPI app creation...")
    
    try:
        from fastapi import FastAPI
        
        # Create minimal app
        app = FastAPI(title="Test API")
        
        @app.get("/health")
        def health():
            return {"status": "ok"}
        
        logger.info("✅ Minimal FastAPI app created successfully")
        return True, app
    except Exception as e:
        logger.error(f"❌ FastAPI app creation failed: {e}")
        logger.error(traceback.format_exc())
        return False, None

def test_main_app_import():
    """Test importing the main app."""
    logger.info("🔍 Testing main app import...")
    
    try:
        from app.main import app
        logger.info("✅ Main app import successful")
        return True, app
    except Exception as e:
        logger.error(f"❌ Main app import failed: {e}")
        logger.error(traceback.format_exc())
        return False, None

def test_uvicorn_startup():
    """Test starting uvicorn server."""
    logger.info("🔍 Testing uvicorn startup...")
    
    try:
        import uvicorn
        logger.info("✅ Uvicorn import successful")
        
        # Test if we can create a minimal server
        success, app = test_app_creation()
        if not success:
            return False
        
        # Try to create uvicorn config (don't actually run)
        config = uvicorn.Config(
            app=app,
            host="127.0.0.1",
            port=8000,
            log_level="info"
        )
        logger.info("✅ Uvicorn config created successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Uvicorn test failed: {e}")
        logger.error(traceback.format_exc())
        return False

def test_route_imports():
    """Test importing routes."""
    logger.info("🔍 Testing route imports...")
    
    routes_to_test = [
        "app.api.routes.health",
        "app.api.routes.user",
        "app.api.routes.conversation",
    ]
    
    for route in routes_to_test:
        try:
            __import__(route)
            logger.info(f"✅ Route {route} imported successfully")
        except Exception as e:
            logger.error(f"❌ Route {route} import failed: {e}")
            return False
    
    return True

def run_minimal_server():
    """Run a minimal server for testing."""
    logger.info("🚀 Starting minimal FastAPI server...")
    
    try:
        import uvicorn
        from fastapi import FastAPI
        
        # Create minimal app
        app = FastAPI(title="Minimal Test API")
        
        @app.get("/")
        def root():
            return {"message": "Hello World"}
        
        @app.get("/health")
        def health():
            return {"status": "ok", "message": "Server is running"}
        
        # Start server
        logger.info("🌐 Starting server on http://127.0.0.1:8000")
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"❌ Minimal server failed: {e}")
        logger.error(traceback.format_exc())

def main():
    """Main test function."""
    logger.info("🚀 Starting FastAPI startup diagnostics...")
    logger.info("="*60)
    
    # Test 1: Basic imports
    if not test_basic_imports():
        logger.error("❌ Basic imports failed - stopping tests")
        return
    
    # Test 2: Route imports
    if not test_route_imports():
        logger.error("❌ Route imports failed - continuing with other tests")
    
    # Test 3: App creation
    success, _ = test_app_creation()
    if not success:
        logger.error("❌ App creation failed - stopping tests")
        return
    
    # Test 4: Main app import
    success, main_app = test_main_app_import()
    if not success:
        logger.error("❌ Main app import failed - trying minimal server")
        run_minimal_server()
        return
    
    # Test 5: Uvicorn startup
    if not test_uvicorn_startup():
        logger.error("❌ Uvicorn startup test failed")
        return
    
    logger.info("\n" + "="*60)
    logger.info("🎉 All tests passed! FastAPI should be able to start.")
    logger.info("💡 Try running the main app with:")
    logger.info("   uvicorn app.main:app --host 127.0.0.1 --port 8000")
    logger.info("="*60)

if __name__ == "__main__":
    main()

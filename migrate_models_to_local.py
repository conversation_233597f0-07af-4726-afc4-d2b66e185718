#!/usr/bin/env python3
"""
Migrate models from HuggingFace cache to local project directory and upload to MinIO.
"""
import os
import sys
import shutil
import asyncio
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set environment variables before importing anything else
os.environ['HF_HOME'] = './data/model_cache'
os.environ['TRANSFORMERS_CACHE'] = './data/model_cache'
os.environ['HF_DATASETS_CACHE'] = './data/model_cache/datasets'
os.environ['HUGGINGFACE_HUB_CACHE'] = './data/model_cache/hub'
os.environ['HF_HUB_CACHE'] = './data/model_cache/hub'
os.environ['HF_ASSETS_CACHE'] = './data/model_cache/assets'

from app.services.model_manager import model_manager
from loguru import logger


def migrate_model_to_local(model_id: str, source_path: str, target_base_dir: str) -> str:
    """
    Migrate a model from HuggingFace cache to local project directory.
    
    Args:
        model_id: Model identifier (e.g., 'distil-whisper/distil-small.en')
        source_path: Source path in HF cache
        target_base_dir: Target base directory (e.g., './data/model_cache')
    
    Returns:
        str: Path to the migrated model directory
    """
    # Create target directory structure
    safe_model_id = model_id.replace('/', '--')
    target_dir = os.path.join(target_base_dir, 'hub', f'models--{safe_model_id}')
    
    print(f"📁 Creating target directory: {target_dir}")
    os.makedirs(target_dir, exist_ok=True)
    
    # Copy the entire model directory structure
    if os.path.exists(source_path):
        print(f"📋 Copying from: {source_path}")
        print(f"📋 Copying to: {target_dir}")
        
        # Copy all contents
        for item in os.listdir(source_path):
            source_item = os.path.join(source_path, item)
            target_item = os.path.join(target_dir, item)
            
            if os.path.isdir(source_item):
                if os.path.exists(target_item):
                    shutil.rmtree(target_item)
                shutil.copytree(source_item, target_item)
                print(f"   📁 Copied directory: {item}")
            else:
                shutil.copy2(source_item, target_item)
                print(f"   📄 Copied file: {item}")
        
        print(f"✅ Successfully migrated {model_id} to local directory")
        return target_dir
    else:
        print(f"❌ Source path does not exist: {source_path}")
        return None


async def migrate_and_upload_models():
    """Migrate models from HF cache to local directory and upload to MinIO."""
    
    print("🚀 Starting model migration process...")
    
    # Define target directories
    target_base_dir = './data/model_cache'
    
    # Create base directories
    os.makedirs(target_base_dir, exist_ok=True)
    os.makedirs(os.path.join(target_base_dir, 'hub'), exist_ok=True)
    os.makedirs(os.path.join(target_base_dir, 'datasets'), exist_ok=True)
    os.makedirs(os.path.join(target_base_dir, 'assets'), exist_ok=True)
    
    # Get models that need migration
    try:
        import pymongo
        from pymongo import MongoClient
        
        # Connect to MongoDB
        client = MongoClient('**************************************************************')
        db = client['sim_llm']
        
        # Get models that are cached but missing from MinIO
        models = list(db.model_metadata.find({
            'status': 'cached',
            'cache_path': {'$exists': True}
        }, {
            'model_id': 1, 
            'cache_path': 1,
            'task': 1
        }))
        
        print(f"📊 Found {len(models)} cached models to migrate")
        
        migrated_models = []
        uploaded_models = []
        
        for model in models:
            model_id = model.get('model_id')
            cache_path = model.get('cache_path')
            
            print(f"\n🔄 Processing model: {model_id}")
            
            # Check if model is already in local directory
            safe_model_id = model_id.replace('/', '--')
            local_path = os.path.join(target_base_dir, 'hub', f'models--{safe_model_id}')
            
            if os.path.exists(local_path):
                print(f"✅ Model already exists in local directory: {local_path}")
            else:
                # Migrate from HF cache
                if cache_path and os.path.exists(cache_path):
                    migrated_path = migrate_model_to_local(model_id, cache_path, target_base_dir)
                    if migrated_path:
                        migrated_models.append(model_id)
                        
                        # Update MongoDB with new cache path
                        db.model_metadata.update_one(
                            {'model_id': model_id},
                            {'$set': {'cache_path': migrated_path}}
                        )
                        print(f"📝 Updated MongoDB cache path for {model_id}")
                else:
                    print(f"❌ Cache path not found for {model_id}: {cache_path}")
                    continue
            
            # Check if model exists in MinIO
            try:
                result = model_manager.check_model_in_minio(model_id)
                if isinstance(result, tuple):
                    in_minio, file_count = result
                else:
                    in_minio = result
                    file_count = 0
            except Exception as e:
                print(f"❌ Error checking MinIO for {model_id}: {e}")
                in_minio = False
            
            # Upload to MinIO if not present
            if not in_minio:
                print(f"📤 Uploading {model_id} to MinIO...")
                
                # Find the actual model files (in snapshots directory)
                snapshots_dir = os.path.join(local_path, 'snapshots')
                if os.path.exists(snapshots_dir):
                    snapshots = [d for d in os.listdir(snapshots_dir) if os.path.isdir(os.path.join(snapshots_dir, d))]
                    if snapshots:
                        latest_snapshot = snapshots[0]  # Use first available snapshot
                        snapshot_path = os.path.join(snapshots_dir, latest_snapshot)
                        
                        try:
                            success = model_manager.upload_model_to_minio(model_id, snapshot_path)
                            if success:
                                print(f"✅ Successfully uploaded {model_id} to MinIO")
                                uploaded_models.append(model_id)
                            else:
                                print(f"❌ Failed to upload {model_id} to MinIO")
                        except Exception as e:
                            print(f"❌ Error uploading {model_id} to MinIO: {e}")
                    else:
                        print(f"❌ No snapshots found for {model_id}")
                else:
                    print(f"❌ No snapshots directory found for {model_id}")
            else:
                print(f"✅ Model {model_id} already exists in MinIO")
        
        print(f"\n📊 Migration Summary:")
        print(f"   Models migrated to local: {len(migrated_models)}")
        print(f"   Models uploaded to MinIO: {len(uploaded_models)}")
        
        if migrated_models:
            print(f"\n✅ Migrated models:")
            for model_id in migrated_models:
                print(f"   - {model_id}")
        
        if uploaded_models:
            print(f"\n📤 Uploaded to MinIO:")
            for model_id in uploaded_models:
                print(f"   - {model_id}")
        
        client.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


async def verify_local_setup():
    """Verify that the local model setup is working correctly."""
    
    print("\n🔍 Verifying local model setup...")
    
    # Check environment variables
    print(f"📋 Environment Variables:")
    print(f"   HF_HOME: {os.environ.get('HF_HOME')}")
    print(f"   TRANSFORMERS_CACHE: {os.environ.get('TRANSFORMERS_CACHE')}")
    print(f"   HUGGINGFACE_HUB_CACHE: {os.environ.get('HUGGINGFACE_HUB_CACHE')}")
    
    # Check directory structure
    base_dir = './data/model_cache'
    directories = [
        base_dir,
        os.path.join(base_dir, 'hub'),
        os.path.join(base_dir, 'datasets'),
        os.path.join(base_dir, 'assets'),
        os.path.join(base_dir, 'local_models')
    ]
    
    print(f"\n📁 Directory Structure:")
    for directory in directories:
        exists = os.path.exists(directory)
        print(f"   {directory}: {'✅ Exists' if exists else '❌ Missing'}")
        if not exists:
            os.makedirs(directory, exist_ok=True)
            print(f"      📁 Created directory: {directory}")
    
    # Check models in local directory
    hub_dir = os.path.join(base_dir, 'hub')
    if os.path.exists(hub_dir):
        models = [d for d in os.listdir(hub_dir) if d.startswith('models--')]
        print(f"\n📦 Models in local directory: {len(models)}")
        for model in models[:5]:  # Show first 5
            print(f"   - {model}")
        if len(models) > 5:
            print(f"   ... and {len(models) - 5} more")
    
    print(f"\n✅ Local setup verification complete!")


async def main():
    """Main function."""
    print("🚀 Starting model migration and setup process...")
    
    # Verify local setup
    await verify_local_setup()
    
    # Migrate and upload models
    await migrate_and_upload_models()
    
    print("\n🎉 Migration process complete!")
    print("\n📋 Next steps:")
    print("   1. Restart your application to use the new local cache")
    print("   2. All new models will be downloaded to ./data/model_cache")
    print("   3. Models will be automatically uploaded to MinIO")


if __name__ == "__main__":
    asyncio.run(main())
